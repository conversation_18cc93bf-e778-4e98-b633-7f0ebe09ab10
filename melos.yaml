name: ziichat_flutter

packages:
  - apps/**
  - packages/**

scripts: # Group: Build commands

  # Release Android
  release_android_live:
    description: Release Android Live.
    steps:
      - echo "Starting Android build Live..."
      - melos exec --scope="mobile" -- flutter build appbundle --flavor live --dart-define=FLAVOR=live --verbose
      - echo "Creating directory for Android release..."
      - mkdir -p ~/Desktop/Release/live/
      - echo "Copying Android App Bundle to Desktop..."
      - cp -f apps/mobile/build/app/outputs/bundle/liveRelease/app-live-release.aab ~/Desktop/Release/live/
      - echo "Zipping debug symbols..."
      - cd apps/mobile/build/app/intermediates/merged_native_libs/liveRelease/mergeLiveReleaseNativeLibs/out/lib && zip -r ~/Desktop/Release/live/symbols.zip arm64-v8a armeabi-v7a x86 x86_64
      - echo "Android release process completed."
      # Release Android Beta
  release_android_beta:
    description: Release Android Beta.
    steps:
      - echo "Starting Android build..."
      - melos exec --scope="mobile" -- flutter build appbundle --flavor beta --dart-define=FLAVOR=beta --verbose
      - echo "Creating directory for Android release..."
      - mkdir -p ~/Desktop/Release/beta/
      - echo "Copying Android App Bundle to Desktop..."
      - cp -f apps/mobile/build/app/outputs/bundle/betaRelease/app-beta-release.aab ~/Desktop/Release/beta/
      - echo "Zipping debug symbols..."
      - cd apps/mobile/build/app/intermediates/merged_native_libs/betaRelease/mergeBetaReleaseNativeLibs/out/lib && zip -r ~/Desktop/Release/beta/symbols.zip arm64-v8a armeabi-v7a x86 x86_64
      - echo "Android release process completed."


  # Release iOS
  release_ios_f:
    description: Release iOS.
    steps:
      - echo "Starting iOS build..."
      - melos exec --scope="mobile" -- flutter build ipa --flavor flutter --dart-define=FLAVOR=beta --verbose
      - echo "Creating directory for iOS release..."
      - mkdir -p ~/Desktop/Release/flutter/
      - echo "Copying iOS IPA to Desktop..."
      - cp -f apps/mobile/build/ios/ipa/*.ipa ~/Desktop/Release/flutter/
      - echo "Zipping iOS dSYM files..."
      - zip -r ~/Desktop/Release/flutter/dSYMs.zip build/ios/archive/ZiiChat.xcarchive/dSYMs/*
      - echo "iOS release process completed."

  # Release iOS
  release_ios_beta:
    description: Release iOS.
    steps:
      - echo "Starting iOS build..."
      - melos exec --scope="mobile" -- flutter build ipa --flavor beta --dart-define=FLAVOR=beta --verbose
      - echo "Creating directory for iOS release..."
      - mkdir -p ~/Desktop/Release/beta
      - echo "Copying iOS IPA to Desktop..."
      - cp -f apps/mobile/build/ios/ipa/*.ipa ~/Desktop/Release/beta
      - echo "Zipping iOS dSYM files..."
      - zip -r ~/Desktop/Release/beta/dSYMs.zip build/ios/archive/ZiiChat.xcarchive/dSYMs/*
      - echo "iOS release process completed."


  release_ios_live:
    description: Release iOS.
    steps:
      - echo "Starting iOS build..."
      - melos exec --scope="mobile" -- flutter build ipa --flavor live --dart-define=FLAVOR=live --verbose
      - echo "Creating directory for iOS release..."
      - mkdir -p ~/Desktop/Release/live
      - echo "Copying iOS IPA to Desktop..."
      - cp -f apps/mobile/build/ios/ipa/*.ipa ~/Desktop/Release/live/
      - echo "Zipping iOS dSYM files..."
      - zip -r ~/Desktop/Release/live/dSYMs.zip build/ios/archive/ZiiChat.xcarchive/dSYMs/*
      - echo "iOS release process completed."

  upload_dsym_live:
    description: Upload dSYM iOS live lên Firebase Crashlytics.
    steps:
      - echo "Uploading dSYM to Firebase Crashlytics..."
      - bash -c '
        ZIP_PATH=~/Desktop/Release/live/dSYMs.zip;
        UPLOAD_SYMBOLS_PATH=apps/mobile/ios/Pods/FirebaseCrashlytics/upload-symbols;
        GOOGLE_SERVICE_INFO=apps/mobile/ios/Runner/GoogleService-Info.plist;
        if [ ! -f "$ZIP_PATH" ]; then echo "❌ dSYM zip not found at $ZIP_PATH"; exit 1; fi;
        if [ ! -f "$UPLOAD_SYMBOLS_PATH" ]; then echo "❌ upload-symbols not found at $UPLOAD_SYMBOLS_PATH"; exit 1; fi;
        if [ ! -f "$GOOGLE_SERVICE_INFO" ]; then echo "❌ GoogleService-Info.plist not found at $GOOGLE_SERVICE_INFO"; exit 1; fi;
        TMP_DIR=$(mktemp -d);
        unzip -q "$ZIP_PATH" -d "$TMP_DIR";
        $UPLOAD_SYMBOLS_PATH -gsp "$GOOGLE_SERVICE_INFO" -p ios "$TMP_DIR";
        rm -rf "$TMP_DIR";
        echo "✅ Uploaded dSYMs to Firebase"
        '
  # Release All
  release_all:
    description: Release both Android and iOS.
    run: |
      echo "Starting parallel release for Android and iOS..."
      parallel ::: \
        "melos run release_android -- --verbose" \
        "melos run release_ios -- --verbose"
      echo "Parallel release process completed."

  # Build all modules
  build_all:
    run: melos exec --depends-on="build_runner" -- flutter packages pub run build_runner build
    description: Build_runner build all modules.

  # Build individual modules
  build_core:
    run: melos exec --scope="app_core" -- flutter packages pub run build_runner build --delete-conflicting-outputs
    description: Build_runner build for "app_core" module.

  build_chat:
    run: melos exec --scope="chat" -- flutter packages pub run build_runner build --delete-conflicting-outputs
    description: Build_runner build for "chat" module.

  build_mobile:
    run: melos exec --scope="mobile" -- flutter packages pub run build_runner build --delete-conflicting-outputs
    description: Build_runner build for "mobile" module.

  build_auth:
    run: melos exec --scope="auth" -- flutter packages pub run build_runner build --delete-conflicting-outputs
    description: Build_runner build for "auth" module.

  build_shared:
    run: melos exec --scope="shared" -- flutter packages pub run build_runner build --delete-conflicting-outputs
    description: Build_runner build for "shared" module.

  build_user_manager:
    run: melos exec --scope="user_manager" -- flutter packages pub run build_runner build --delete-conflicting-outputs
    description: Build_runner build for "user_manager" module.

  build_search:
    run: melos exec --scope="search" -- flutter packages pub run build_runner build --delete-conflicting-outputs
    description: Build_runner build for "search" module.

  build_sticker:
    run: melos exec --scope="sticker" -- flutter packages pub run build_runner build --delete-conflicting-outputs
    description: Build_runner build for "sticker" module.

  build_talk_and_translate:
    run: melos exec --scope="talk_translate" -- flutter packages pub run build_runner build --delete-conflicting-outputs
    description: Build_runner build for "talk_translate" module.

  build_share_to:
    run: melos exec --scope="share_to" -- flutter packages pub run build_runner build --delete-conflicting-outputs
    description: Build_runner build for "share_to" module.

  build_upload_manager:
    run: melos exec --scope="upload_manager" -- flutter packages pub run build_runner build --delete-conflicting-outputs
    description: Build_runner build for "upload_manager" module.

  build_call:
    run: melos exec --scope="call" -- flutter packages pub run build_runner build --delete-conflicting-outputs
    description: Build_runner build for "call" module.

  ### Group: Watch commands ###

  # Watch all modules
  watch_all:
    run: melos exec --depends-on="build_runner" -- flutter packages pub run build_runner watch
    description: Build_runner watch all modules.

  # Watch individual modules
  watch_core:
    run: melos exec --scope="app_core" -- flutter packages pub run build_runner watch
    description: Build_runner watch for "app_core" module.

  watch_chat:
    run: melos exec --scope="chat" -- flutter packages pub run build_runner watch
    description: Build_runner watch for "chat" module.

  watch_mobile:
    run: melos exec --scope="mobile" -- flutter packages pub run build_runner watch
    description: watch "mobile" module.

  watch_auth:
    run: melos exec --scope="auth" -- flutter packages pub run build_runner watch
    description: Build_runner watch for "auth" module.

  watch_shared:
    run: melos exec --scope="shared" -- flutter packages pub run build_runner watch
    description: Build_runner watch for "shared" module.

  watch_user_manager:
    run: melos exec --scope="user_manager" -- flutter packages pub run build_runner watch
    description: Build_runner watch for "user_manager" module.

  watch_upload_manager:
    run: melos exec --scope="upload_manager" -- flutter packages pub run build_runner watch
    description: Build_runner watch for "upload_manager" module.

  watch_search:
    run: melos exec --scope="search" -- flutter packages pub run build_runner watch
    description: Build_runner watch for "search" module.

  watch_sticker:
    run: melos exec --scope="sticker" -- flutter packages pub run build_runner watch
    description: Build_runner watch for "sticker" module.

  watch_talk_and_translate:
    run: melos exec --scope="talk_translate" -- flutter packages pub run build_runner watch
    description: Build_runner watch for "talk_translate" module.

  watch_share_to:
    run: melos exec --scope="share_to" -- flutter packages pub run build_runner watch
    description: Build_runner watch for "share_to" module.

  watch_call:
    run: melos exec --scope="call" -- flutter packages pub run build_runner watch
    description: Build_runner watch for "call" module.
  ### Group: Run commands ###

  run_android:
    run: melos exec --scope="mobile" -- flutter run --flavor sandbox
    description: Run the app on Android.

  ### Group: Force commands ###

  force_build_all:
    run: melos exec --depends-on="build_runner" -- flutter packages pub run build_runner build --delete-conflicting-outputs
    description: Force build_runner build for all modules.

  force_update_all:
    run: melos exec --flutter --concurrency=3 -- "flutter pub upgrade --major-versions"
    description: Force update for all modules.

  update_app_module:
    run: melos exec --flutter --concurrency=3 -- "flutter pub upgrade localization_client ziichat_ui filestore_sdk download_manager webauthn, passkeys, share_to_sdk, ziichat_video_player,apns_notification,fcm_notification"
    description: Force update for app modules.

  update_ui_module:
    run: melos exec --flutter --concurrency=3 -- "flutter pub upgrade ziichat_ui"
    description: Force update for app modules.

  update_filestore_sdk_module:
    run: melos exec --flutter --concurrency=3 -- "flutter pub upgrade filestore_sdk"
    description: Force update for filestore_sdk modules.

  update_translate_module:
    run: melos exec --flutter --concurrency=3 -- "flutter pub upgrade localization_client"
    description: Force update for app modules.

  ### Group: Sequentially build commands ###

  seq_build_all:
    run: |
      melos exec --scope="app_core" -- flutter packages pub run build_runner build &&
      melos exec --scope="chat" -- flutter packages pub run build_runner build &&
      melos exec --scope="mobile" -- flutter packages pub run build_runner build &&
      melos exec --scope="auth" -- flutter packages pub run build_runner build &&
      melos exec --scope="shared" -- flutter packages pub run build_runner build &&
      melos exec --scope="user_manager" -- flutter packages pub run build_runner build &&
      melos exec --scope="upload_manager" -- flutter packages pub run build_runner build &&
      melos exec --scope="search" -- flutter packages pub run build_runner build &&
      melos exec --scope="sticker" -- flutter packages pub run build_runner build &&
      melos exec --scope="talk_translate" -- flutter packages pub run build_runner build &&
      melos exec --scope="share_to" -- flutter packages pub run build_runner build &&
      melos exec --scope="call" -- flutter packages pub run build_runner build
    description: Sequentially build_runner build for all modules without deleting conflicting outputs.

  seq_force_build_all:
    run: |
      melos exec --scope="app_core" -- flutter packages pub run build_runner build --delete-conflicting-outputs &&
      melos exec --scope="chat" -- flutter packages pub run build_runner build --delete-conflicting-outputs &&
      melos exec --scope="mobile" -- flutter packages pub run build_runner build --delete-conflicting-outputs &&
      melos exec --scope="auth" -- flutter packages pub run build_runner build --delete-conflicting-outputs &&
      melos exec --scope="shared" -- flutter packages pub run build_runner build --delete-conflicting-outputs &&
      melos exec --scope="user_manager" -- flutter packages pub run build_runner build --delete-conflicting-outputs &&
      melos exec --scope="upload_manager" -- flutter packages pub run build_runner build --delete-conflicting-outputs &&
      melos exec --scope="search" -- flutter packages pub run build_runner build --delete-conflicting-outputs &&
      melos exec --scope="sticker" -- flutter packages pub run build_runner build --delete-conflicting-outputs &&
      melos exec --scope="talk_translate" -- flutter packages pub run build_runner build --delete-conflicting-outputs &&
      melos exec --scope="share_to" -- flutter packages pub run build_runner build --delete-conflicting-outputs &&
      melos exec --scope="call" -- flutter packages pub run build_runner build --delete-conflicting-outputs
    description: Sequentially force build_runner build for all modules.

    ### Group: Parallel build commands ###
  parallel_build_all:
    description: Parallel build_runner build for all modules without deleting conflicting outputs.
    run: |
      parallel -k ::: \
        "melos exec --scope='app_core' -- flutter packages pub run build_runner build" \
        "melos exec --scope='chat' -- flutter packages pub run build_runner build" \
        "melos exec --scope='mobile' -- flutter packages pub run build_runner build" \
        "melos exec --scope='auth' -- flutter packages pub run build_runner build" \
        "melos exec --scope='shared' -- flutter packages pub run build_runner build" \
        "melos exec --scope='user_manager' -- flutter packages pub run build_runner build" \
        "melos exec --scope='upload_manager' -- flutter packages pub run build_runner build" \
        "melos exec --scope='search' -- flutter packages pub run build_runner build" \
        "melos exec --scope='sticker' -- flutter packages pub run build_runner build" \
        "melos exec --scope='talk_translate' -- flutter packages pub run build_runner build" \
        "melos exec --scope='share_to' -- flutter packages pub run build_runner build" \
        "melos exec --scope='call' -- flutter packages pub run build_runner build"

  parallel_force_build_all:
    description: Parallel force build_runner build for all modules.
    run: |
      parallel -k ::: \
        "melos exec --scope='app_core' -- flutter packages pub run build_runner build --delete-conflicting-outputs" \
        "melos exec --scope='chat' -- flutter packages pub run build_runner build --delete-conflicting-outputs" \
        "melos exec --scope='mobile' -- flutter packages pub run build_runner build --delete-conflicting-outputs" \
        "melos exec --scope='auth' -- flutter packages pub run build_runner build --delete-conflicting-outputs" \
        "melos exec --scope='shared' -- flutter packages pub run build_runner build --delete-conflicting-outputs" \
        "melos exec --scope='user_manager' -- flutter packages pub run build_runner build --delete-conflicting-outputs" \
        "melos exec --scope='upload_manager' -- flutter packages pub run build_runner build --delete-conflicting-outputs" \
        "melos exec --scope='search' -- flutter packages pub run build_runner build --delete-conflicting-outputs" \
        "melos exec --scope='sticker' -- flutter packages pub run build_runner build --delete-conflicting-outputs" \
        "melos exec --scope='talk_translate' -- flutter packages pub run build_runner build --delete-conflicting-outputs" \
        "melos exec --scope='share_to' -- flutter packages pub run build_runner build --delete-conflicting-outputs" \
        "melos exec --scope='call' -- flutter packages pub run build_runner build --delete-conflicting-outputs"
  ### Group: Clean commands ###

  clean:
    run: melos exec --flutter --concurrency=3 -- "flutter clean"
    description: Clean cache for all modules.

  remove_all_pubspec_lock:
    run: |
      # This script removes all pubspec.lock files within the project
      echo "Removing all pubspec.lock files..."

      # Use 'find' to locate all 'pubspec.lock' files and remove them
      find . -name "pubspec.lock" -exec rm -f {} \;

      # Print a confirmation message after removal
      echo "All pubspec.lock files have been successfully removed!"

  remove_all_freezed_files:
    run: |
      # This script removes all *.freezed.dart files within the project
      echo "Removing all *.freezed.dart files..."

      # Use 'find' to locate all '*.freezed.dart' files and remove them
      find . -name "*.freezed.dart" -exec rm -f {} \;

      # Print a confirmation message after removal
      echo "All *.freezed.dart files have been successfully removed!"

  gen:run-config:
    run: dart tools/gen_run_config.dart
    description: "Auto gen Run Configuration cho IntelliJ and VS Code"