part of 'create_channel_bloc.dart';

@freezed
sealed class CreateChannelState extends BaseBlocState
    with _$CreateChannelState {
  const CreateChannelState._();

  factory CreateChannelState.initial() = CreateChannelStateInitial;

  factory CreateChannelState.channelNameChanged({
    @Default('') String name,
  }) = CreateChannelStateChannelNameChanged;

  factory CreateChannelState.channelAvatarChanged({
    @Default(null) Uint8List? avatar,
  }) = CreateChannelStateChannelAvatarChanged;

  factory CreateChannelState.userIDsInvitedUpdated({
    @Default([]) List<String> userIDsInvited,
  }) = CreateChannelStateUserIDsInvitedUpdated;

  factory CreateChannelState.searchingStatusChanged({
    required bool isSearching,
  }) = CreateChannelStateSearchingStatusChanged;

  factory CreateChannelState.searchTextChanged({
    required String keyword,
  }) = CreateChannelStateSearchTextChanged;
}

extension CreateChannelStateX on CreateChannelState {
  T when<T>({
    required T Function() initial,
    required T Function(String name) channelNameChanged,
    required T Function(Uint8List? avatar) channelAvatarChanged,
    required T Function(List<String> userIDsInvited) userIDsInvitedUpdated,
    required T Function(bool isSearching) searchingStatusChanged,
    required T Function(String keyword) searchTextChanged,
  }) {
    final state = this;

    if (state is CreateChannelStateInitial) {
      return initial();
    }
    if (state is CreateChannelStateChannelNameChanged) {
      return channelNameChanged(state.name);
    }
    if (state is CreateChannelStateChannelAvatarChanged) {
      return channelAvatarChanged(state.avatar);
    }
    if (state is CreateChannelStateUserIDsInvitedUpdated) {
      return userIDsInvitedUpdated(state.userIDsInvited);
    }
    if (state is CreateChannelStateSearchingStatusChanged) {
      return searchingStatusChanged(state.isSearching);
    }
    if (state is CreateChannelStateSearchTextChanged) {
      return searchTextChanged(state.keyword);
    }

    throw StateError('Unhandled CreateChannelState: $state');
  }

  T maybeWhen<T>({
    T Function()? initial,
    T Function(String name)? channelNameChanged,
    T Function(Uint8List? avatar)? channelAvatarChanged,
    T Function(List<String> userIDsInvited)? userIDsInvitedUpdated,
    T Function(bool isSearching)? searchingStatusChanged,
    T Function(String keyword)? searchTextChanged,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is CreateChannelStateInitial && initial != null) {
      return initial();
    }
    if (state is CreateChannelStateChannelNameChanged &&
        channelNameChanged != null) {
      return channelNameChanged(state.name);
    }
    if (state is CreateChannelStateChannelAvatarChanged &&
        channelAvatarChanged != null) {
      return channelAvatarChanged(state.avatar);
    }
    if (state is CreateChannelStateUserIDsInvitedUpdated &&
        userIDsInvitedUpdated != null) {
      return userIDsInvitedUpdated(state.userIDsInvited);
    }
    if (state is CreateChannelStateSearchingStatusChanged &&
        searchingStatusChanged != null) {
      return searchingStatusChanged(state.isSearching);
    }
    if (state is CreateChannelStateSearchTextChanged &&
        searchTextChanged != null) {
      return searchTextChanged(state.keyword);
    }

    return orElse();
  }
}
