part of 'create_channel_bloc.dart';

abstract class Create<PERSON>hannelEvent extends BaseBloc<PERSON>vent {
  const CreateChannelEvent();
}

class InitiateCreateChannelEvent extends CreateChannelEvent {
  const InitiateCreateChannelEvent();
}

class CreateNewChannelEvent extends CreateChannelEvent {
  final String name;
  final String? avatarPath;
  final List<String> userIDsInvited;
  final void Function(Channel)? onCreated;
  final void Function(dynamic)? onError;

  const CreateNewChannelEvent({
    required this.name,
    this.userIDsInvited = const [],
    this.onCreated,
    this.onError,
    this.avatarPath,
  });
}

class ChangeNameChannelEvent extends CreateChannelEvent {
  final String value;

  const ChangeNameChannelEvent(this.value);
}

class ChangeAvatarChannelEvent extends CreateChannelEvent {
  final Uint8List? value;

  const ChangeAvatarChannelEvent(this.value);
}

class ChangeSearchingStatusEvent extends CreateChan<PERSON><PERSON>vent {
  final bool isSearching;

  const ChangeSearchingStatusEvent(this.isSearching);
}

class ChangeSearchTextEvent extends CreateChan<PERSON>Event {
  final String keyword;

  const ChangeSearchTextEvent(this.keyword);
}

class UpdateUserIDsInvitedUserEvent extends CreateChannelEvent {
  final List<String> userIDsInvited;

  const UpdateUserIDsInvitedUserEvent({
    this.userIDsInvited = const [],
  });
}

class UpdateChannelAvatarEvent extends CreateChannelEvent {
  final UploadFile avatar;
  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final void Function(String)? onAvatarChanged;

  const UpdateChannelAvatarEvent({
    required this.avatar,
    this.workspaceId,
    this.channelId,
    this.userId,
    this.onAvatarChanged,
  });
}

class OnErrorEvent extends CreateChannelEvent {
  final String errorMessage;

  const OnErrorEvent({required this.errorMessage});
}

class OnAvatarUpdatedEvent extends CreateChannelEvent {
  final String? avatarPath;

  const OnAvatarUpdatedEvent({this.avatarPath = ""});
}
