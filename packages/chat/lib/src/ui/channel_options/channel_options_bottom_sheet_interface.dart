import '../../data/repositories/database/enums/channel_type.dart';

abstract class ChannelOptionsBottomSheetInterface {
  ChannelOptionsBottomSheetInterface({
    required this.channelName,
    this.workspaceId,
    this.channelId,
    this.userId,
    this.avatarUrl,
    this.channelType = ChannelTypeEnum.BROADCAST,
  });

  final String? avatarUrl;
  final String channelName;
  final ChannelTypeEnum? channelType;
  final String? userId;
  final String? channelId;
  final String? workspaceId;

  bool get isChannel => channelType != ChannelTypeEnum.DM;

  void onTapDeleteChat();

  void onTapInviteToChannel();

  void onChangePinChannel(bool value);

  void onChangeMuteChannel(bool value);

  void onTapReportUser({String? userId, String? name});
}
