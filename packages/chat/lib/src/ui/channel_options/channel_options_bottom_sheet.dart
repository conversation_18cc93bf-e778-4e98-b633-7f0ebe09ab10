import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../chat.dart';
import 'channel_options_bottom_sheet_interface.dart';

class ChannelOptionsBottomSheet extends StatefulWidget {
  const ChannelOptionsBottomSheet({
    required this.interface,
    required this.isMuteChannel,
    super.key,
  });

  final ChannelOptionsBottomSheetInterface interface;
  final bool isMuteChannel;

  @override
  State<ChannelOptionsBottomSheet> createState() =>
      _ChannelOptionsBottomSheetState();
}

class _ChannelOptionsBottomSheetState extends State<ChannelOptionsBottomSheet> {
  bool _isPinChannel = true;

  // bool _isMuteChannel = true;
  late ChannelsBloc bloc;
  Map<String, ChannelPrivateData> _mapChannelPrivateData = {};
  Map<String, ChatUser> _mapBlockUser = {};
  late ValueNotifier<bool> _isBlockUserReport = ValueNotifier(false);

  void _handleChannelPrivateState(ChannelPrivateDataState state) {
    state.when(
      initial: () {},
      listChannelPrivateData:
          (List<ChannelPrivateData> listChannelPrivateData) {
        _mapChannelPrivateData = {};
        listChannelPrivateData.forEach((item) {
          _mapChannelPrivateData.putIfAbsent(item.channelId, () => item);
        });
        _isPinChannel =
            _mapChannelPrivateData[widget.interface.channelId]?.pinned ?? false;
      },
    );
  }

  void _blocChannelPrivateListener(
    BuildContext context,
    ChannelPrivateDataState state,
  ) {
    state.when(
      initial: () {},
      listChannelPrivateData:
          (List<ChannelPrivateData> listChannelPrivateData) {
        _mapChannelPrivateData = {};
        listChannelPrivateData.forEach((item) {
          _mapChannelPrivateData.putIfAbsent(item.channelId, () => item);
        });
        _isPinChannel =
            _mapChannelPrivateData[widget.interface.channelId]?.pinned ?? false;
      },
    );
  }

  void _blocUserReportListener(
    BuildContext context,
    UserReportState state,
  ) {
    state.maybeWhen(
      initial: () {},
      showProcessDialog: () {},
      updateProcessDialog: (response, userId, name, isBlocked) {
        if (response) {
          showReportThankYou(userId, name!, isBlocked);
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              popShowClearMessages();
            },
          );
        }
      },
      orElse: () {},
    );
  }

  void showReportThankYou(String? userId, String name, bool? isBlocked) {
    _isBlockUserReport.value = isBlocked != null
        ? isBlocked
        : _mapBlockUser[userId] == null
            ? false
            : true;
    ui.BottomSheetUtil.showThankYouDmChannelBottomSheet(
      context: context,
      enableDrag: false,
      isDismissible: false,
      onClickClose: () {
        popShowClearMessages();
      },
      onClickBlock: () {
        ui.ActionSheetUtil.showBlockUserActionSheet(
          context,
          username: name,
          onBlock: () {
            context
                .read<BlockUserBloc>()
                .add(OnBlockUserEvent(userId: userId!, popOnlyMine: true));
          },
          onCancel: () {
            Navigator.pop(context);
          },
        );
      },
      onClickCommunityStandard: () async {
        OpenLauncherUrl.onOpenLauncherURL(
          OpenLauncherUrl.pathCommunityStandards,
        );
      },
      blockedUsername: name,
      isBlocked: _isBlockUserReport,
    );
  }

  void _blocBlockUserListener(BuildContext context, BlockUserState state) {
    state.maybeWhen(
      loadListBlockUser: (listBlockUser) {
        _mapBlockUser = {};
        listBlockUser?.forEach((item) {
          _mapBlockUser.putIfAbsent(
            item.userId,
            () => ChatUser.fromJson(item.toJson()),
          );
        });
      },
      updateProcessDialog: (response, bool? popOnlyMine) {
        if (response) {
          _isBlockUserReport.value = true;
          if (Navigator.canPop(context)) {
            popOnlyMine == true
                ? Navigator.pop(context)
                : popShowClearMessages();
          }
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              Navigator.pop(context);
            },
          );
        }
      },
      orElse: () {},
    );
  }

  void _blocSettingNotification(
    BuildContext context,
    SettingNotificationState state,
  ) {
    state.maybeWhen(
      subscribeChannel: (response) {
        if (response == true) {
          popToHome();
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              popToHome();
            },
          );
        }
      },
      unsubscribeChannel: (response) {
        if (response == true) {
          popToHome();
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              popToHome();
            },
          );
        }
      },
      orElse: () {},
    );
  }

  void popToHome() {
    AppEventBus.publish(
      ReplacePopToHomeEvent(),
    );
  }

  @override
  Widget build(BuildContext context) {
    bloc = context.read<ChannelsBloc>();
    final channelPrivateDataState =
        context.watch<ChannelPrivateDataBloc>().state;
    _handleChannelPrivateState(channelPrivateDataState);
    return MultiBlocListener(
      listeners: [
        BlocListener<ChannelPrivateDataBloc, ChannelPrivateDataState>(
          listenWhen: (prev, state) => prev != state,
          listener: _blocChannelPrivateListener,
        ),
        BlocListener<BlockUserBloc, BlockUserState>(
          listenWhen: (prev, state) => prev != state,
          listener: _blocBlockUserListener,
        ),
        BlocListener<UserReportBloc, UserReportState>(
          listenWhen: (prev, state) => prev != state,
          listener: _blocUserReportListener,
        ),
        BlocListener<SettingNotificationBloc, SettingNotificationState>(
          listenWhen: (prev, state) => prev != state,
          listener: _blocSettingNotification,
        ),
      ],
      child: ui.BottomSheetPinChatWidget(
        context: context,
        bottomSheetPinChat: ui.BottomSheetPinChat(
          avatarUrl: widget.interface.avatarUrl,
          channelName: widget.interface.channelName,
          isChannel: widget.interface.isChannel,
          isMuteChannel: widget.isMuteChannel,
          isPinChannel: _isPinChannel,
        ),
        onPinChatClicked: onPinChatClicked,
        onUnpinChatClicked: onUnpinChatClicked,
        onMuteNotificationClicked: onMuteNotificationClicked,
        onUnMuteNotificationClicked: onUnMuteNotificationClicked,
        onReportUserClicked: onReportUserClicked,
        onAddMemberClicked: onInviteToChannelClicked,
        onClearMessagesClicked: onClearMessagesClicked,
      ),
    );
  }

  void onDeleteChatClicked(ui.BottomSheetPinChat bottomSheetPinChat) {
    widget.interface.onTapDeleteChat();
  }

  void onInviteToChannelClicked(ui.BottomSheetPinChat bottomSheetPinChat) {
    widget.interface.onTapInviteToChannel();
  }

  void onMuteNotificationClicked(ui.BottomSheetPinChat bottomSheetPinChat) {
    popToHome();
    this.context.read<SettingNotificationBloc>().add(
          OnUnsubscribeChannelEvent(
            workspaceId: widget.interface.workspaceId,
            channelId: widget.interface.channelId,
          ),
        );
    widget.interface.onChangeMuteChannel(widget.isMuteChannel);
  }

  void onUnMuteNotificationClicked(ui.BottomSheetPinChat bottomSheetPinChat) {
    popToHome();
    this.context.read<SettingNotificationBloc>().add(
          OnSubscribeChannelEvent(
            workspaceId: widget.interface.workspaceId,
            channelId: widget.interface.channelId,
          ),
        );
    widget.interface.onChangeMuteChannel(widget.isMuteChannel);
  }

  void onPinChatClicked(ui.BottomSheetPinChat bottomSheetPinChat) {
    AppEventBus.publish(
      AddPinChannelEvent(channelId: widget.interface.channelId!, isPin: true),
    );
  }

  void onReportUserClicked(ui.BottomSheetPinChat bottomSheetPinChat) {
    widget.interface.onTapReportUser(
      userId: widget.interface.userId,
      name: bottomSheetPinChat.channelName,
    );
  }

  void onUnpinChatClicked(ui.BottomSheetPinChat bottomSheetPinChat) {
    AppEventBus.publish(
      UnPinChannelEvent(channelId: widget.interface.channelId!),
    );
  }

  void popShowClearMessages() {
    AppEventBus.publish(
      PopToHomeEvent(),
    );
  }

  void onClearMessagesClicked(ui.BottomSheetPinChat bottomSheetPinChat) {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    }
    if (widget.interface.isChannel) {
      ui.ActionSheetUtil.showClearMessagesActionSheet(
        context,
        onRemove: () {
          popShowClearMessages();
          bloc.add(
            CallClearMessageAllForEveryoneChannelEvent(
              workspaceId: widget.interface.workspaceId,
              channelId: widget.interface.channelId,
            ),
          );
        },
        onCancel: () {
          popShowClearMessages();
        },
      );
    } else {
      ui.ActionSheetUtil.showClearDmMessagesActionSheet(
        context,
        friendName: widget.interface.channelName,
        onRemoveForMe: () {
          popShowClearMessages();
          bloc.add(RefreshClearMessageEvent());
          bloc.add(
            CallClearMessageAllForMeChannelEvent(
              workspaceId: widget.interface.workspaceId,
              channelId: widget.interface.channelId,
              userId: widget.interface.userId,
            ),
          );
        },
        onRemoveForMeAndFriend: () {
          popShowClearMessages();
          bloc.add(RefreshClearMessageEvent());
          bloc.add(
            CallClearMessageAllForEveryoneChannelEvent(
              workspaceId: widget.interface.workspaceId,
              channelId: widget.interface.channelId,
              userId: widget.interface.userId,
            ),
          );
        },
        onCancel: () {
          popShowClearMessages();
        },
      );
    }
  }
}
