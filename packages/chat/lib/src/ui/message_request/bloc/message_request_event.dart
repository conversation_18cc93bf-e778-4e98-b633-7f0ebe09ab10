part of 'message_request_bloc.dart';

sealed class MessageRequestEvent extends BaseBlocEvent {
  const MessageRequestEvent();
}

@freezed
sealed class InitMessageRequestEvent extends MessageRequestEvent
    with _$InitMessageRequestEvent {
  const InitMessageRequestEvent._();
  factory InitMessageRequestEvent() = _InitMessageRequest;
}

@freezed
sealed class UpdateListMessageRequestEvent extends MessageRequestEvent
    with _$UpdateListMessageRequestEvent {
  const UpdateListMessageRequestEvent._();
  factory UpdateListMessageRequestEvent({
    required List<Channel> channels,
  }) = _UpdateListMessageRequest;
}

@freezed
sealed class AcceptMessageRequestEvent extends MessageRequestEvent
    with _$AcceptMessageRequestEvent {
  const AcceptMessageRequestEvent._();
  factory AcceptMessageRequestEvent({
    required String userId,
  }) = _AcceptMessageRequestEvent;
}

@freezed
sealed class RejectMessageRequestEvent extends MessageRequestEvent
    with _$RejectMessageRequestEvent {
  const RejectMessageRequestEvent._();
  factory RejectMessageRequestEvent({
    required String userId,
  }) = _RejectMessageRequestEvent;
}

@freezed
sealed class UpdatedClosedWarningStatusEvent extends MessageRequestEvent
    with _$UpdatedClosedWarningStatusEvent {
  const UpdatedClosedWarningStatusEvent._();
  factory UpdatedClosedWarningStatusEvent({
    required bool closedWarningStatus,
  }) = _UpdatedClosedWarningStatusEvent;
}

@freezed
sealed class LoadMessageRequestEvent extends MessageRequestEvent
    with _$LoadMessageRequestEvent {
  const LoadMessageRequestEvent._();
  factory LoadMessageRequestEvent() = _LoadMessageRequest;
}

@freezed
sealed class UpdateTranslatedMessagesEvent extends MessageRequestEvent
    with _$UpdateTranslatedMessagesEvent {
  const UpdateTranslatedMessagesEvent._();
  factory UpdateTranslatedMessagesEvent({
    required Map<String, String?> translatedMessages,
  }) = _UpdateTranslatedMessagesEvent;
}
