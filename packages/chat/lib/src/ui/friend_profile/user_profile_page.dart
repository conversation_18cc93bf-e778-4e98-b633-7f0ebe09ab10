import 'dart:async';
import 'dart:io';

import 'package:app_core/core.dart' hide Config;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../chat.dart'
    hide
        ClearMessageAllForMeChannelEvent,
        ClearMessageAllForEveryoneChannelEvent;
import '../../common/di/di.dart';
import '../../data/repositories/database/entities/channel_local_metadata.dart';
import '../../data/repositories/database/enums/chat_friend_status.dart';
import '../../data/repositories/database/enums/dm_status.dart';
import '../../data/repositories/database/enums/user_badge_enum.dart';
import '../../domain/usecase/chat_user/get_me_use_case.dart';
import '../create_channel/create_channel_bottom_sheet.dart';
import '../translate_to/bloc/translate_to_bloc.dart';
import 'bloc/user_profile_bloc.dart';
import 'user_profile_handler.dart';

class UserProfilePage extends StatefulWidget {
  const UserProfilePage({
    this.workspaceId,
    this.channelId,
    this.userId,
    this.username,
    this.callVisitedProfile,
    this.user,
    this.onBack,
    this.goToDMChannel,
    this.onGoToViewImagePage,
    super.key,
  });

  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final String? username;
  final bool? callVisitedProfile;
  final Map<String, dynamic>? user;
  final void Function()? onBack;
  final Function(String? userId)? goToDMChannel;
  final void Function(String coverPath)? onGoToViewImagePage;

  @override
  State<UserProfilePage> createState() => _UserProfilePageState();
}

class _UserProfilePageState
    extends BasePageState<UserProfilePage, UserProfileBloc>
    implements ui.FriendProfileInterface {
  ChatUser? _user;
  String? _workspaceId;
  String? _channelId;
  bool _hideClearMessage = true;
  late AppLocalizations appLocalizations;
  List<UserPrivateData> _listUserPrivateData = [];
  Map<String, ChatUser> _mapBlockUser = {};
  late ValueNotifier<String> name;
  late ValueNotifier<bool> _isBlockUser;
  late StreamSubscription? _listenEventSubscription;
  late SettingNotificationBloc _settingNotificationBloc;
  late ValueNotifier<bool> _displayNotificationOption = ValueNotifier(true);
  late VisitedProfileBloc _visitedProfileBloc;

  late final UserPrivateDataBloc _userPrivateDataBloc;
  late final UserProfileBloc _userProfileBloc;
  late final BlockUserBloc _blockUserBloc;
  late final UserReportBloc _userReportBloc;
  int badgeEnum = 0;
  ui.UserBadgeType? userBadgeType;
  late final TranslateToBloc _translateToBloc;
  late final TranslateToHandler _translateToHandler;
  bool _translateToBlocInitiate = false;
  ChannelLocalMetadata? _metadata;
  late final StreamSubscription? _streamUnfriend;
  late ChatUser? meInfo;
  bool _isShowUnfriendBottomSheet = false;

  ValueNotifier<ui.ProcessStatus> processStatus =
      ValueNotifier(ui.ProcessStatus.loading);
  ValueNotifier<String> processContent = ValueNotifier("");
  late ValueNotifier<bool> _isHiddenPoke = ValueNotifier(true);
  late ValueNotifier<bool> _isBlockReportUser = ValueNotifier(false);

  @override
  void initState() {
    super.initState();
    _workspaceId = widget.workspaceId;
    _channelId = widget.channelId;
    _userProfileBloc = getIt<UserProfileBloc>();
    _userPrivateDataBloc = getIt<UserPrivateDataBloc>();
    _blockUserBloc = getIt<BlockUserBloc>();
    _userReportBloc = getIt<UserReportBloc>();

    _translateToHandler = getIt<TranslateToHandler>();
    _translateToBloc = getIt<TranslateToBloc>();

    _userPrivateDataBloc.add(InitUserPrivateDataEvent());
    _visitedProfileBloc = getIt<VisitedProfileBloc>();
    if (widget.callVisitedProfile != null &&
        widget.callVisitedProfile == false) {
      _visitedProfileBloc.add(InitVisitedProfileEvent());
    }
    _userProfileBloc.add(
      InitiateUserProfileEvent(
        userId: widget.userId,
        workspaceId: widget.workspaceId,
        channelId: widget.channelId,
        username: widget.username,
        callVisitedProfile: widget.callVisitedProfile ?? true,
      ),
    );
    _blockUserBloc.add(OnLoadListBlockUserEvent());

    if ((_workspaceId ?? '').isNotEmpty && (_channelId ?? '').isNotEmpty)
      _initTranslateBlocIfNeeded();

    name = ValueNotifier('');
    _isBlockUser = ValueNotifier(false);

    setupListenEventHandler();
    _settingNotificationBloc = getIt<SettingNotificationBloc>();

    _streamUnfriend = GetIt.I.get<AppEventBus>().on<UnFriendEvent>().listen(
      (event) {
        if (event.userId == _user?.userId) {
          _onCancel();
        }
      },
    );
    super.initState();
  }

  void _initTranslateBlocIfNeeded() {
    _translateToBloc.add(
      InitiateTranslateToEvent(
        workspaceId: _workspaceId,
        channelId: _channelId,
      ),
    );
  }

  @override
  void dispose() {
    getIt<UserPrivateDataBloc>().add(GetPrivateDataUnSubscriptionEvent());
    _listenEventSubscription?.cancel();
    // _translateToHandler.dispose();
    _streamUnfriend?.cancel();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void setupListenEventHandler() {
    _listenEventSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<ListenBlockUserEvent>()
        .listen(onReceivedListenEvent);
  }

  void onReceivedListenEvent(event) async {
    if (event is BlockEvent) {
      var item = ChatUser.fromJson(event.user);
      _mapBlockUser.putIfAbsent(item.userId, () => item);
      _isBlockUser.value = _mapBlockUser[_user?.userId] != null;
    }
    if (event is UnBlockEvent) {
      _mapBlockUser.remove(event.userId);
      _isBlockUser.value = _mapBlockUser[_user?.userId] != null;
    }
    _isBlockReportUser.value = _isBlockUser.value;
  }

  void _blocSettingNotification(
    BuildContext context,
    SettingNotificationState state,
  ) {
    state.maybeWhen(
      subscribeChannel: (response) {
        if (response == true) {
          _userProfileBloc.add(
            OnSettingNotificationEvent(
              userId: _user!.userId,
              isNotification: true,
            ),
          );
          _user?.notificationStatus = true;
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              Navigator.pop(context);
            },
          );
        }
        _userProfileBloc.add(RefreshEvent());
      },
      unsubscribeChannel: (response) {
        if (response == true) {
          _userProfileBloc.add(
            OnSettingNotificationEvent(
              userId: _user!.userId,
              isNotification: false,
            ),
          );
          _user?.notificationStatus = false;
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              Navigator.pop(context);
            },
          );
        }
        _userProfileBloc.add(RefreshEvent());
      },
      orElse: () {},
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    appLocalizations = AppLocalizations.of(context)!;

    return MultiBlocProvider(
      providers: [
        BlocProvider<UserProfileBloc>.value(value: _userProfileBloc),
        BlocProvider<UserPrivateDataBloc>.value(value: _userPrivateDataBloc),
        BlocProvider<BlockUserBloc>.value(value: _blockUserBloc),
        BlocProvider<UserReportBloc>.value(value: _userReportBloc),
        BlocProvider<TranslateToBloc>.value(value: _translateToBloc),
        BlocProvider<SettingNotificationBloc>.value(
          value: _settingNotificationBloc,
        ),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<UserPrivateDataBloc, UserPrivateDataState>(
            listenWhen: (prev, current) => prev != current,
            listener: _blocUserPrivateListener,
          ),
          BlocListener<BlockUserBloc, BlockUserState>(
            listenWhen: (prev, current) => prev != current,
            listener: _blocBlockUserListener,
          ),
          BlocListener<UserReportBloc, UserReportState>(
            listenWhen: (prev, current) => prev != current,
            listener: _blocUserReportListener,
          ),
          BlocListener<TranslateToBloc, TranslateToState>(
            listenWhen: (prev, current) => prev != current,
            listener: (context, state) {
              _translateToHandler.handleTranslateToState(
                context: context,
                state: state,
                onLoaded: (metadata, translateResultList) {
                  setState(() {
                    _metadata = metadata;
                  });
                },
              );
            },
          ),
          BlocListener<SettingNotificationBloc, SettingNotificationState>(
            listenWhen: (prev, state) => prev != state,
            listener: _blocSettingNotification,
          ),
        ],
        child: BlocBuilder<UserProfileBloc, UserProfileState>(
          buildWhen: (prev, current) => prev != current,
          builder: (context, state) {
            return state.maybeWhen(
              initial: () {
                return ui.FriendProfileSkeleton();
              },
              loadUser: (user, channel) {
                _user = user;
                badgeEnum = _user?.profile?.userBadgeType ?? 0;
                userBadgeType = UserBadgeEnumExtension.getEnumByValue(badgeEnum)
                    .toUserBadgeType();
                LoadingOverlayHelper.hideLoading(context);
                handleLoadUser(user, channel);
                if (_user?.username?.toLowerCase() ==
                    GlobalConfig.ghost.toLowerCase()) {
                  return _buildGhostPage();
                }
                if (_user != null) {
                  return _buildUserProfilePage();
                } else {
                  return ui.FriendProfileSkeleton();
                }
              },
              showProcessDialog: () {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (!mounted) return;
                  processContent.value = appLocalizations.clearingMessages;
                  processStatus.value = ui.ProcessStatus.loading;
                  ui.DialogUtils.showProcessStatusDialog(
                    context,
                    processStatus: processStatus,
                    processContent: processContent,
                  );
                });
                return _buildUserProfilePage();
              },
              updateProcessDialog: (response) {
                if (response) {
                  changeProcessDialog(
                    ui.ProcessStatus.success,
                    appLocalizations.messagesRemoved,
                  );
                  _hideClearMessage = false;
                  AppEventBus.publish(
                    ClearMessageEvent(
                      workspaceId: widget.workspaceId,
                      channelId: widget.channelId,
                    ),
                  );
                } else {
                  changeProcessDialog(
                    ui.ProcessStatus.failed,
                    appLocalizations.clearingProcessFailed,
                  );
                  _hideClearMessage = false;
                }
                return _user != null
                    ? _buildUserProfilePage()
                    : ui.FriendProfileSkeleton();
              },
              orElse: () => _buildUserProfilePage(),
              refresh: () {
                LoadingOverlayHelper.hideLoading(context);
                return _buildUserProfilePage();
              },
              onError: (code, message) {
                LoadingOverlayHelper.hideLoading(context);
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  ui.DialogUtils.showErrorOccurredTranslateDialog(
                    context,
                    onOkClicked: () {
                      popShowDialogProcess();
                    },
                  );
                });
                return _buildUserProfilePage();
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildUserProfilePage() {
    return ui.FriendProfilePage(
      interface: this,
      showCall: shouldShowCallButton(),
      showCallVideo: shouldShowCallButton(),
      showOnlineStatus: true,
      isHiddenButtonClearMessage: _hideClearMessage,
      isHiddenButtonTranslateTo: _hideClearMessage,
      name: name,
      isHiddenPokeMessageButton: _isHiddenPoke,
      badgeType: userBadgeType,
    );
  }

  bool shouldShowCallButton() {
    if (Platform.isAndroid) return false;

    return _user?.userId != GlobalConfig.ZIICHAT_USER_ID &&
        GlobalConfig.callDmEnable;
  }

  Widget _buildGhostPage() {
    return ui.GhostPage(
      onClickBack: onClickBack,
    );
  }

  void handleLoadUser(ChatUser? user, Channel? channel) {
    if (_user != null) {
      name.value = getAliasName(user?.userId) ?? displayName();
      _isBlockUser.value = _mapBlockUser[user?.userId] == null ? false : true;
      _isBlockReportUser.value = _isBlockUser.value || (user?.blocked ?? false);
      _displayNotificationOption.value = _user?.blocked == true ? false : true;
      if (_channelId == null) {
        _displayNotificationOption.value = false;
      }
      _isHiddenPoke.value =
          _user?.friendData?.status != ChatFriendStatusEnum.FRIEND;
    }

    if (channel != null) {
      if ((_workspaceId ?? '').isEmpty) _workspaceId = channel.workspaceId;
      if ((_channelId ?? '').isEmpty) _channelId = channel.channelId;
      if (channel.dmStatus == DMStatusEnum.CONTACTED) {
        _isHiddenPoke.value = false;
      }
      _hideClearMessage = false;
    }

    if ((_workspaceId ?? '').isNotEmpty && (_channelId ?? '').isNotEmpty)
      _initTranslateBlocIfNeeded();
  }

  @override
  void onClickTranslateTo() {
    if (_user?.blocked == true) {
      return UserProfileHandler.showDialogUnavailable(context);
    }
    _translateToHandler.onClickTranslateTo(
      context: context,
      metadata: _metadata,
      onLanguageUpdated: (ChannelLocalMetadata? newMetadata) {
        setState(() {
          _metadata = newMetadata;
        });
      },
      workspaceId: _workspaceId!,
      channelId: _channelId!,
    );
  }

  void _blocUserPrivateListener(
    BuildContext context,
    UserPrivateDataState state,
  ) {
    state.when(
      initial: () {},
      listUserPrivateData: (List<UserPrivateData> listUserPrivateData) {
        _listUserPrivateData = listUserPrivateData;
        // Cập nhật lại alias name (nếu cần)
        name.value = getAliasName(_user?.userId) ?? displayName();
        setState(() {});
      },
    );
  }

  void _blocBlockUserListener(BuildContext context, BlockUserState state) {
    state.maybeWhen(
      loadListBlockUser: (listBlockUser) {
        _mapBlockUser = {};
        if (listBlockUser != null) {
          for (var item in listBlockUser) {
            if (item.userId == _user?.userId) {
              _mapBlockUser[item.userId] = ChatUser.fromJson(item.toJson());
            }
          }
        }
        _isBlockUser.value = _mapBlockUser[_user?.userId] != null;
        _isBlockReportUser.value = _isBlockUser.value;
      },
      showProcessDialog: () {},
      updateProcessDialog: (response, bool? popOnlyMine) {
        if (response) {
          if (Navigator.canPop(context)) {
            popOnlyMine == true
                ? Navigator.pop(context)
                : popShowDialogProcess();
          }
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              Navigator.pop(context);
            },
          );
        }
      },
      orElse: () {},
    );
  }

  void popShowDialogProcess() {
    AppEventBus.publish(PopToUserProfileEvent());
  }

  void _blocUserReportListener(BuildContext context, UserReportState state) {
    state.maybeWhen(
      updateProcessDialog: (response, userId, name, isBlock) {
        if (response) {
          if (Navigator.canPop(context)) {
            showReportThankYou();
          }
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              popShowDialogProcess();
            },
          );
        }
      },
      orElse: () {},
    );
  }

  void changeProcessDialog(ui.ProcessStatus status, String content) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      processContent.value = content;
      processStatus.value = status;
      Future.delayed(const Duration(seconds: 2), () {
        if (Navigator.canPop(context)) {
          popShowDialogProcess();
        }
      });
    });
  }

  String? getAliasName(String? userId) {
    if (userId == null) return null;
    try {
      UserPrivateData userPrivateData =
          _listUserPrivateData.firstWhere((u) => u.userId == userId);
      return (userPrivateData.aliasName != null &&
              userPrivateData.aliasName!.isNotEmpty)
          ? userPrivateData.aliasName
          : null;
    } catch (error) {
      return null;
    }
  }

  //============== FriendProfileInterface ==============//

  @override
  String avatarPath() {
    return UrlUtils.parseAvatar(_user?.profile?.avatar);
  }

  @override
  String coverPath() {
    return UrlUtils.parseAvatar(_user?.profile?.cover);
  }

  @override
  bool isOnline() {
    return _user?.presenceData?.isOnline ?? false;
  }

  @override
  String statusEmoji() {
    return _user?.statusData?.status ?? '';
  }

  @override
  String statusText() {
    return _user?.statusData?.content ?? '';
  }

  String displayName() {
    return _user?.profile?.displayName ?? '';
  }

  @override
  String username() {
    return _user?.username ?? '';
  }

  @override
  ui.FriendStatus friendStatus() {
    switch (_user?.friendData?.status) {
      case ChatFriendStatusEnum.NOT_FRIEND:
        return ui.FriendStatus.notFriend;
      case ChatFriendStatusEnum.REQUEST_SENT:
        return ui.FriendStatus.requestSent;
      case ChatFriendStatusEnum.REQUEST_RECEIVED:
        return ui.FriendStatus.requestReceived;
      case ChatFriendStatusEnum.REQUEST_DELETED:
        return ui.FriendStatus.requestDeleted;
      case ChatFriendStatusEnum.FRIEND:
        return ui.FriendStatus.friend;
      default:
        return ui.FriendStatus.unspecified;
    }
  }

  Timer? _debounceTimer;
  @override
  void onFriendStatusButtonClick() {
    if (_debounceTimer?.isActive ?? false) return;

    _debounceTimer = Timer(Duration(milliseconds: 500), () {});

    if (_user?.blocked == true) {
      return UserProfileHandler.showDialogUnavailable(context);
    }

    if (!(_user?.friendData?.status == ChatFriendStatusEnum.FRIEND)) {
      LoadingOverlayHelper.showLoading(context);
    }

    switch (_user?.friendData?.status) {
      case ChatFriendStatusEnum.NOT_FRIEND:
        _addFriend();
        break;
      case ChatFriendStatusEnum.REQUEST_SENT:
        _cancelRequest();
        break;
      case ChatFriendStatusEnum.REQUEST_RECEIVED:
      case ChatFriendStatusEnum.REQUEST_DELETED:
        _acceptRequest();
        break;
      case ChatFriendStatusEnum.FRIEND:
        _unfriend();
        break;
      default:
        break;
    }
  }

  void _addFriend() {
    _userProfileBloc.add(AddFriendEvent(user: _user!));
  }

  void _acceptRequest() {
    _userProfileBloc.add(AcceptRequestEvent(user: _user!));
  }

  void _cancelRequest() {
    _userProfileBloc.add(CancelRequestEvent(user: _user!));
  }

  void _unfriend() {
    _isShowUnfriendBottomSheet = true;
    ui.ActionSheetUtil.showUnfriendActionSheet(
      context,
      onClickUnfriend: () {
        LoadingOverlayHelper.showLoading(context);
        _userProfileBloc.add(UnfriendEvent(user: _user!));
        AppEventBus.publish(PopToUserProfileEvent());
      },
      onCancel: _onCancel,
      name: name.value.isNotEmpty ? name.value : username(),
    );
  }

  void _onCancel() {
    _isShowUnfriendBottomSheet = false;
    AppEventBus.publish(PopToUserProfileEvent());
  }

  @override
  void onClickPoke() {
    AppEventBus.publish(
      PokeMessageEvent(
        userId: _user?.userId,
        workspaceId: _workspaceId,
        channelId: _channelId,
      ),
    );
  }

  @override
  void onClickMessage() {
    widget.goToDMChannel?.call(widget.username != null ? _user?.userId : null);
  }

  Future<bool> _checkPermissionsForCallDM() async {
    final isMicroGranted = await PermissionUtils.requestMicrophonePermission(
      context,
    );
    if (isMicroGranted != true) return false;

    final isCameraGranted = await PermissionUtils.requestCameraPermission(
      context,
    );
    if (isCameraGranted != true) return false;

    return true;
  }

  @override
  void onClickCall() async {
    if (_user?.blocked == true) {
      return UserProfileHandler.showDialogUnavailable(context);
    } else {
      final hasPermission = await _checkPermissionsForCallDM();
      final callerId = Config.getInstance().activeSessionKey;
      final outputMe = await getIt.get<GetMeUseCase>().execute(GetMeInput());

      meInfo = outputMe.user;
      if (hasPermission) {
        AppEventBus.publish(
          CallCreatedEvent(
            callee: {
              'user_id': widget.userId,
              'username': _user!.username ?? '',
              'avatar': _user!.profile!.avatar ?? '',
              'displayName': _user!.profile!.displayName ?? '',
            },
            caller: {
              'user_id': callerId,
              'username': meInfo!.username ?? '',
              'avatar': meInfo!.profile!.avatar ?? '',
              'displayName': meInfo!.profile!.displayName ?? '',
            },
          ),
        );
      }
    }
  }

  @override
  void onClickVideo() async {
    if (_user?.blocked == true) {
      return UserProfileHandler.showDialogUnavailable(context);
    } else {
      final hasPermission = await _checkPermissionsForCallDM();
      final callerId = Config.getInstance().activeSessionKey;
      final outputMe = await getIt.get<GetMeUseCase>().execute(GetMeInput());

      meInfo = outputMe.user;
      if (hasPermission) {
        AppEventBus.publish(
          CallCreatedEvent(
            isVideoCall: true,
            callee: {
              'user_id': widget.userId,
              'username': _user!.username ?? '',
              'avatar': _user!.profile!.avatar ?? '',
              'displayName': _user!.profile!.displayName ?? '',
            },
            caller: {
              'user_id': callerId,
              'username': meInfo!.username ?? '',
              'avatar': meInfo!.profile!.avatar ?? '',
              'displayName': meInfo!.profile!.displayName ?? '',
            },
          ),
        );
      }
    }
  }

  @override
  void onClickAddAliasName() {
    UserProfileHandler.showBottomSheetAliasName(
      context,
      _user!.userId,
      aliasName: aliasName(),
    );
  }

  @override
  bool showNotification() {
    return _user?.notificationStatus ?? true;
  }

  @override
  void updateNotification({required bool notification}) async {
    LoadingOverlayHelper.showLoading(context);
    _user?.blocked = await UserProfileHandler.checkBlock(_user!.userId);
    LoadingOverlayHelper.hideLoading(context);
    if (_user?.blocked == true) {
      _displayNotificationOption.value = _user?.blocked == true ? false : true;
      _userProfileBloc.add(RefreshEvent());
      return UserProfileHandler.showDialogUnavailable(context);
    } else {
      if (notification == true) {
        _settingNotificationBloc.add(
          OnSubscribeChannelEvent(
            userId: _user?.userId,
            workspaceId: widget.workspaceId,
            channelId: widget.channelId,
          ),
        );
      } else {
        _settingNotificationBloc.add(
          OnUnsubscribeChannelEvent(
            userId: _user?.userId,
            workspaceId: widget.workspaceId,
            channelId: widget.channelId,
          ),
        );
      }
    }
    // _allowNotification = notification;
  }

  void popShowClearMessage() {
    Navigator.pop(context);
  }

  @override
  void onClickDeleteChat() async {
    LoadingOverlayHelper.showLoading(context);
    _user?.blocked = await UserProfileHandler.checkBlock(_user!.userId);
    LoadingOverlayHelper.hideLoading(context);

    if (_user?.blocked == true) {
      return UserProfileHandler.showDialogUnavailable(context);
    }
    ui.ActionSheetUtil.showClearDmMessagesActionSheet(
      context,
      friendName: name.value.isNotEmpty
          ? name.value
          : displayName().isNotEmpty
              ? displayName()
              : username(),
      onRemoveForMe: () {
        popShowClearMessage();
        ui.DialogUtils.showProcessStatusDialog(
          context,
          processStatus: processStatus,
          processContent: processContent,
        );
        _userProfileBloc.add(
          ClearMessageAllForMeChannelEvent(
            workspaceId: widget.workspaceId,
            channelId: widget.channelId,
            userId: widget.userId,
          ),
        );
      },
      onRemoveForMeAndFriend: () {
        popShowClearMessage();
        ui.DialogUtils.showProcessStatusDialog(
          context,
          processStatus: processStatus,
          processContent: processContent,
        );
        _userProfileBloc.add(
          ClearMessageAllForEveryoneChannelEvent(
            workspaceId: widget.workspaceId,
            channelId: widget.channelId,
            userId: widget.userId,
          ),
        );
      },
      onCancel: () {
        popShowClearMessage();
      },
    );
  }

  @override
  void onClickReportUser() {
    ui.BottomSheetUtil.showReportBottomSheet(
      context: context,
      onClickCancel: () {
        Navigator.pop(context);
      },
      onClickSubmit: (ctx, reason, pretending, other) {
        Navigator.pop(context);
        _userReportBloc.add(
          OnUserReportEvent(
            userId: _user!.userId,
            pretendingTo: pretending.name.split('.')[0],
            reportCategory: reason.name.split('.')[0],
            reason: other,
          ),
        );
      },
    );
  }

  void showReportThankYou() {
    _isBlockReportUser.value =
        (_mapBlockUser[_user!.userId] == null && _user?.blocked == false)
            ? false
            : true;
    ui.BottomSheetUtil.showThankYouDmChannelBottomSheet(
      context: context,
      enableDrag: false,
      isDismissible: false,
      onClickClose: () {
        popShowDialogProcess();
      },
      onClickBlock: () {
        ui.ActionSheetUtil.showBlockUserActionSheet(
          context,
          username: name.value.isNotEmpty ? name.value : username(),
          onBlock: () {
            _blockUserBloc.add(
              OnBlockUserEvent(userId: _user!.userId, popOnlyMine: true),
            );
          },
          onCancel: () {
            Navigator.pop(context);
          },
        );
      },
      onClickCommunityStandard: () async {
        OpenLauncherUrl.onOpenLauncherURL(
          OpenLauncherUrl.pathCommunityStandards,
        );
      },
      blockedUsername: name.value.isNotEmpty ? name.value : username(),
      isBlocked: _isBlockReportUser,
    );
  }

  @override
  void onClickBlock() async {
    LoadingOverlayHelper.showLoading(context);
    bool checkBlocked = await UserProfileHandler.checkBlock(_user!.userId);
    LoadingOverlayHelper.hideLoading(context);

    _user?.blocked = checkBlocked;
    if (_mapBlockUser[_user?.userId] == null && checkBlocked == true) {
      // Đã bị block => show dialog
      return UserProfileHandler.showDialogUnavailable(context);
    } else {
      if (_mapBlockUser[_user?.userId] == null) {
        ui.ActionSheetUtil.showBlockUserActionSheet(
          context,
          username: name.value.isNotEmpty ? name.value : username(),
          onBlock: () {
            Navigator.pop(context);
            _blockUserBloc.add(OnBlockUserEvent(userId: _user!.userId));
          },
          onCancel: () {
            Navigator.pop(context);
          },
        );
      } else {
        ui.ActionSheetUtil.showUnblockUserActionSheet(
          context,
          username: name.value.isNotEmpty ? name.value : username(),
          onUnblock: () {
            Navigator.pop(context);
            _blockUserBloc.add(OnUnBlockUserEvent(userId: _user!.userId));
          },
          onCancel: () {
            Navigator.pop(context);
          },
        );
      }
    }
  }

  @override
  bool isFinishedOverview() {
    return false;
  }

  String aliasName() {
    return getAliasName(_user?.userId) ?? '';
  }

  void onSetAliasNamePressedCancel(BuildContext context) {
    Navigator.of(context).pop();
  }

  @override
  bool isBeingBlocked() {
    return _user?.blocked ?? false;
  }

  @override
  ValueNotifier<bool> isBlocking() {
    return _isBlockUser;
  }

  void onSetAliasNamePressedDone(
    BuildContext context,
    String aliasName,
  ) {
    Navigator.of(context).pop();
  }

  @override
  void onClickBack() {
    widget.onBack!.call();
  }

  @override
  bool isUpdatedAliasName() {
    return aliasName().isNotEmpty;
  }

  void onProfileClicked() {
    if (coverPath().isEmpty) {
      return;
    }
    widget.onGoToViewImagePage?.call(coverPath());
  }

  Future<void> _showViewCoverDialog(BuildContext context) async {
    Navigator.of(context).pop();
    if (coverPath().isEmpty) return;
    final imageProvider = NetworkImage(coverPath());
    showDialog(
      context: context,
      useSafeArea: false,
      builder: (ctx) {
        return ui.AppScaffold(
          hasSafeArea: false,
          appBar: ui.ViewAvatarAppbarWidget(
            interface: ViewAvatarAppbarWidgetImplementation(
              context: context,
              onSaveToGallery: _onSaveCoverToGallery,
            ),
          ),
          body: InteractiveViewer(
            child: Container(
              decoration: BoxDecoration(
                gradient: null,
                color: ui.AppColors.gray0,
                image: DecorationImage(image: imageProvider),
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> _onSaveCoverToGallery() async {
    if (coverPath().isEmpty) return;
    final isGranted =
        await PermissionUtils.requestSaveToGalleryPermission(context);
    if (isGranted) {
      //TODO SaverGallery
    }
  }

  @override
  String translateToLanguage() {
    if (_metadata == null) return '';

    return _translateToHandler.getLangNameFromLanguageCode(
          context,
          _metadata!.translateToLanguage!,
        ) ??
        '';
  }

  @override
  ValueNotifier<bool> displayNotificationOption() {
    return _displayNotificationOption;
  }

  @override
  ValueNotifier<bool>? notificationState() {
    return null;
  }

  @override
  void onAvatarClicked() {
    if (avatarPath().isEmpty) {
      return;
    }
    widget.onGoToViewImagePage?.call(avatarPath());
  }

  @override
  void onStatusBallonClicked() {
    final bottomSheetUserStatus = ui.BottomSheetUserStatus(
      emoji: statusEmoji(),
      content: statusText(),
      avatarUrl: avatarPath(),
      name: name.value,
      username: '@${username()}',
      isMyStatus: false,
      badgeType: userBadgeType,
    );
    ui.BottomSheetUtil.showBottomSheetUserStatus(
      context,
      bottomSheetUserStatus: bottomSheetUserStatus,
      onEditStatusButtonClicked: (BottomSheetUserStatus) {},
      onDeleteButtonClicked: (BottomSheetUserStatus) {},
    );
  }

  @override
  bool isZiiChatProfile() {
    return widget.userId == GlobalConfig.ZIICHAT_USER_ID;
  }
}
