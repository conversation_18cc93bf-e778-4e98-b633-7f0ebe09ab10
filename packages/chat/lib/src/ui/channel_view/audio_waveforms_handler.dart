import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

class AudioWaveformsHandler {
  final PlayerController playerController = PlayerController();
  final RecorderController recorderController = RecorderController();

  ValueNotifier<String> timer = ValueNotifier('');
  bool isRecordingCompleted = false;
  bool isPlayingBack = false;
  bool isCanceled = false;
  bool isProcessing = false;

  String? _audioRecordedPath;

  String get audioRecordedPath => _audioRecordedPath ?? "";

  Future<String> get compressAudioPath async {
    return _audioRecordedPath ?? "";
  }

  List<double> waveFormData = [];

  void updateTimer(int milliseconds) {
    final duration = Duration(milliseconds: milliseconds);
    final minutes = duration.inMinutes.remainder(60).toString().padLeft(2, '0');
    final seconds = duration.inSeconds.remainder(60).toString().padLeft(2, '0');

    timer.value = '$minutes:$seconds';
  }

  void startRecording() {
    recorderController.refresh();
    recorderController.reset();
    recorderController.record(
      recorderSettings: RecorderSettings(
        iosEncoderSettings: IosEncoderSetting(
          iosEncoder: IosEncoder.kAudioFormatMPEG4AAC,
        ),
        androidEncoderSettings: AndroidEncoderSettings(
          androidEncoder: AndroidEncoder.aac,
          androidOutputFormat: AndroidOutputFormat.aac_adts,
        ),
        sampleRate: 44100,
        bitRate: 128000,
      ),
    );
    isRecordingCompleted = false;
  }

  void cancelRecording(BuildContext context) {
    ui.DialogUtils.showDiscardVoice(
      context,
      onNoClicked: (dialogContext) {
        Navigator.pop(dialogContext);
      },
      onYesClicked: (dialogContext) {
        recorderController.stop(true);
        playerController.stopPlayer();
        FocusScope.of(context).requestFocus(FocusNode());
        AppEventBus.publish(PopToChannelViewEvent());
      },
    );
  }

  Future<void> onCloseRecordBottomSheet() async {
    await recorderController.stop(true);
    await playerController.stopPlayer();
  }

  Future<void> onSendZiiVoice() async {
    final path = await recorderController.stop(false);
    Log.e(name: 'AudioWaveformsHandler.onSendZiiVoice', path);
    _audioRecordedPath = path;
  }

  Future<void> stopRecording(Function onRecordingComplete) async {
    if (isProcessing) return;
    isProcessing = true;
    isCanceled = false;

    final path = await recorderController.stop(false);

    if (path != null) {
      _audioRecordedPath = path;

      if (!isCanceled) {
        await playerController.preparePlayer(
          path: path,
          shouldExtractWaveform: false,
        );
        await playerController.setFinishMode(finishMode: FinishMode.pause);
        await playerController.startPlayer();
        await playerController.seekTo(0);
      }

      if (!isCanceled) {
        isRecordingCompleted = true;
        isPlayingBack = true;
        waveFormData = recorderController.waveData;
        onRecordingComplete();
      }
    }
    isProcessing = false;
  }

  void cancel() {
    isCanceled = true;
  }

  Future<void> onPausePlayBack() async {
    await playerController.pausePlayer();
    isPlayingBack = false;
  }

  Future<void> onPlayBack() async {
    if (isCanceled) return;
    await playerController.setFinishMode(finishMode: FinishMode.pause);
    await playerController.startPlayer();
    isPlayingBack = true;
  }

  Future<void> dispose() async {
    playerController.dispose();
    recorderController.dispose();
  }

  Widget waveFormWidget() {
    return isRecordingCompleted
        ? AudioFileWaveforms(
            size: const Size(double.infinity, 30),
            padding: EdgeInsets.symmetric(horizontal: 60.w),
            playerController: playerController,
            continuousWaveform: false,
            enableSeekGesture: false,
            waveformData: waveFormData,
            waveformType: WaveformType.long,
            playerWaveStyle: PlayerWaveStyle(
              liveWaveColor: const Color.fromRGBO(11, 101, 244, 1.0),
              scaleFactor: 15,
              waveThickness: 2.5,
              spacing: 3.3,
              showSeekLine: false,
              fixedWaveColor: Color.fromRGBO(211, 219, 231, 1.0),
            ),
          )
        : AudioWaveforms(
            size: const Size(double.infinity, 30),
            padding: EdgeInsets.symmetric(horizontal: 60.w),
            recorderController: recorderController,
            enableGesture: false,
            waveStyle: WaveStyle(
              extendWaveform: true,
              showMiddleLine: false,
              waveColor: const Color.fromRGBO(11, 101, 244, 1.0),
              scaleFactor: 15,
              waveThickness: 2.5,
              spacing: 3.3,
            ),
          );
  }
}
