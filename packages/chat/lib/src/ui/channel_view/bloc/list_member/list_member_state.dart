part of 'list_member_bloc.dart';

@freezed
sealed class ListMemberState with _$ListMemberState {
  const ListMemberState._();
  factory ListMemberState.loading() = ListMemberStateLoading;

  factory ListMemberState.loaded({
    required Map<String, Member> members,
  }) = ListMemberStateLoaded;

  factory ListMemberState.error({
    required String message,
  }) = ListMemberStateError;
}

extension ListMemberStateX on ListMemberState {
  T when<T>({
    required T Function() loading,
    required T Function(Map<String, Member> members) loaded,
    required T Function(String message) error,
  }) {
    final state = this;

    if (state is ListMemberStateLoading) return loading();
    if (state is ListMemberStateLoaded) return loaded(state.members);
    if (state is ListMemberStateError) return error(state.message);

    throw StateError('Unhandled ListMemberState: $state');
  }

  T maybeWhen<T>({
    T Function()? loading,
    T Function(Map<String, Member> members)? loaded,
    T Function(String message)? error,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is ListMemberStateLoading && loading != null) return loading();
    if (state is ListMemberStateLoaded && loaded != null)
      return loaded(state.members);
    if (state is ListMemberStateError && error != null)
      return error(state.message);

    return orElse();
  }
}
