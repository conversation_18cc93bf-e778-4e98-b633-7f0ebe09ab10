import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../../chat.dart';
import '../../../../data/repositories/database/enums/channel_type.dart';
import '../../../../data/repositories/database/enums/dm_status.dart';

part 'channel_view_bloc.freezed.dart';
part 'channel_view_event.dart';
part 'channel_view_state.dart';

@injectable
class ChannelViewBloc extends BaseBloc<ChannelViewEvent, ChannelViewState> {
  ChannelViewBloc(
    this._loadChannelUseCase,
    this._getUserUseCase,
    this._chatUserRepository,
    this._channelRepository,
    this._getMeetingRoomUseCase,
  ) : super(ChannelViewState.initial()) {
    on<OnInitChannelViewEvent>(_onInitChannelView);
    on<OnChannelUpdatedEvent>(_onChannelUpdated);
    on<OnUserUpdatedEvent>(_onUserUpdated);
    on<OnMeUpdatedEvent>(_onMeUpdated);
    on<ChannelNotExistEvent>(_onChannelNotExist);
    on<GetMeetingRoomEvent>(_onGetMeetingRoom);
  }

  final LoadChannelUseCase _loadChannelUseCase;
  final GetChatUserUseCase _getUserUseCase;
  final ChatUserRepository _chatUserRepository;
  final ChannelRepository _channelRepository;
  Channel? _channel;
  final GetMeetingRoomUseCase _getMeetingRoomUseCase;

  StreamSubscription? _meSubscription;
  StreamSubscription? _userSubscription;
  StreamSubscription? _channelSubscription;
  bool _isClosed = false;

  FutureOr<void> _onInitChannelView(
    OnInitChannelViewEvent event,
    Emitter<ChannelViewState> emit,
  ) async {
    await _subscribeToChannel(event, emit);
    await _subscribeToUser(event, emit);
    await _subscribeToMe(event, emit);

    await _updateChannelFromApi(event, emit);
    await _updateUserFromApi(event, emit);
  }

  Future<void> _subscribeToChannel(
    OnInitChannelViewEvent event,
    Emitter<ChannelViewState> emit,
  ) async {
    if (event.userId != null) {
      _channelSubscription?.cancel();
      _channelSubscription =
          _channelRepository.observerDMChannel(event.userId!, (channel) {
        add(OnChannelUpdatedEvent(channel: channel));
        if (_channel == null) _channel = channel;
      });
      return;
    }

    if (event.workspaceId == null || event.channelId == null) return;

    _channelSubscription?.cancel();
    _channelSubscription = _channelRepository
        .observerChannel(event.workspaceId!, event.channelId!, (channel) {
      add(OnChannelUpdatedEvent(channel: channel));
      if (_channel == null) _channel = channel;
    });
  }

  Future<void> _subscribeToUser(
    OnInitChannelViewEvent event,
    Emitter<ChannelViewState> emit,
  ) async {
    if (event.userId == null || event.userId!.isEmpty) return;
    _userSubscription?.cancel();
    _userSubscription = _chatUserRepository.observerChatUser(
      chatUserId: event.userId,
      listener: (ChatUser? user) {
        if (user != null) {
          add(OnUserUpdatedEvent(user: user));
          if (_channel == null)
            add(
              OnChannelUpdatedEvent(
                channel: _createChannelForUser(user, event.userId!),
              ),
            );
        }
      },
    );
  }

  Future<void> _subscribeToMe(
    OnInitChannelViewEvent event,
    Emitter<ChannelViewState> emit,
  ) async {
    _meSubscription?.cancel();
    _meSubscription = _chatUserRepository.observerMe(
      (ChatUser? user) {
        if (user != null) {
          add(OnMeUpdatedEvent(user: user));
        }
      },
    );
  }

  Future<void> _updateChannelFromApi(
    OnInitChannelViewEvent event,
    Emitter<ChannelViewState> emit,
  ) async {
    final output = await _loadChannelUseCase.execute(
      LoadChannelInput(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        userId: event.userId,
      ),
    );

    if (output.channel != null) {
      _channelRepository.insert(output.channel!);
      if (isClosed) return;
      // add(OnChannelUpdatedEvent(channel: output.channel!));
    }
    if (output.channel == null) {
      if (isClosed) return;
      add(ChannelNotExistEvent());
    }
  }

  Future<void> _updateUserFromApi(
    OnInitChannelViewEvent event,
    Emitter<ChannelViewState> emit,
  ) async {
    if (event.userId == null || event.userId!.isEmpty) return;

    final output = await _getUserUseCase.execute(
      GetChatUserInput(userId: event.userId!),
    );

    if (output.user != null) {
      _chatUserRepository.insert(output.user!);
      if (isClosed) return;
      add(OnUserUpdatedEvent(user: output.user!));
    }
  }

  @override
  Future<void> close() {
    _isClosed = true;
    _userSubscription?.cancel();
    _channelSubscription?.cancel();
    _meSubscription?.cancel();
    return super.close();
  }

  Channel _createChannelForUser(ChatUser user, String userId) {
    final dmStatus =
        // Handle scan qr to message
        (_channel != null &&
                _channel?.workspaceId != null &&
                _channel?.channelId != null &&
                _channel?.userId == null)
            ? null
            // Handle case message request
            : ((_channel?.channelId != null && _channel?.workspaceId != null) ||
                    _channel?.userId != null)
                ? DMStatusEnum.PENDING
                : null;

    return Channel(
      workspaceId: '',
      channelId: '',
      name: user.dmChannelName,
      avatar: UrlUtils.parseAvatar(user.profile?.avatar),
      recipientId: userId,
      type: ChannelTypeEnum.DM,
      dmStatus: dmStatus,
    );
  }

  FutureOr<void> _onChannelUpdated(
    OnChannelUpdatedEvent event,
    Emitter<ChannelViewState> emit,
  ) {
    emit(ChannelViewState.channelLoaded(event.channel));
  }

  FutureOr<void> _onChannelNotExist(
    ChannelNotExistEvent event,
    Emitter<ChannelViewState> emit,
  ) {
    emit(ChannelViewState.channelNotExists());
  }

  FutureOr<void> _onUserUpdated(
    OnUserUpdatedEvent event,
    Emitter<ChannelViewState> emit,
  ) {
    emit(ChannelViewState.userLoaded(event.user));
  }

  FutureOr<void> _onMeUpdated(
    OnMeUpdatedEvent event,
    Emitter<ChannelViewState> emit,
  ) {
    emit(ChannelViewState.meLoaded(event.user));
  }

  Future<void> _onGetMeetingRoom(
    GetMeetingRoomEvent event,
    Emitter<ChannelViewState> emit,
  ) async {
    try {
      await Future.delayed(Duration(seconds: 2));
      final output = await _getMeetingRoomUseCase.execute(
        GetMeetingRoomInput(
          workspaceId: event.workspaceId,
          channelId: event.channelId,
        ),
      );
      if (output.hasRoom) {
        AppEventBus.publish(
          ShowMinimizedCallGroupEvent(
            channelId: event.channelId,
            workspaceId: event.workspaceId,
            numberParticipants: output.numParticipants,
          ),
        );
      }
    } catch (e) {
      Log.e(name: 'ChannelViewBloc._onGetMeetingRoom', e);
    }
  }
}
