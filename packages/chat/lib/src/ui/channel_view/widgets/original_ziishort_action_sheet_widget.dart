import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;
import 'package:ziichat_video_player/ziichat_video_player.dart';

class OriginalZiishortActionSheetWidget extends StatefulWidget {
  const OriginalZiishortActionSheetWidget({
    super.key,
    required this.ziiShortPath,
    required this.messageItem,
  });

  final String ziiShortPath;
  final ui.MessageItem messageItem;

  @override
  State<OriginalZiishortActionSheetWidget> createState() =>
      _OriginalZiishortActionSheetWidgetState();
}

class _OriginalZiishortActionSheetWidgetState
    extends State<OriginalZiishortActionSheetWidget> {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: ClipPath(
        clipper: ui.SquircleClipper(),
        child: SizedBox(
          width: 200,
          height: 200,
          child: ActionSheetVideoPlayerWidget(
            videoPath: widget.ziiShortPath,
            playButton: _buildPlayButton(),
          ),
        ),
      ),
    );
  }

  Widget _buildPlayButton() {
    return SizedBox(
      width: 60.w,
      height: 60.w,
      child: DecoratedBox(
        decoration: const BoxDecoration(
          color: Color.fromRGBO(0, 0, 0, 0.3),
          shape: BoxShape.circle,
        ),
        child: Padding(
          padding: EdgeInsets.only(left: 2.w),
          child: ui.AppAssets.pngIconAsset(
            ui.AppAssets.icPlayNew,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
