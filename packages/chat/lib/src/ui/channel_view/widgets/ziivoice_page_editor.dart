import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import 'send_ziivoice_bottom_sheet_widget.dart';

class ZiiVoicePage extends StatefulWidget {
  const ZiiVoicePage({
    super.key,
    required this.applocalizations,
    required this.onChangedSwitch,
    required this.locale,
    required this.appLocale,
    required this.onChangeLanguage,
    required this.onSendZiiVoice,
    required this.canUseCameraOrMicrophone,
    this.switchValue = false,
  });

  final AppLocalizations applocalizations;
  final void Function(bool) onChangedSwitch;
  final void Function() onChangeLanguage;
  final void Function(String audioRecordedPath) onSendZiiVoice;
  final bool Function(BuildContext context) canUseCameraOrMicrophone;
  final Locale locale;
  final bool switchValue;
  final Locale appLocale;

  @override
  State<ZiiVoicePage> createState() => _ZiiVoicePageState();
}

class _ZiiVoicePageState extends State<ZiiVoicePage> {
  late bool switchValueChange;

  @override
  void initState() {
    switchValueChange = widget.switchValue;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 30.h, horizontal: 20.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Todo: pending phase sau

          // ui.AppCard(
          //   child: Column(
          //     children: [
          //       _buildSelectItem(
          //         subWidget: CupertinoSwitch(
          //           value: switchValueChange,
          //           activeTrackColor: CupertinoColors.activeBlue,
          //           inactiveTrackColor: isDarkMode ? ui.AppColors.gray13 : null,
          //           inactiveThumbColor: isDarkMode ? ui.AppColors.gray0 : null,
          //           thumbColor: isDarkMode ? ui.AppColors.gray0 : null,
          //           onChanged: (bool? value) {
          //             widget.onChangedSwitch(value ?? false);
          //             setState(() {
          //               switchValueChange = value ?? false;
          //             });
          //           },
          //         ),
          //         title: widget.applocalizations.convertSpeechToText,
          //         icon: ui.AppAssets.icAAlphabet,
          //       ),
          //       InkWell(
          //         onTap: widget.onChangeLanguage,
          //         child: _buildSelectItem(
          //           subWidget: Text(
          //             widget.locale.displayLanguageIn(widget.appLocale),
          //             style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          //                   color: ui.AppColors.primaryBlue
          //                       .withAlpha((255 / 2).toInt()),
          //                 ),
          //           ),
          //           title: widget.applocalizations.changeLanguage,
          //           icon: ui.AppAssets.icGlobe,
          //         ),
          //       ),
          //     ],
          //   ),
          // ),
          // SizedBox(
          //   height: 34.h,
          // ),
          _buildButtonHoldToRecordWidget(context),
          SizedBox(
            height: 12.h,
          ),
          Text(
            widget.applocalizations.holdToRecord,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: ui.AppColors.messageCreateTime,
                  fontWeight: FontWeight.w500,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectItem({
    required String icon,
    required String title,
    required Widget subWidget,
    bool isDarkMode = false,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 20.w,
        vertical: 16.h,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Row(
              spacing: 16.w,
              children: [
                ui.AppAssets.pngIconAsset(
                  icon,
                  color: ui.AppColors.primaryBlue,
                ),
                Flexible(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: isDarkMode
                              ? ui.AppColors.descriptionColorDark
                              : ui.AppColors.descriptionColorLight,
                        ),
                  ),
                ),
              ],
            ),
          ),
          subWidget,
        ],
      ),
    );
  }

  Widget _buildButtonHoldToRecordWidget(BuildContext context) {
    return GestureDetector(
      onLongPress: () {
        if (!widget.canUseCameraOrMicrophone(context)) return;

        _onHoldToRecord(context);
      },
      child: ClipOval(
        child: SizedBox(
          width: 55.w,
          height: 55.w,
          child: ColoredBox(
            color: ui.AppColors.primaryBlue,
            child: ui.AppAssets.pngIconAsset(
              ui.AppAssets.icMicrophone,
              color: Theme.of(context).colorScheme.onPrimaryFixed,
            ),
          ),
        ),
      ),
    );
  }

  void _onHoldToRecord(BuildContext context) {
    ui.BottomSheetUtil.showDefaultBottomSheet(
      context: context,
      child: SendZiiVoiceBottomSheetWidget(
        onSendZiiVoice: (String audioRecordedPath) =>
            widget.onSendZiiVoice.call(audioRecordedPath),
      ),
      onClose: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      isWrap: true,
      isDismissible: true,
    );
  }
}
