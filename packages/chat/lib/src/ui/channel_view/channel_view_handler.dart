import 'dart:async';

import 'package:app_core/core.dart';
import 'package:flutter/cupertino.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../friend_profile/user_profile_handler.dart';

class ChannelViewHandler {
  static String sessionKey = Config.getInstance().activeSessionKey ?? '';
  late StreamSubscription? _dialogEventSubscription;
  late BuildContext context;

  void setupDialogHandler(BuildContext context) {
    this.context = context;
    _dialogEventSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<DialogEvent>()
        .listen(_onReceivedFromDialogEvent);
  }

  void dispose() {
    _dialogEventSubscription?.cancel();
  }

  void _onReceivedFromDialogEvent(event) async {
    if (event is DialogErrorOccurredEvent) {
      return showErrorOccurredDialog();
    }
    if (event is DialogUnavailableEvent) {
      return UserProfileHandler.showDialogUnavailable(context);
    }
    if (event is CloseDialogEvent) {
      return Navigator.of(context).pop();
    }
  }

  void showErrorOccurredDialog() {
    return ui.DialogUtils.showErrorOccurredTranslateDialog(
      context,
      onOkClicked: () {
        Navigator.pop(context);
      },
    );
  }
}
