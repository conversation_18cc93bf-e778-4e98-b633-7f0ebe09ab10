part of 'invitable_users_bloc.dart';

abstract class InvitableUsersEvent extends BaseBlocEvent {
  const InvitableUsersEvent();
}

class InitiateInvitableUsersEvent extends InvitableUsersEvent {
  const InitiateInvitableUsersEvent();
}

class ListUserPrivateDataEvent extends InvitableUsersEvent {
  final List<UserPrivateData> listUserPrivateData;

  const ListUserPrivateDataEvent({this.listUserPrivateData = const []});
}
