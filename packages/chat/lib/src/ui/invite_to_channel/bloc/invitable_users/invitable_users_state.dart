part of 'invitable_users_bloc.dart';

@freezed
sealed class InviableUsersState extends BaseBlocState
    with _$InviableUsersState {
  const InviableUsersState._();

  factory InviableUsersState.initial() = InviableUsersStateInitial;

  factory InviableUsersState.loading() = InviableUsersStateLoading;

  factory InviableUsersState.loadedUsers({
    @Default([]) List<InvitableUser> users,
  }) = InviableUsersStateLoaded;
}

extension InviableUsersStateX on InviableUsersState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function()? loading,
    T Function(List<InvitableUser> users)? loadedUsers,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is InviableUsersStateInitial && initial != null) return initial();
    if (state is InviableUsersStateLoading && loading != null) return loading();
    if (state is InviableUsersStateLoaded && loadedUsers != null) {
      return loadedUsers(state.users);
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function() loading,
    required T Function(List<InvitableUser> users) loadedUsers,
  }) {
    final state = this;

    if (state is InviableUsersStateInitial) return initial();
    if (state is InviableUsersStateLoading) return loading();
    if (state is InviableUsersStateLoaded) return loadedUsers(state.users);

    throw StateError('Unhandled state: $state');
  }
}
