part of 'search_users_bloc.dart';

@freezed
sealed class SearchUsersState extends BaseBlocState with _$SearchUsersState {
  const SearchUsersState._();

  factory SearchUsersState.initial() = SearchUsersStateInitial;

  factory SearchUsersState.searching() = SearchUsersStateSearching;

  factory SearchUsersState.searchedUsers({
    @Default([]) List<InvitableUser> users,
    @Default('1') String nextPageToken,
    @Default(false) bool hasNext,
  }) = SearchUsersStateSearched;
}

extension SearchUsersStateExtensions on SearchUsersState {
  bool isSameFactory(SearchUsersState other) {
    if (other is SearchUsersStateInitial && this is SearchUsersStateInitial) {
      return true;
    }
    if (other is SearchUsersStateSearching &&
        this is SearchUsersStateSearching) {
      return true;
    }
    if (other is SearchUsersStateSearched && this is SearchUsersStateSearched) {
      return true;
    }
    return false;
  }

  bool get isStateSearched => this is SearchUsersStateSearched;

  List<InvitableUser> get invitableUsers =>
      (this as SearchUsersStateSearched).users;
}

extension SearchUsersStateX on SearchUsersState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function()? searching,
    T Function(List<InvitableUser> users, String nextPageToken, bool hasNext)?
        searchedUsers,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is SearchUsersStateInitial && initial != null) return initial();
    if (state is SearchUsersStateSearching && searching != null)
      return searching();
    if (state is SearchUsersStateSearched && searchedUsers != null) {
      return searchedUsers(state.users, state.nextPageToken, state.hasNext);
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function() searching,
    required T Function(
      List<InvitableUser> users,
      String nextPageToken,
      bool hasNext,
    ) searchedUsers,
  }) {
    final state = this;

    if (state is SearchUsersStateInitial) return initial();
    if (state is SearchUsersStateSearching) return searching();
    if (state is SearchUsersStateSearched) {
      return searchedUsers(state.users, state.nextPageToken, state.hasNext);
    }

    throw StateError('Unhandled state: $state');
  }
}
