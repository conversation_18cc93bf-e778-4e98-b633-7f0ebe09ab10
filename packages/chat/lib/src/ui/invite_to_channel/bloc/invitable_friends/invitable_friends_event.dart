part of 'invitable_friends_bloc.dart';

abstract class InvitableFriendsEvent extends BaseBlocEvent {
  const InvitableFriendsEvent();
}

class InitiateInvitableFriendsEvent extends InvitableFriendsEvent {
  const InitiateInvitableFriendsEvent();
}

class LoadInvitableFriendsEvent extends InvitableFriendsEvent {
  final String nextPageToken;

  const LoadInvitableFriendsEvent({
    required this.nextPageToken,
  });
}
