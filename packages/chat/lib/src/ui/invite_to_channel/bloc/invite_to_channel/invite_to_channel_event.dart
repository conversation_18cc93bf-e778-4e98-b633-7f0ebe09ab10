part of 'invite_to_channel_bloc.dart';

sealed class InviteToChannelEvent extends BaseBlocEvent {
  const InviteToChannelEvent();
}

@freezed
sealed class InitiateInviteToChannelEvent extends InviteToChannelEvent
    with _$InitiateInviteToChannelEvent {
  const InitiateInviteToChannelEvent._();
  factory InitiateInviteToChannelEvent({
    required String channelId,
    required String workspaceId,
  }) = _InitiateInviteToChannelEvent;
}

@freezed
sealed class SendInviteToChannelEvent extends InviteToChannelEvent
    with _$SendInviteToChannelEvent {
  const SendInviteToChannelEvent._();
  factory SendInviteToChannelEvent({
    required String channelId,
    required String workspaceId,
    @Default([]) List<String> userIDsInvited,
    @Default(null) void Function()? onCreated,
    @Default(null) void Function(V3Error)? onError,
  }) = _SendInviteToChannelEvent;
}

@freezed
sealed class ChangeSearchingStatusEvent extends InviteToChannelEvent
    with _$ChangeSearchingStatusEvent {
  const ChangeSearchingStatusEvent._();
  factory ChangeSearchingStatusEvent(bool isSearching) =
      _ChangeSearchingStatusEvent;
}

@freezed
sealed class ChangeSearchTextEvent extends InviteToChannelEvent
    with _$ChangeSearchTextEvent {
  const ChangeSearchTextEvent._();
  factory ChangeSearchTextEvent(String keyword) = _ChangeSearchTextEvent;
}

@freezed
sealed class UpdateUserIDsInvitedEvent extends InviteToChannelEvent
    with _$UpdateUserIDsInvitedEvent {
  const UpdateUserIDsInvitedEvent._();
  factory UpdateUserIDsInvitedEvent({
    @Default([]) List<String> userIDsInvited,
  }) = _UpdateUserIDsInvitedEvent;
}
