import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../common/di/di.dart';
import '../../../data/models/invitable_user.dart';
import '../bloc/invitable_users/invitable_users_bloc.dart';
import 'list_skeletons.dart';

class InvitableUsersListView extends StatefulWidget {
  const InvitableUsersListView({
    required this.onTapUser,
    this.selectedUserIds,
    this.memberIds,
    super.key,
  });

  final void Function(InvitableUser item) onTapUser;
  final List<String>? selectedUserIds;
  final List<String>? memberIds;

  @override
  State<InvitableUsersListView> createState() => InvitableUsersListViewState();
}

class InvitableUsersListViewState
    extends BasePageState<InvitableUsersListView, InvitableUsersBloc> {
  PagingController<String, InvitableUser> _pagingController =
      PagingController(firstPageKey: '');

  List<String> get selectedUserIds => widget.selectedUserIds ?? [];

  List<String> get memberIds => widget.memberIds ?? [];
  late final InvitableUsersBloc _invitableUsersBloc;
  late final UserPrivateDataBloc _userPrivateDataBloc;
  List<UserPrivateData> _listUserPrivateData = [];

  @override
  void initState() {
    super.initState();
    _invitableUsersBloc = getIt<InvitableUsersBloc>();
    _invitableUsersBloc.add(InitiateInvitableUsersEvent());
    _userPrivateDataBloc = getIt<UserPrivateDataBloc>();
    _userPrivateDataBloc.add(InitUserPrivateDataEvent());
    WidgetsBinding.instance.addPostFrameCallback(
      (_) => bloc.add(
        InitiateInvitableUsersEvent(),
      ),
    );
  }

  @override
  void dispose() {
    _pagingController.dispose();
    getIt<UserPrivateDataBloc>().add(GetPrivateDataUnSubscriptionEvent());
    super.dispose();
  }

  void _blocUserPrivateListener(
    BuildContext context,
    UserPrivateDataState state,
  ) {
    state.when(
      initial: () {},
      listUserPrivateData: (List<UserPrivateData> listUserPrivateData) {
        _listUserPrivateData = listUserPrivateData;
        _pagingController.itemList?.forEach((item) {
          var listUserPrivateDataFiltered = listUserPrivateData
              .firstWhere((user) => user.userId == item.userId);
          item.aliasName = listUserPrivateDataFiltered.aliasName;
        });
      },
    );
  }

  InvitableUser updateUserAliasName(InvitableUser user) {
    try {
      UserPrivateData userPrivateData = _listUserPrivateData
          .firstWhere((privateData) => privateData.userId == user.userId);
      user.aliasName = userPrivateData.aliasName;
      return user;
    } catch (error) {
      return user;
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<InvitableUsersBloc>.value(value: _invitableUsersBloc),
        BlocProvider<UserPrivateDataBloc>.value(value: _userPrivateDataBloc),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<UserPrivateDataBloc, UserPrivateDataState>(
            listenWhen: (prev, state) => prev != state,
            listener: _blocUserPrivateListener,
          ),
        ],
        child: BlocBuilder<InvitableUsersBloc, InviableUsersState>(
          buildWhen: (prev, current) => prev != current,
          builder: (context, state) {
            Widget child = SizedBox.shrink();
            state.when(
              initial: () {
                child = ui.NoInvitableUsersWidget();
              },
              loading: () {
                child = ListSkeletons();
              },
              loadedUsers: (users) {
                if (users.isEmpty) {
                  child = ui.NoInvitableUsersWidget();
                } else {
                  if (_pagingController.value.nextPageKey != null) {
                    _pagingController.appendLastPage(users);
                  }
                  child = PagedListView<String, InvitableUser>(
                    keyboardDismissBehavior:
                        ScrollViewKeyboardDismissBehavior.onDrag,
                    pagingController: _pagingController,
                    builderDelegate: PagedChildBuilderDelegate<InvitableUser>(
                      itemBuilder: (context, invitableUser, index) {
                        updateUserAliasName(invitableUser);
                        final account = ui.ItemAccountInvitation(
                          name: invitableUser.name,
                          id: invitableUser.userId,
                          url: UrlUtils.parseAvatar(invitableUser.avatar),
                        );
                        final isSelected =
                            selectedUserIds.contains(invitableUser.userId) ||
                                memberIds.contains(invitableUser.userId);

                        if (memberIds.contains(invitableUser.userId)) {
                          return SizedBox.shrink();
                        }
                        return ui.AccountWithCheckbox(
                          onTap: () => widget.onTapUser(invitableUser),
                          account: account,
                          isSelected: isSelected,
                        );
                      },
                      noItemsFoundIndicatorBuilder: (_) {
                        return Container();
                      },
                      newPageProgressIndicatorBuilder: (_) {
                        return Center(
                          child: ui.AppCircularProgressIndicator(),
                        );
                      },
                      firstPageProgressIndicatorBuilder: (_) {
                        return Container();
                      },
                    ),
                  );
                }
              },
            );

            return AnimatedSwitcher(
              duration: const Duration(milliseconds: 250),
              transitionBuilder: (Widget child, Animation<double> animation) {
                return FadeTransition(opacity: animation, child: child);
              },
              child: child,
            );
          },
        ),
      ),
    );
  }
}
