part of 'reactions_list_bloc.dart';

@freezed
sealed class ReactionsListEvent with _$ReactionsListEvent {
  const ReactionsListEvent._();
  factory ReactionsListEvent.initiate({
    required String workspaceId,
    required String channelId,
    required String messageId,
    String? userId,
    required String emoji,
    @Default(100) int limit,
  }) = _Initiate;

  factory ReactionsListEvent.loadMore({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required String emoji,
    @Default(100) int limit,
    String? nextPageToken,
  }) = _LoadMore;
}
