import 'dart:async';

import 'package:bloc/src/bloc.dart';
import 'package:filestore_sdk/filestore_sdk.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:upload_manager/upload_manager.dart';

import '../../../../chat.dart';
import '../../../data/repositories/translate_to_repository.dart';
import '../../../domain/usecase/channel/delete_channel_avatar_use_case.dart';
import '../../../domain/usecase/member/load_all_member_use_case.dart';

part 'channel_info_bloc.freezed.dart';

part 'channel_info_event.dart';

part 'channel_info_state.dart';

@injectable
class ChannelInfoBloc extends BaseBloc<ChannelInfoEvent, ChannelInfoState> {
  ChannelInfoBloc(
    this._chatUserRepository,
    this._memberRepository,
    this._channelRepository,
    this._loadChannelUseCase,
    this._clearMessageAllForMeUseCase,
    this._loadAllMemberUseCase,
    this._updateChannelAvatarUseCase,
    this._deleteAllMessagesUseCase,
    this._deleteChannelAvatarUseCase,
    this._uploadImageHandler,
    this._translateToRepository,
  ) : super(ChannelInfoState.initial()) {
    on<InitiateChannelInfoEvent>(_onInit);
    on<ChannelChangedEvent>(_onChannelChangedEvent);
    on<ListMembersChangedEvent>(_onListMembersChanged);
    on<ListUsersChangedEvent>(_onListUsersChanged);
    on<ClearMessageAllForMeEvent>(_onClearMessageAllForMe);
    on<UpdateChannelAvatarEvent>(_onUpdateAvatar);
    on<DeleteChannelAvatarEvent>(_onDeleteAvatar);
    on<OnAvatarUpdatedEvent>(_onAvatarChanged);
    on<OnRefreshEvent>(_onRefresh);
  }

  final MemberRepository _memberRepository;
  final ChatUserRepository _chatUserRepository;
  final ChannelRepository _channelRepository;
  final TranslateToRepository _translateToRepository;

  final LoadChannelUseCase _loadChannelUseCase;
  final ClearMessageAllForMeUseCase _clearMessageAllForMeUseCase;
  final LoadAllMemberUseCase _loadAllMemberUseCase;
  final DeleteAllMessagesUseCase _deleteAllMessagesUseCase;
  final DeleteChannelAvatarUseCase _deleteChannelAvatarUseCase;

  StreamSubscription? _memberSubscription;
  StreamSubscription? _userSubscription;
  StreamSubscription? _channelSubscription;
  StreamSubscription? _channelLocalMetadataSubscription;

  final UploadImageHandler _uploadImageHandler;
  final UpdateChannelAvatarUseCase _updateChannelAvatarUseCase;

  late Channel _channel = Channel.mock();

  late Map<String, Member> _members = {};

  late Map<String, ChatUser> _users = {};

  late Set<String> setPreviousIds = {};

  @override
  Future<void> close() async {
    await _memberSubscription?.cancel();
    await _userSubscription?.cancel();
    await _channelSubscription?.cancel();
    await _channelLocalMetadataSubscription?.cancel();
    super.close();
  }

  Future<void> _onInit(
    InitiateChannelInfoEvent event,
    Emitter<ChannelInfoState> emit,
  ) async {
    await _subscribeToChannel(
      workspaceId: event.channel!.workspaceId,
      channelId: event.channel!.channelId,
    );

    await _channelFromApi(
      workspaceId: event.channel!.workspaceId,
      channelId: event.channel!.channelId,
    );

    _initListMembers(
      workspaceId: event.channel!.workspaceId,
      channelId: event.channel!.channelId,
    );

    _loadMembers(
      workspaceId: event.channel!.workspaceId,
      channelId: event.channel!.channelId,
    );
  }

  Future<void> _loadMembers({
    required String channelId,
    required String workspaceId,
  }) async {
    final output = await _loadAllMemberUseCase.execute(
      LoadAllMemberInput(workspaceId: workspaceId, channelId: channelId),
    );
    if (output.members?.isNotEmpty ?? false) {
      _memberRepository.insertAll(output.members!);
    }
  }

  FutureOr<void> _subscribeToChannel({
    required String workspaceId,
    required String channelId,
  }) {
    _channelSubscription?.cancel();
    _channelSubscription = _channelRepository.observerChannel(
      workspaceId,
      channelId,
      (channel) {
        if (isClosed) return;

        if (channel == null) return;

        add(ChannelChangedEvent(channel: channel));
      },
    );

    _channelLocalMetadataSubscription?.cancel();
    _channelLocalMetadataSubscription = _translateToRepository
        .observerChannelLocalMetadata(workspaceId, channelId, (metadata) {
      if (isClosed) return;
      if (metadata == null) return;
    });
  }

  FutureOr<void> _channelFromApi({
    required String workspaceId,
    required String channelId,
  }) async {
    final output = await _loadChannelUseCase.execute(
      LoadChannelInput(
        channelId: channelId,
        workspaceId: workspaceId,
      ),
    );
    if (output.channel != null) {
      add(ChannelChangedEvent(channel: output.channel!));
    }
  }

  void _initListMembers({
    required String workspaceId,
    required String channelId,
  }) {
    _memberSubscription = _memberRepository.observerListMembers(
      workspaceId: workspaceId,
      channelId: channelId,
      listener: (List<Member> members) {
        add(ListMembersChangedEvent(members: members));
        var setIds = members.map((e) => e.userId).toSet();
        bool isEqual = setIds.containsAll(setPreviousIds) &&
            setPreviousIds.containsAll(setIds);
        if (!isEqual) {
          _resetUserStream(setIds);
        }
        setPreviousIds = setIds;
      },
    );
  }

  void _resetUserStream(Set<String> setIds) {
    _userSubscription?.cancel();

    _userSubscription = _chatUserRepository
        .getAllUsersBySetUserIdOnChannelStream(setIds)
        .listen((List<ChatUser> users) {
      add(ListUsersChangedEvent(users: users));
    });
  }

  FutureOr<void> _onChannelChangedEvent(
    ChannelChangedEvent event,
    Emitter<ChannelInfoState> emit,
  ) {
    if (state is ChannelInfoStateLoaded) {
      emit(
        (state as ChannelInfoStateLoaded).copyWith(channel: event.channel),
      );
    } else {
      _channel = event.channel;
      if (_members.isNotEmpty && _users.isNotEmpty)
        emit(
          ChannelInfoState.loaded(
            channel: _channel,
            members: _members,
            users: _users,
          ),
        );
    }
  }

  FutureOr<void> _onListMembersChanged(
    ListMembersChangedEvent event,
    Emitter<ChannelInfoState> emit,
  ) {
    final members = {
      for (var members in event.members) members.userId: members,
    };
    if (state is ChannelInfoStateLoaded) {
      emit((state as ChannelInfoStateLoaded).copyWith(members: members));
    }
    _members = members;
  }

  FutureOr<void> _onListUsersChanged(
    ListUsersChangedEvent event,
    Emitter<ChannelInfoState> emit,
  ) {
    final users = {for (var user in event.users) user.userId: user};
    if (state is ChannelInfoStateLoaded) {
      emit((state as ChannelInfoStateLoaded).copyWith(users: users));
    } else {
      _users = users;
    }
  }

  FutureOr<void> _onClearMessageAllForMe(
    ClearMessageAllForMeEvent event,
    Emitter<ChannelInfoState> emit,
  ) async {
    emit(ChannelInfoState.showProcessDialog());
    ClearMessageAllForMeOutput clearMessageAllForMeOutputOutput =
        await _clearMessageAllForMeUseCase.execute(
      ClearMessageAllForMeInput(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        userId: event.userId,
      ),
    );

    if (clearMessageAllForMeOutputOutput.ok == true) {
      if (event.channelId != null) {
        AppEventBus.publish(UnPinChannelEvent(channelId: event.channelId!));
      }
      await _deleteAllMessagesUseCase.execute(
        DeleteAllMessagesInput(
          workspaceId: event.workspaceId!,
          channelId: event.channelId!,
        ),
      );
    }
    await Future.delayed(Duration(milliseconds: 30));

    emit(
      ChannelInfoState.updateProcessDialog(
        response: clearMessageAllForMeOutputOutput.ok,
      ),
    );
  }

  Future<void> _onUpdateAvatar(
    UpdateChannelAvatarEvent event,
    Emitter<ChannelInfoState> emit,
  ) async {
    UploadFile avatar = event.avatar;
    String workspaceId = event.workspaceId!;
    String channelId = event.channelId!;

    await _handleUpload(
      avatar,
      (String fileUrl) async {
        await _updateAvatarPath(workspaceId, channelId, fileUrl);
      },
    );
  }

  Future<void> _onDeleteAvatar(
    DeleteChannelAvatarEvent event,
    Emitter<ChannelInfoState> emit,
  ) async {
    _deleteChannelAvatarUseCase.execute(
      DeleteChannelAvatarInput(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
      ),
    );
  }

  Future<void> _handleUpload(
    UploadFile file,
    Future<void> Function(String fileUrl) onSuccessHandler,
  ) async {
    try {
      final result = await _uploadImageHandler.handleUpload(
        file: file,
        onSuccess: (UpFile file, String fileUrl) async {
          await onSuccessHandler(fileUrl);
        },
        onError: _error,
      );

      if (!result.success) {
        add(
          OnErrorEvent(
            errorMessage: " Upload failed: ${result.errorMessage}",
          ),
        );
      }
    } catch (e) {
      add(OnErrorEvent(errorMessage: " Exception while uploading file: $e"));
    }
  }

  Future<void> _updateAvatarPath(
    String workspaceId,
    String channelId,
    String avatarPath,
  ) async {
    final UpdateChannelAvatarOutput output =
        await _updateChannelAvatarUseCase.execute(
      UpdateChannelAvatarInput(
        workspaceId: workspaceId,
        channelId: channelId,
        avatarPath: avatarPath,
      ),
    );

    if (output.success) {
      final avatarPath = output.avatarPath!;
      add(OnAvatarUpdatedEvent(avatarPath));
    } else {
      final error = output.error!;
      add(OnErrorEvent(errorMessage: error.message!));
    }
  }

  void _error(UpFile file, ErrorCode errorCode, String message) {
    switch (errorCode) {
      case ErrorCode.noInternet:
        add(
          OnErrorEvent(
            errorMessage: "Update noInternet error! $message",
          ),
        );
        break;
      case ErrorCode.uploadError:
        add(OnErrorEvent(errorMessage: " Update error! $message"));
        break;
      default:
        add(OnErrorEvent(errorMessage: message));
    }
  }

  void _onAvatarChanged(
    OnAvatarUpdatedEvent event,
    Emitter<ChannelInfoState> emit,
  ) {
    emit(ChannelInfoState.ChannelAvatarChanged(avatarPath: event.avatarPath));
  }

  Future<void> _onRefresh(
    OnRefreshEvent event,
    Emitter<ChannelInfoState> emit,
  ) async {
    emit(
      ChannelInfoState.refresh(),
    );
  }
}
