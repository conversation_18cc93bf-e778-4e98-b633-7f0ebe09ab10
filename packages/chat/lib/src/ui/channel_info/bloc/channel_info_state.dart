part of 'channel_info_bloc.dart';

@freezed
sealed class ChannelInfoState extends BaseBlocState with _$ChannelInfoState {
  const ChannelInfoState._();

  factory ChannelInfoState.initial() = ChannelInfoStateInitial;

  factory ChannelInfoState.loading() = ChannelInfoStateLoading;

  factory ChannelInfoState.loaded({
    required Channel channel,
    required Map<String, Member> members,
    required Map<String, ChatUser> users,
  }) = ChannelInfoStateLoaded;

  factory ChannelInfoState.showProcessDialog() =
      ChannelInfoStateShowProcessDialog;

  factory ChannelInfoState.updateProcessDialog({
    @Default(false) bool response,
  }) = ChannelInfoStateUpdateProcessDialog;

  factory ChannelInfoState.ChannelAvatarChanged({
    @Default('') String? avatarPath,
  }) = ChannelInfoStateChannelAvatarChanged;

  factory ChannelInfoState.refresh() = ChannelInfoStateRefresh;
}

extension ChannelInfoStateX on ChannelInfoState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function()? loading,
    T Function(
      Channel channel,
      Map<String, Member> members,
      Map<String, ChatUser> users,
    )? loaded,
    T Function()? showProcessDialog,
    T Function(bool response)? updateProcessDialog,
    T Function(String? avatarPath)? channelAvatarChanged,
    T Function()? refresh,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is ChannelInfoStateInitial && initial != null) {
      return initial();
    }
    if (state is ChannelInfoStateLoading && loading != null) {
      return loading();
    }
    if (state is ChannelInfoStateLoaded && loaded != null) {
      return loaded(state.channel, state.members, state.users);
    }
    if (state is ChannelInfoStateShowProcessDialog &&
        showProcessDialog != null) {
      return showProcessDialog();
    }
    if (state is ChannelInfoStateUpdateProcessDialog &&
        updateProcessDialog != null) {
      return updateProcessDialog(state.response);
    }
    if (state is ChannelInfoStateChannelAvatarChanged &&
        channelAvatarChanged != null) {
      return channelAvatarChanged(state.avatarPath);
    }
    if (state is ChannelInfoStateRefresh && refresh != null) {
      return refresh();
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function() loading,
    required T Function(
      Channel channel,
      Map<String, Member> members,
      Map<String, ChatUser> users,
    ) loaded,
    required T Function() showProcessDialog,
    required T Function(bool response) updateProcessDialog,
    required T Function(String? avatarPath) channelAvatarChanged,
    required T Function() refresh,
  }) {
    final state = this;

    if (state is ChannelInfoStateInitial) {
      return initial();
    }
    if (state is ChannelInfoStateLoading) {
      return loading();
    }
    if (state is ChannelInfoStateLoaded) {
      return loaded(state.channel, state.members, state.users);
    }
    if (state is ChannelInfoStateShowProcessDialog) {
      return showProcessDialog();
    }
    if (state is ChannelInfoStateUpdateProcessDialog) {
      return updateProcessDialog(state.response);
    }
    if (state is ChannelInfoStateChannelAvatarChanged) {
      return channelAvatarChanged(state.avatarPath);
    }
    if (state is ChannelInfoStateRefresh) {
      return refresh();
    }

    throw StateError('Unhandled ChannelInfoState: $state');
  }
}
