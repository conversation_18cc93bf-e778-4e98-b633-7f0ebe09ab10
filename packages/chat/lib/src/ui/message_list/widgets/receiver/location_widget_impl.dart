import 'package:flutter/material.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../data/repositories/extensions/location_datamap_extensions.dart';
import '../../../../data/repositories/extensions/message_extension.dart';
import '../base/base_receiver_widget.dart';

class LocationWidgetImpl extends BaseReceiverWidget {
  final bool isOpenCheckBox;
  final bool isCheckedMessage;
  final bool isHighlighted;
  final bool shouldAnimate;
  final void Function(MessageItem messageItem)? onCheckBoxButtonTap;
  final bool isShowCreateTime;
  final bool isShowAvatar;
  final bool isShowName;
  final bool isHiddenPin;

  LocationWidgetImpl({
    required this.isOpenCheckBox,
    this.onCheckBoxButtonTap,
    required this.isCheckedMessage,
    required this.isHighlighted,
    required this.shouldAnimate,
    this.isShowCreateTime = false,
    this.isShowAvatar = false,
    this.isHiddenPin = false,
    this.isShowName = false,
    required super.messageItem,
    required super.message,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (!message.hasLocationData) {
      return SizedBox.shrink();
    }

    final locationData = message.firstEmbed!.locationData!;

    return ShareLocationMessageReceiverWidget(
      interface: this,
      messageItem: messageItem,
      onQuote: onQuote,
      isShowCreateTime: isShowCreateTime,
      isShowAvatar: isShowAvatar,
      isShowName: isShowName,
      isHiddenPin: isHiddenPin,
      onCopy: (messageItem) => onCopy(context, messageItem),
      emojiList: message.emojiList,
      thumbnailUrl: locationData.thumbnailUrl ?? '',
      locationDescription: locationData.description ?? '',
      onDeleteMessages: onDeleteMessages,
      onForward: onForward,
      onPinMessage: onPinMessage,
      onUnPinMessage: onUnPinMessage,
      onReport: onReport,
      quickReact: hasQuickReaction,
      isOpenCheckBox: isOpenCheckBox,
      isCheckedMessage: isCheckedMessage,
      onCheckBoxButtonTap: onCheckBoxButtonTap,
      isHighlighted: isHighlighted,
      shouldAnimate: shouldAnimate,
    );
  }

  @override
  void onMessageItemClicked(MessageItem messageItem) {
    if (!message.hasLocationData) return;

    final locationData = message.firstEmbed!.locationData!;

    onClickLink(locationData.mapsLink);
  }
}
