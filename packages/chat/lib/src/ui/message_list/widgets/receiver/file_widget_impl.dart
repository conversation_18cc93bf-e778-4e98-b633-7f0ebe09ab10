import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../data/repositories/database/classes/media_object.dart';
import '../../../../data/repositories/database/entities/message.dart';
import '../../../../data/repositories/extensions/message_extension.dart';
import '../../../../utils/file_message_utils.dart';
import '../base/base_receiver_widget.dart';

class FileWidgetImpl extends StatefulWidget {
  final bool isOpenCheckBox;
  final bool isCheckedMessage;
  final bool isFocusable;
  final bool isShouldAnimate;
  final void Function(MessageItem messageItem)? onCheckBoxButtonTap;

  const FileWidgetImpl({
    required this.isOpenCheckBox,
    this.onCheckBoxButtonTap,
    required this.isCheckedMessage,
    required this.isFocusable,
    required this.isShouldAnimate,
    required this.messageItem,
    required this.message,
    this.isShowCreateTime = false,
    this.isShowAvatar = false,
    this.isShowName = false,
    this.isHiddenPin = false,
    super.key,
  });

  final MessageItem messageItem;
  final Message message;
  final bool isShowCreateTime;
  final bool isShowAvatar;
  final bool isShowName;
  final bool isHiddenPin;

  @override
  State<FileWidgetImpl> createState() => _FileWidgetImplState();
}

class _FileWidgetImplState extends State<FileWidgetImpl> {
  Message get message => widget.message;

  MessageItem get messageItem => widget.messageItem;

  MediaObject? get file {
    if (message.mediaAttachments.isEmpty) {
      return null;
    }
    try {
      final attachment = message.mediaAttachments.firstWhere(
        (element) =>
            element.attachmentId != null &&
            element.attachmentId?.isNotEmpty == true,
      );
      return attachment.file ?? attachment.undefined;
    } catch (_) {
      final firstAttachment = message.mediaAttachments.first;
      return firstAttachment.file ?? firstAttachment.undefined;
    }
  }

  FileState _fileState = FileState.download;
  bool _canViewFile = true;

  @override
  void initState() {
    super.initState();
    final fileObj = file;
    if (fileObj == null) {
      _canViewFile = false;
      return;
    }
    final fileUrl = fileObj.fileUrl;
    _canViewFile = fileUrl != null && FileMessageUtils.canViewFile(fileUrl);
    if (_canViewFile) {
      _fileState = FileState.canview;
      FileMessageUtils.downloadTempFile(
        UrlUtils.parseCDNUrl(fileUrl),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final fileObj = file;
    if (fileObj == null) {
      // File not found, return empty widget
      return const SizedBox();
    }
    return FileWidgetExtendBase(
      isShowCreateTime: widget.isShowCreateTime,
      message: widget.message,
      messageItem: widget.messageItem,
      onTap: onTapMessageItem,
      isHiddenPin: widget.isHiddenPin,
      fileState: _fileState,
      isShowName: widget.isShowName,
      isShowAvatar: widget.isShowAvatar,
      file: fileObj,
      isOpenCheckBox: widget.isOpenCheckBox,
      isCheckedMessage: widget.isCheckedMessage,
      onCheckBoxButtonTap: widget.onCheckBoxButtonTap,
      isFocusable: widget.isFocusable,
      isShouldAnimate: widget.isShouldAnimate,
    );
  }

  Future<void> onTapMessageItem() async {
    final fileObj = file;
    if (fileObj == null) return;
    final fileUrl = UrlUtils.parseCDNUrl(fileObj.fileUrl);
    if (_canViewFile) {
      final tempFile = await FileMessageUtils.tempFileDownloaded(fileUrl);
      if (tempFile == null || tempFile.isEmpty) {
        return;
      }
      FileMessageUtils.openFile(
        filePath: tempFile,
        mimetype: FileMessageUtils.getMimeType(fileUrl),
      );
      return;
    }
    AppEventBus.publish(
      DownloadEnqueueEvent(
        url: fileUrl,
        fileName: fileObj.fileMetadata?.filename,
        messageId: message.messageId,
      ),
    );
  }
}

class FileWidgetExtendBase extends BaseReceiverWidget {
  FileWidgetExtendBase({
    required super.messageItem,
    required super.message,
    required this.onTap,
    required this.file,
    required this.isOpenCheckBox,
    required this.isFocusable,
    required this.isShouldAnimate,
    this.onCheckBoxButtonTap,
    required this.isCheckedMessage,
    this.fileState = FileState.canview,
    required this.isShowCreateTime,
    this.isShowAvatar = false,
    this.isShowName = false,
    this.isHiddenPin = false,
    super.key,
  });

  final VoidCallback onTap;
  final FileState fileState;
  final MediaObject file;
  final bool isOpenCheckBox;
  final bool isCheckedMessage;
  final bool isFocusable;
  final bool isShouldAnimate;
  final bool isShowCreateTime;
  final bool isShowAvatar;
  final bool isShowName;
  final bool isHiddenPin;
  final void Function(MessageItem messageItem)? onCheckBoxButtonTap;

  @override
  Widget build(BuildContext context) {
    return FileMessageReceiverWidget(
      interface: this,
      messageItem: messageItem,
      onQuote: onQuote,
      isHiddenPin: isHiddenPin,
      isShowAvatar: isShowAvatar,
      isShowName: isShowName,
      isShowCreateTime: isShowCreateTime,
      onDeleteMessages: onDeleteMessages,
      onForward: onForward,
      shouldAnimate: isShouldAnimate,
      onPinMessage: onPinMessage,
      onUnPinMessage: onUnPinMessage,
      emojiList: message.emojiList,
      onReport: onReport,
      quickReact: hasQuickReaction,
      fileName: file.fileMetadata?.filename ?? '',
      fileSize: file.fileMetadata?.filesize?.toStringStorageFormat() ?? '',
      onDownload: (MessageItem messageItem) => onDownload(context),
      fileState: fileState,
      isCheckedMessage: isCheckedMessage,
      isOpenCheckBox: isOpenCheckBox,
      onCheckBoxButtonTap: onCheckBoxButtonTap,
      isHighlighted: isFocusable,
    );
  }

  @override
  Future<void> onMessageItemClicked(MessageItem messageItem) async {
    onTap();
  }
}
