import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../../chat.dart';
import '../../../../data/repositories/extensions/message_extension.dart';
import '../../../../domain/event_bus/message/show_fullscreen_event.dart';
import '../base/base_receiver_widget.dart';

class VideosWidgetImpl extends BaseReceiverWidget {
  final bool isOpenCheckBox;
  final bool isCheckedMessage;
  final bool isFocusable;
  final void Function(MessageItem messageItem)? onCheckBoxButtonTap;
  final bool isShowCreateTime;
  final bool isShowAvatar;
  final bool isShowName;
  final bool isHiddenPin;

  VideosWidgetImpl({
    required super.messageItem,
    required this.isOpenCheckBox,
    required this.isCheckedMessage,
    required this.isFocusable,
    this.onCheckBoxButtonTap,
    this.isShowCreateTime = false,
    this.isHiddenPin = false,
    this.isShowAvatar = false,
    this.isShowName = false,
    super.key,
    required super.message,
  });

  @override
  Widget build(BuildContext context) {
    if (message.mediaAttachments.isEmpty) {
      //TODO: handle file not found view
      return const SizedBox();
    }

    final media =
        message.mediaAttachments.last.video ?? MediaObject.nullObject();
    final videoAttachment = VideoAttachment(
      attachmentId:
          media.attachmentId ?? media.fileId ?? RandomUtils.randomUlId(),
      attachmentUrl: UrlUtils.parseCDNUrl(media.thumbnailUrl),
      duration: Duration(seconds: media.fileMetadata?.duration ?? 0),
    );
    return VideosMessageReceiverWidget(
      interface: this,
      messageItem: messageItem,
      onQuote: onQuote,
      isShowName: isShowName,
      isShowCreateTime: isShowCreateTime,
      isShowAvatar: isShowAvatar,
      isHiddenPin: isHiddenPin,
      onDeleteMessages: onDeleteMessages,
      onForward: onForward,
      onPinMessage: onPinMessage,
      onUnPinMessage: onUnPinMessage,
      emojiList: message.emojiList,
      onReport: onReport,
      videoAttachments: [videoAttachment],
      quickReact: hasQuickReaction,
      onVideoClicked: (MessageItem messageItem, String attachmentId) {
        AppEventBus.publish(
          ShowFullscreenEvent(
            messageId: messageItem.messageId,
            workspaceId: message.workspaceId,
            channelId: message.channelId,
          ),
        );
      },
      onDownload: (MessageItem messageItem) => onDownload(context),
      isOpenCheckBox: isOpenCheckBox,
      isCheckedMessage: isCheckedMessage,
      onCheckBoxButtonTap: onCheckBoxButtonTap,
      isHighlighted: isFocusable,
    );
  }
}
