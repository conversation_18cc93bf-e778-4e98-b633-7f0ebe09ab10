import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:sticker/sticker.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../data/repositories/extensions/message_extension.dart';
import '../base/base_receiver_widget.dart';

class StickerWidgetImpl extends BaseReceiverWidget {
  final String messageContent;
  final bool isOpenCheckBox;
  final bool isCheckedMessage;
  final bool isFocusable;
  final void Function(MessageItem messageItem)? onCheckBoxButtonTap;
  final bool isShowCreateTime;
  final bool isShowAvatar;
  final bool isShowName;
  final bool isHiddenPin;

  StickerWidgetImpl({
    required this.messageContent,
    required this.isOpenCheckBox,
    required this.isCheckedMessage,
    required this.isFocusable,
    this.onCheckBoxButtonTap,
    this.isShowCreateTime = false,
    this.isHiddenPin = false,
    this.isShowAvatar = false,
    this.isShowName = false,
    required super.messageItem,
    required super.message,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return StickerMessageReceiverWidget(
      interface: this,
      messageItem: messageItem,
      onQuote: onQuote,
      isShowCreateTime: isShowCreateTime,
      isShowAvatar: isShowAvatar,
      isHiddenPin: isHiddenPin,
      isShowName: isShowName,
      onDeleteMessages: onDeleteMessages,
      onForward: onForward,
      onPinMessage: onPinMessage,
      onUnPinMessage: onUnPinMessage,
      onReport: onReport,
      emojiList: message.emojiList,
      stickerWidget: StickerWidget.fromUrl(
        lottieUrl: UrlUtils.parseSticker(
          message.mediaAttachments.first.sticker!.stickerUrl,
        ),
        size: StickerSize.x256,
      ),
      quickReact: hasQuickReaction,
      isOpenCheckBox: isOpenCheckBox,
      isCheckedMessage: isCheckedMessage,
      onCheckBoxButtonTap: onCheckBoxButtonTap,
      isHighlighted: isFocusable,
      isHideOptionForward: message.mediaAttachments.first.sticker?.stickerId ==
          GlobalConfig.STICKER_POKE_ID,
    );
  }
}
