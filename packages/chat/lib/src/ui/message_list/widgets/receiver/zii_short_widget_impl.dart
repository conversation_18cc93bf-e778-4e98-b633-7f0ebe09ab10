import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../data/repositories/extensions/message_extension.dart';
import '../../../../domain/event_bus/message/show_fullscreen_event.dart';
import '../base/base_receiver_widget.dart';

class ZiiShortWidgetImpl extends BaseReceiverWidget {
  final bool isOpenCheckBox;
  final bool isCheckedMessage;
  final bool isFocusable;
  final void Function(MessageItem messageItem)? onCheckBoxButtonTap;
  final bool isShowCreateTime;
  final bool isShowAvatar;
  final bool isShowName;
  final bool isHiddenPin;

  ZiiShortWidgetImpl({
    required super.messageItem,
    required this.isOpenCheckBox,
    required this.isCheckedMessage,
    required this.isFocusable,
    this.onCheckBoxButtonTap,
    this.isShowCreateTime = false,
    this.isShowAvatar = false,
    this.isShowName = false,
    this.isHiddenPin = false,
    super.key,
    required super.message,
  });

  @override
  Widget build(BuildContext context) {
    if (message.mediaAttachments.isEmpty) {
      //TODO: handle file not found view
      return const SizedBox();
    }

    final media = message.mediaAttachments.last.videoMessage!;
    return ZiiShortMessageReceiverWidget(
      interface: this,
      messageItem: messageItem,
      isShowCreateTime: isShowCreateTime,
      isShowAvatar: isShowAvatar,
      onQuote: onQuote,
      isHiddenPin: isHiddenPin,
      isShowName: isShowName,
      onDeleteMessages: onDeleteMessages,
      onForward: onForward,
      onPinMessage: onPinMessage,
      onUnPinMessage: onUnPinMessage,
      emojiList: message.emojiList,
      onReport: onReport,
      thumbnailUrl: UrlUtils.parseCDNUrl(media.thumbnailUrl),
      quickReact: hasQuickReaction,
      onPlayShot: (MessageItem messageItem) {
        AppEventBus.publish(
          ShowFullscreenEvent(
            messageId: messageItem.messageId,
            workspaceId: message.workspaceId,
            channelId: message.channelId,
          ),
        );
      },
      onDownload: (MessageItem messageItem) => onDownload(context),
      onCopy: (MessageItem messageItem) {},
      isOpenCheckBox: isOpenCheckBox,
      isCheckedMessage: isCheckedMessage,
      onCheckBoxButtonTap: onCheckBoxButtonTap,
      isHighlighted: isFocusable,
    );
  }
}
