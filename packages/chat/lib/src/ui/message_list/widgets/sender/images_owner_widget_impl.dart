import 'dart:io';

import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' hide MessageStatus;

import '../../../../../chat.dart';
import '../../../../data/repositories/extensions/message_extension.dart';
import '../../../../domain/event_bus/message/resend_attachment_event.dart';
import '../../../../domain/event_bus/message/show_fullscreen_event.dart';
import '../../../../domain/usecase/message/delete_local_attachment_use_case.dart';
import '../base/base_sender_widget.dart';

class ImagesOwnerWidgetImpl extends BaseSenderWidget {
  final bool isOpenCheckBox;
  final bool isCheckedMessage;
  final bool isHighlighted;
  final bool shouldAnimate;
  final void Function(MessageItem messageItem)? onCheckBoxButtonTap;
  final bool isShowCreateTime;
  final bool isHiddenPin;
  final bool isLastMessage;

  ImagesOwnerWidgetImpl({
    required this.isOpenCheckBox,
    this.onCheckBoxButtonTap,
    this.isShowCreateTime = false,
    this.isHiddenPin = false,
    this.isLastMessage = false,
    required this.isCheckedMessage,
    required this.isHighlighted,
    required this.shouldAnimate,
    required super.messageItem,
    super.key,
    required super.message,
  });

  int _findAttachmentIndex(
    List<ImageAttachment> attachments,
    String targetId,
  ) {
    return attachments
        .indexWhere((attachment) => attachment.attachmentId == targetId);
  }

  void _onImageClick(
    List<ImageAttachment> imageAttachments,
    String attachmentId,
    MessageItem messageItem,
    BuildContext context,
  ) {
    final attachmentIndex =
        _findAttachmentIndex(imageAttachments, attachmentId);
    if (message.messageStatus == MessageStatus.FAILURE) return;

    final mediaAttachment = message.mediaAttachments[attachmentIndex];
    switch (mediaAttachment.attachmentStatus) {
      case null:
      case AttachmentStatusEnum.UPLOADING:
        break;
      case AttachmentStatusEnum.UNSPECIFIED:
      case AttachmentStatusEnum.SUCCESS:
        AppEventBus.publish(
          ShowFullscreenEvent(
            messageId: messageItem.messageId,
            attachmentIndex: attachmentIndex,
            workspaceId: message.workspaceId,
            channelId: message.channelId,
          ),
        );
        break;
      case AttachmentStatusEnum.FAILURE:
        final isHideOptionResend = mediaAttachment.photo?.filePath != null
            ? !File(mediaAttachment.photo!.filePath!).existsSync()
            : true;
        ActionSheetUtil.showResendMessageActionSheet(
          context,
          onCancel: () => AppEventBus.publish(PopToChannelViewEvent()),
          onResend: (_) => resendAttachment(mediaAttachment),
          onDiscard: (_) => discardAttachment(mediaAttachment),
          messageItem: messageItem,
          isHiddenResend: isHideOptionResend,
        );
        break;
    }
  }

  void resendAttachment(Attachment attachment) {
    discardAttachment(attachment);
    AppEventBus.publish(ResendAttachmentEvent(attachment: attachment));
  }

  void discardAttachment(Attachment attachment) {
    AppEventBus.publish(PopToChannelViewEvent());
    final output = GetIt.instance.get<DeleteLocalAttachmentUseCase>().execute(
          DeleteLocalAttachmentInput(
            attachmentRef: attachment.ref,
            attachmentId: attachment.attachmentId,
          ),
        );
    if (output.message != null) {
      AppEventBus.publish(MessageCreatedEvent(message: output.message));
    }
  }

  @override
  Widget build(BuildContext context) {
    if (message.mediaAttachments.isEmpty) {
      return const SizedBox();
    }

    final defaultAttachments = message.mediaAttachments.map((attachment) {
      final photo = attachment.photo ?? MediaObject.nullObject();
      return ImageAttachment(
        attachmentId:
            photo.attachmentId ?? photo.fileId ?? RandomUtils.randomUlId(),
        attachmentUrl: UrlUtils.parseCDNUrl(photo.fileUrl),
        attachmentPath: '',
        width: photo.fileMetadata?.dimensions?.width?.toDouble() ?? 0,
        height: photo.fileMetadata?.dimensions?.height?.toDouble() ?? 0,
      );
    }).toList();

    return FutureBuilder<List<ImageAttachment>>(
      future: _parseImageAttachments(),
      builder: (context, snapshot) {
        final imageAttachments = snapshot.data ?? defaultAttachments;

        final isHideOptionResend =
            message.messageStatus == MessageStatus.FAILURE &&
                imageAttachments.length != message.mediaAttachments.length;

        return ImagesMessageSenderWidget(
          isLastMessage: isLastMessage,
          messageItem: messageItem,
          onQuote: onQuote,
          isShowCreateTime: isShowCreateTime,
          isHideOptionCopy: imageAttachments.length > 1 || Platform.isAndroid,
          onDeleteMessages: onDeleteMessages,
          onForward: onForward,
          onPinMessage: onPinMessage,
          isHiddenPin: isHiddenPin,
          onUnPinMessage: onUnPinMessage,
          onMessageItemClicked: (messageItem) {},
          onListReactionClicked: onListReactionClicked,
          onEmojiClicked: onEmojiClicked,
          emojiList: message.emojiList,
          imageAttachments: imageAttachments,
          onDownload: (MessageItem messageItem) => onDownload(context),
          onImageClicked: (MessageItem messageItem, String attachmentId) {
            _onImageClick(imageAttachments, attachmentId, messageItem, context);
          },
          onResend: onResendMessage,
          onDiscard: onDiscardMessage,
          onCopy: (MessageItem messageItem) => onCopy(context, messageItem),
          isOpenCheckBox: isOpenCheckBox,
          isCheckedMessage: isCheckedMessage,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          isHideOptionResend: isHideOptionResend,
          isHighlighted: isHighlighted,
          shouldAnimate: shouldAnimate,
        );
      },
    );
  }

  Future<List<ImageAttachment>> _parseImageAttachments() async {
    if (message.mediaAttachments.isEmpty) return [];

    final futures = message.mediaAttachments.map((attachment) async {
      final photo = attachment.photo ?? MediaObject.nullObject();

      final path = await FileUtils.getImagePathFromFileRef(
            messageRef: message.ref!,
            fileRef: photo.fileRef ?? '',
          ) ??
          '';

      return ImageAttachment(
        attachmentId:
            photo.attachmentId ?? photo.fileId ?? RandomUtils.randomUlId(),
        attachmentUrl: UrlUtils.parseCDNUrl(photo.fileUrl),
        attachmentPath: path,
        width: photo.fileMetadata?.dimensions?.width?.toDouble() ?? 0,
        height: photo.fileMetadata?.dimensions?.height?.toDouble() ?? 0,
      );
    });

    return await Future.wait(futures);
  }
}
