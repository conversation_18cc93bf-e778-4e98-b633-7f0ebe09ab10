import 'dart:io';

import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../../chat.dart';
import '../../../../data/repositories/extensions/message_extension.dart';
import '../../../../utils/file_message_utils.dart';
import '../base/base_sender_widget.dart';

class FileOwnerWidgetImpl extends StatefulWidget {
  const FileOwnerWidgetImpl({
    required this.isOpenCheckBox,
    required this.isHighlighted,
    required this.shouldAnimate,
    this.onCheckBoxButtonTap,
    required this.isCheckedMessage,
    required this.messageItem,
    required this.message,
    this.isShowCreateTime = false,
    this.isHiddenPin = false,
    this.isLastMessage = false,
    super.key,
  });

  final MessageItem messageItem;
  final Message message;
  final bool isOpenCheckBox;
  final bool isCheckedMessage;
  final bool isHighlighted;
  final bool shouldAnimate;
  final void Function(MessageItem messageItem)? onCheckBoxButtonTap;
  final bool isShowCreateTime;
  final bool isHiddenPin;
  final bool isLastMessage;

  @override
  State<FileOwnerWidgetImpl> createState() => _FileOwnerWidgetImplState();
}

class _FileOwnerWidgetImplState extends State<FileOwnerWidgetImpl> {
  Message get message => widget.message;

  MessageItem get messageItem => widget.messageItem;

  MediaObject? get file {
    if (message.mediaAttachments.isEmpty) {
      return null;
    }
    try {
      final attachment = message.mediaAttachments.firstWhere(
        (element) =>
            element.attachmentId != null &&
            element.attachmentId?.isNotEmpty == true,
      );
      return attachment.file ?? attachment.undefined;
    } catch (_) {
      final firstAttachment = message.mediaAttachments.first;
      return firstAttachment.file ?? firstAttachment.undefined;
    }
  }

  FileState _fileState = FileState.download;
  bool _canViewFile = true;

  @override
  void initState() {
    super.initState();
    final fileObj = file;
    if (fileObj == null) return;
    final fileUrlOrPath = fileObj.fileUrl ?? fileObj.filePath;
    _canViewFile =
        fileUrlOrPath != null && FileMessageUtils.canViewFile(fileUrlOrPath);
    if (_canViewFile) {
      _fileState = FileState.canview;
      if (fileObj.fileUrl != null) {
        FileMessageUtils.downloadTempFile(
            UrlUtils.parseCDNUrl(fileObj.fileUrl));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final fileObj = file;
    if (fileObj == null) {
      // File not found, return empty widget
      return const SizedBox();
    }
    final isHideOptionResend =
        fileObj.filePath != null ? !File(fileObj.filePath!).existsSync() : true;
    return FileOwnerWidgetExtendBase(
      isLastMessage: widget.isLastMessage,
      message: widget.message,
      messageItem: widget.messageItem,
      onTap: onTapMessageItem,
      fileState: _fileState,
      file: fileObj,
      isHiddenPin: widget.isHiddenPin,
      isShowCreateTime: widget.isShowCreateTime,
      isOpenCheckBox: widget.isOpenCheckBox,
      isCheckedMessage: widget.isCheckedMessage,
      onCheckBoxButtonTap: widget.onCheckBoxButtonTap,
      isHighlighted: widget.isHighlighted,
      shouldAnimate: widget.shouldAnimate,
      isHideOptionResend: isHideOptionResend,
    );
  }

  Future<void> onTapMessageItem() async {
    final fileObj = file;
    if (fileObj == null) return;
    final fileUrl = UrlUtils.parseCDNUrl(fileObj.fileUrl);
    if (_canViewFile) {
      final tempFile = fileUrl.isEmpty
          ? fileObj.filePath
          : await FileMessageUtils.tempFileDownloaded(fileUrl);
      if (tempFile == null || tempFile.isEmpty) {
        return;
      }
      FileMessageUtils.openFile(
        filePath: tempFile,
        mimetype: FileMessageUtils.getMimeType(fileUrl),
      );
      return;
    }
    AppEventBus.publish(
      DownloadEnqueueEvent(
        url: fileUrl,
        fileName: fileObj.fileMetadata?.filename,
        messageId: message.messageId,
      ),
    );
  }
}

class FileOwnerWidgetExtendBase extends BaseSenderWidget {
  FileOwnerWidgetExtendBase({
    required super.messageItem,
    required super.message,
    required this.onTap,
    required this.file,
    required this.isOpenCheckBox,
    required this.isCheckedMessage,
    required this.isHighlighted,
    required this.shouldAnimate,
    this.onCheckBoxButtonTap,
    this.fileState = FileState.canview,
    this.isShowCreateTime = false,
    this.isHiddenPin = false,
    this.isHideOptionResend = false,
    this.isLastMessage = false,
    super.key,
  });

  final VoidCallback onTap;
  final FileState fileState;
  final MediaObject file;
  final bool isOpenCheckBox;
  final bool isShowCreateTime;
  final bool isCheckedMessage;
  final bool isHighlighted;
  final bool shouldAnimate;
  final bool isHiddenPin;
  final bool isHideOptionResend;
  final bool isLastMessage;
  final void Function(MessageItem messageItem)? onCheckBoxButtonTap;

  @override
  Widget build(BuildContext context) {
    return FileMessageSenderWidget(
      isLastMessage: isLastMessage,
      messageItem: messageItem,
      onQuote: onQuote,
      isHiddenPin: isHiddenPin,
      onDeleteMessages: onDeleteMessages,
      onForward: onForward,
      isShowCreateTime: isShowCreateTime,
      onPinMessage: onPinMessage,
      onUnPinMessage: onUnPinMessage,
      onMessageItemClicked: onMessageItemClicked,
      onListReactionClicked: onListReactionClicked,
      onEmojiClicked: onEmojiClicked,
      emojiList: message.emojiList,
      fileName: file.fileMetadata?.filename ?? '',
      fileSize: file.fileMetadata?.filesize?.toStringStorageFormat() ?? '',
      onDownload: (MessageItem messageItem) => onDownload(context),
      onResend: onResendMessage,
      onDiscard: onDiscardMessage,
      fileState: fileState,
      isOpenCheckBox: isOpenCheckBox,
      isCheckedMessage: isCheckedMessage,
      onCheckBoxButtonTap: onCheckBoxButtonTap,
      isHideOptionResend: isHideOptionResend,
      isHideOptionCopy: true,
      isHighlighted: isHighlighted,
      shouldAnimate: shouldAnimate,
    );
  }

  @override
  Future<void> onMessageItemClicked(MessageItem messageItem) async {
    onTap();
  }
}
