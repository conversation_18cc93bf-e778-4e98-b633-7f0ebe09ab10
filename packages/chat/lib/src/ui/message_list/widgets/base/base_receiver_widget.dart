import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../../chat.dart' as chat;
import '../../../../common/di/di.dart';
import '../../../../domain/event_bus/message/show_fullscreen_event.dart';
import '../../../../domain/event_bus/show_member_settings_event.dart';
import '../../../../domain/usecase/chat_user/get_me_use_case.dart';
import '../../../../utils/file_message_utils.dart';
import '../../constants.dart';
import '../../reaction_handler.dart';
import 'one_time_message_action_controller.dart';

abstract class BaseReceiverWidget extends StatelessWidget
    implements MessageContainerReceiverWidgetInterface {
  final MessageItem messageItem;
  final chat.Message message;

  late final ReactionHandler _reactionHandler;

  final AppLocalizations appLocalizations =
      GetIt.instance.get<AppLocalizations>();
  final OneTimeMessageActionController _oneTimeMessageActionController =
      OneTimeMessageActionController();

  BaseReceiverWidget({
    required this.messageItem,
    required this.message,
    super.key,
  }) {
    _reactionHandler = ReactionHandler.getInstance(
      channelId: messageItem.channelId,
      workspaceId: messageItem.workspaceId,
      userId: messageItem.recipientId,
    );
  }

  @override
  void onMessageItemClicked(MessageItem messageItem) {
    AppEventBus.publish(
      ShowFullscreenEvent(
        messageId: messageItem.messageId,
        workspaceId: message.workspaceId,
        channelId: message.channelId,
      ),
    );
  }

  @override
  void onListReactionClicked(MessageItem messageItem) {
    _reactionHandler.showListReactions(message, messageItem);
  }

  @override
  void onEmojiClicked(MessageItem messageItem, Map<String, dynamic> emoji) {
    if (!_oneTimeMessageActionController.canProcessOption()) return;
    _reactionHandler.toggleReaction(
      messageItem: messageItem,
      message: message,
      emoji: emoji,
    );
  }

  @override
  void onAvatarClicked(MessageItem messageItem) {
    AppEventBus.publish(ShowMemberSettingsEvent(userId: messageItem.userId));
  }

  @override
  void onQuickReactClicked(MessageItem messageItem) {
    _reactionHandler.quickReaction(
      messageItem: messageItem,
      message: message,
    );
  }

  @override
  void onUserNameClicked(MessageItem messageItem) {
    AppEventBus.publish(ShowMemberSettingsEvent(userId: messageItem.userId));
  }

  //region Message Options
  void onQuote(MessageItem messageItem) {
    if (!_oneTimeMessageActionController.canProcessOption()) return;
    if (messageItem.messageErrorReason == MessageErrorReason.blocked) {
      return AppEventBus.publish(DialogUnavailableEvent());
    }
    AppEventBus.publish(
      QuoteMessageEvent(
        workspaceId: message.workspaceId,
        channelId: message.channelId,
        messageId: message.messageId,
        isBlocked: messageItem.messageErrorReason == MessageErrorReason.blocked,
      ),
    );
    AppEventBus.publish(PopToChannelViewEvent());
  }

  void onCopy(BuildContext context, MessageItem messageItem) async {
    if (!_oneTimeMessageActionController.canProcessOption()) return;
    AppEventBus.publish(
      CopyEvent(
        workspaceId: message.workspaceId,
        channelId: message.channelId,
        messageId: message.messageId,
        context: context,
        appLocalizations: appLocalizations,
      ),
    );
    AppEventBus.publish(PopToChannelViewEvent());
  }

  void onEdit(MessageItem messageItem) {
    Log.d('onEdit Clicked');
  }

  void onTranslateMessage(MessageItem messageItem) {
    if (!_oneTimeMessageActionController.canProcessOption()) return;
    if (messageItem.messageErrorReason == MessageErrorReason.blocked) {
      return AppEventBus.publish(DialogUnavailableEvent());
    }
    AppEventBus.publish(PopToChannelViewEvent());
  }

  void onDeleteMessages(MessageItem messageItem) {
    if (!_oneTimeMessageActionController.canProcessOption()) return;
    AppEventBus.publish(
      CallCheckMessagesEvent(
        channelId: messageItem.channelId,
        messageId: messageItem.messageId,
        isCall: true,
        isDelete: true,
        isBlocked: messageItem.messageErrorReason == MessageErrorReason.blocked,
      ),
    );
  }

  void onForward(MessageItem messageItem) {
    if (!_oneTimeMessageActionController.canProcessOption()) return;
    if (messageItem.messageErrorReason == MessageErrorReason.blocked) {
      return AppEventBus.publish(DialogUnavailableEvent());
    }
    AppEventBus.publish(
      CallCheckMessagesEvent(
        channelId: messageItem.channelId,
        messageId: messageItem.messageId,
        isCall: true,
        isForward: true,
        isBlocked: messageItem.messageErrorReason == MessageErrorReason.blocked,
      ),
    );
  }

  void onPinMessage(MessageItem messageItem) {
    if (!_oneTimeMessageActionController.canProcessOption()) return;
    if (messageItem.messageErrorReason == MessageErrorReason.blocked) {
      return AppEventBus.publish(DialogUnavailableEvent());
    }
    AppEventBus.publish(
      CallPinUnPinMessageEvent(
        workspaceId: messageItem.workspaceId,
        channelId: messageItem.channelId,
        isChannel: messageItem.isChannel,
        messageId: messageItem.messageId,
        status: true,
      ),
    );
  }

  Future<void> onDownload(BuildContext context) async {
    if (!_oneTimeMessageActionController.canProcessOption()) return;
    AppEventBus.publish(PopToChannelViewEvent());
    final isGranted =
        await PermissionUtils.requestSaveToGalleryPermission(context);
    if (!isGranted) {
      return;
    }

    final mediaList = FileMessageUtils.getMediaList(message);

    await FileMessageUtils.downloadAllMedia(mediaList);
  }

  void onUnPinMessage(MessageItem messageItem) {
    if (!_oneTimeMessageActionController.canProcessOption()) return;
    if (messageItem.messageErrorReason == MessageErrorReason.blocked) {
      return AppEventBus.publish(DialogUnavailableEvent());
    }
    AppEventBus.publish(
      CallPinUnPinMessageEvent(
        workspaceId: messageItem.workspaceId,
        channelId: messageItem.channelId,
        isChannel: messageItem.isChannel,
        messageId: messageItem.messageId,
        status: false,
      ),
    );
  }

  void onReport(MessageItem messageItem) {
    if (!_oneTimeMessageActionController.canProcessOption()) return;
    AppEventBus.publish(
      CallReportMessageEvent(
        workspaceId: messageItem.isChannel ? messageItem.workspaceId : null,
        channelId: messageItem.isChannel ? messageItem.channelId : null,
        messageId: messageItem.messageId,
        userId: messageItem.userId,
        name: messageItem.name ?? '',
      ),
    );
  }

  //endregion

  void onClickMention(BuildContext context, String mention) async {
    final outputMe = await getIt.get<GetMeUseCase>().execute(GetMeInput());
    final currentUser = outputMe.user;
    if (currentUser == null) return;

    if (mention == '@${currentUser.username}' ||
        mention == '@${appLocalizations.all}') {
      return;
    }

    LoadingOverlayHelper.showLoading(context);

    final outputUser = await getIt
        .get<chat.GetChatUserByUsernameUseCase>()
        .execute(chat.GetChatUserByUsernameInput(userName: mention));
    final mentionedUser = outputUser.user;
    LoadingOverlayHelper.hideLoading(context);

    if (mentionedUser == null) {
      DialogUtils.showAccountUnavailableDialog(
        context,
        onFirstAction: (dialogContext) => dialogContext.maybePop(),
      );
      return;
    }

    AppEventBus.publish(OnGoToUserProfileEvent(username: mention));
  }

  void onClickLink(String link) {
    if (!_oneTimeMessageActionController.canProcessOption()) return;
    AppEventBus.publish(
      OnLinkClickedEvent(
        link: link,
        workspaceId: message.workspaceId,
        channelId: message.channelId,
        userId: messageItem.recipientId,
        messageId: message.messageId,
      ),
    );
  }

  bool get hasQuickReaction {
    return messageItem.isLastMessage &&
        (message.reactions?[heartEmoji['emoji'] as String]?.isReacted ?? false);
  }
}
