part of 'messages_bloc.dart';

@freezed
sealed class MessagesEvent extends BaseBlocEvent with _$MessagesEvent {
  const MessagesEvent._();
  factory MessagesEvent.initiate({
    String? workspaceId,
    String? channelId,
    String? userId,
    @Default(50) int limit,
  }) = MessagesEventInitiate;

  factory MessagesEvent.loadMore({
    String? workspaceId,
    String? channelId,
    String? userId,
    @Default(50) int limit,
    String? nextPageToken,
  }) = MessagesEventLoadMore;

  factory MessagesEvent.sync({
    String? workspaceId,
    String? channelId,
    String? userId,
    required String prevPageToken,
  }) = Sync;

  factory MessagesEvent.addTempMessage(Message message) = AddTempMessage;

  factory MessagesEvent.addMessage(Message message) = AddMessage;

  factory MessagesEvent.updateMessage(Message message) = UpdateMessage;

  factory MessagesEvent.updateAttachment(Message message) = UpdateAttachment;

  factory MessagesEvent.updateMessageStatus({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required MessageStatus messageStatus,
    String? messageRef,
  }) = UpdateMessageStatus;

  factory MessagesEvent.addMessageReaction(ReactionData reaction) =
      AddMessageReaction;

  factory MessagesEvent.revokeMessageReaction(ReactionData reaction) =
      RevokeMessageReaction;

  factory MessagesEvent.saveTempMessage(Message message) = SaveTempMessage;

  factory MessagesEvent.clearAllMessageEvent({
    String? workspaceId,
    String? channelId,
  }) = ClearAllMessageEvent;

  factory MessagesEvent.dispose() = Dispose;

  factory MessagesEvent.clearMessageUnSubscription() =
      ClearMessageUnSubscription;

  factory MessagesEvent.OnDeleteMessageForMeEvent({
    String? workspaceId,
    String? channelId,
    String? userId,
    List<String>? messageIds,
  }) = OnDeleteMessageForMeEvent;

  factory MessagesEvent.OnDeleteMessageForEveryOneEvent({
    String? workspaceId,
    String? channelId,
    String? userId,
    List<String>? messageIds,
  }) = OnDeleteMessageForEveryOneEvent;

  factory MessagesEvent.MarkAsReadMessageEvent({
    required String messageId,
    String? workspaceId,
    String? channelId,
    String? userId,
  }) = MarkAsReadMessageEvent;

  factory MessagesEvent.OnLocalDeleteMessageEvent({
    String? workspaceId,
    String? channelId,
    String? userId,
    List<String>? messageIds,
  }) = OnLocalDeleteMessageEvent;

  factory MessagesEvent.resendMessage(Message message) = ResendTempMessage;

  factory MessagesEvent.refreshDelete() = refreshDelete;

  factory MessagesEvent.onPinUnPinMessage({
    required String workspaceId,
    required String channelId,
    String? userId,
    required String messageId,
    required bool status,
  }) = OnPinUnPinMessage;

  factory MessagesEvent.pinUnPinUpdateMessage(Message message) =
      pinUnPinUpdateMessage;

  factory MessagesEvent.OnLoadPinUnPinMessage(List<Message> messages) =
      OnLoadPinUnPinMessage;

  factory MessagesEvent.InitLoadPinUnPinMessage({
    String? workspaceId,
    String? channelId,
    String? userId,
  }) = InitLoadPinUnPinMessage;

  factory MessagesEvent.updateChannelIdEvent({
    String? workspaceId,
    String? channelId,
  }) = UpdateChannelIdEvent;
}
