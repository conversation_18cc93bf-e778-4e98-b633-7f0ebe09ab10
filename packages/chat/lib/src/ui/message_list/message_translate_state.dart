import 'package:ziichat_ui/ziichat_ui.dart';

class MessagesTranslateState {
  final String messageId;

  late TranslateStatus _translateStatus;

  TranslateStatus get translateStatus => _translateStatus;

  set translateStatus(TranslateStatus value) {
    _translateStatus = value;
  }

  late TranslateButtonStatus _translateButtonStatus;

  TranslateButtonStatus get translateButtonStatus => _translateButtonStatus;

  set translateButtonStatus(TranslateButtonStatus value) {
    _translateButtonStatus = value;
  }

  late bool _hasTranslate;

  bool get hasTranslate => _hasTranslate;

  set hasTranslate(bool value) {
    _hasTranslate = value;
  }

  MessagesTranslateState({
    required this.messageId,
    required TranslateStatus translateStatus,
    required TranslateButtonStatus translateButtonStatus,
    required bool hasTranslate,
  })  : _translateStatus = translateStatus,
        _translateButtonStatus = translateButtonStatus,
        _hasTranslate = hasTranslate;

  void update(MessagesTranslateState newState) {
    _translateStatus = newState.translateStatus;
    _translateButtonStatus = newState.translateButtonStatus;
    _hasTranslate = newState.hasTranslate;
  }
}
