part of 'channels_bloc.dart';

@freezed
sealed class ChannelsState extends BaseBlocState with _$ChannelsState {
  const ChannelsState._();

  factory ChannelsState.initial() = ChannelsStateInitial;

  factory ChannelsState.loading() = ChannelsStateLoading;

  factory ChannelsState.loaded({
    @Default([]) List<Channel> channels,
    @Default(false) bool noMoreItems,
  }) = ChannelsStateLoaded;

  factory ChannelsState.channelCreatedOrUpdated({
    required Channel channel,
  }) = ChannelsStateChannelCreatedOrUpdated;

  factory ChannelsState.channelDeleted({
    required String workspaceId,
    required String channelId,
  }) = ChannelsStateChannelDeleted;

  factory ChannelsState.callClearMessageAllForMe({
    String? workspaceId,
    String? channelId,
    String? userId,
  }) = ChannelsStateCallClearMessageAllForMe;

  factory ChannelsState.clearMessageAllForMe({
    @Default(false) bool response,
  }) = ChannelsStateClearMessageAllForMe;

  factory ChannelsState.callClearMessageAllForEveryone({
    String? workspaceId,
    String? channelId,
    String? userId,
  }) = ChannelsStateCallClearMessageAllForEveryone;

  factory ChannelsState.clearMessageAllForEveryone({
    @Default(false) bool response,
  }) = ChannelsStateClearMessageAllForEveryone;

  factory ChannelsState.refreshClearMessage() =
      ChannelsStateRefreshClearMessage;

  factory ChannelsState.translatedMessages({
    required Map<String, String?> translatedMessages,
  }) = ChannelsStateTranslatedMessages;

  factory ChannelsState.loadedMeetingRoomList({
    required Map<String, bool> mapMeetingChannelIds,
  }) = LoadedMeetingRoomList;
}

extension ChannelsStateX on ChannelsState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function()? loading,
    T Function(List<Channel> channels, bool noMoreItems)? loaded,
    T Function(Channel channel)? channelCreatedOrUpdated,
    T Function(String workspaceId, String channelId)? channelDeleted,
    T Function(String? workspaceId, String? channelId, String? userId)?
        callClearMessageAllForMe,
    T Function(bool response)? clearMessageAllForMe,
    T Function(String? workspaceId, String? channelId, String? userId)?
        callClearMessageAllForEveryone,
    T Function(bool response)? clearMessageAllForEveryone,
    T Function()? refreshClearMessage,
    T Function(Map<String, String?> translatedMessages)? translatedMessages,
    T Function(Map<String, bool> mapMeetingChannelIds)? loadedMeetingRoomList,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is ChannelsStateInitial && initial != null) return initial();
    if (state is ChannelsStateLoading && loading != null) return loading();
    if (state is ChannelsStateLoaded && loaded != null) {
      return loaded(state.channels, state.noMoreItems);
    }
    if (state is ChannelsStateChannelCreatedOrUpdated &&
        channelCreatedOrUpdated != null) {
      return channelCreatedOrUpdated(state.channel);
    }
    if (state is ChannelsStateChannelDeleted && channelDeleted != null) {
      return channelDeleted(state.workspaceId, state.channelId);
    }
    if (state is ChannelsStateCallClearMessageAllForMe &&
        callClearMessageAllForMe != null) {
      return callClearMessageAllForMe(
        state.workspaceId,
        state.channelId,
        state.userId,
      );
    }
    if (state is ChannelsStateClearMessageAllForMe &&
        clearMessageAllForMe != null) {
      return clearMessageAllForMe(state.response);
    }
    if (state is ChannelsStateCallClearMessageAllForEveryone &&
        callClearMessageAllForEveryone != null) {
      return callClearMessageAllForEveryone(
        state.workspaceId,
        state.channelId,
        state.userId,
      );
    }
    if (state is ChannelsStateClearMessageAllForEveryone &&
        clearMessageAllForEveryone != null) {
      return clearMessageAllForEveryone(state.response);
    }
    if (state is ChannelsStateRefreshClearMessage &&
        refreshClearMessage != null) {
      return refreshClearMessage();
    }
    if (state is ChannelsStateTranslatedMessages &&
        translatedMessages != null) {
      return translatedMessages(state.translatedMessages);
    }
    if (state is LoadedMeetingRoomList && loadedMeetingRoomList != null) {
      return loadedMeetingRoomList(state.mapMeetingChannelIds);
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function() loading,
    required T Function(List<Channel> channels, bool noMoreItems) loaded,
    required T Function(Channel channel) channelCreatedOrUpdated,
    required T Function(String workspaceId, String channelId) channelDeleted,
    required T Function(String? workspaceId, String? channelId, String? userId)
        callClearMessageAllForMe,
    required T Function(bool response) clearMessageAllForMe,
    required T Function(String? workspaceId, String? channelId, String? userId)
        callClearMessageAllForEveryone,
    required T Function(bool response) clearMessageAllForEveryone,
    required T Function() refreshClearMessage,
    required T Function(Map<String, String?> translatedMessages)
        translatedMessages,
    required T Function(Map<String, bool> mapMeetingChannelIds)
        loadedMeetingRoomList,
  }) {
    final state = this;

    if (state is ChannelsStateInitial) return initial();
    if (state is ChannelsStateLoading) return loading();
    if (state is ChannelsStateLoaded) {
      return loaded(state.channels, state.noMoreItems);
    }
    if (state is ChannelsStateChannelCreatedOrUpdated) {
      return channelCreatedOrUpdated(state.channel);
    }
    if (state is ChannelsStateChannelDeleted) {
      return channelDeleted(state.workspaceId, state.channelId);
    }
    if (state is ChannelsStateCallClearMessageAllForMe) {
      return callClearMessageAllForMe(
        state.workspaceId,
        state.channelId,
        state.userId,
      );
    }
    if (state is ChannelsStateClearMessageAllForMe) {
      return clearMessageAllForMe(state.response);
    }
    if (state is ChannelsStateCallClearMessageAllForEveryone) {
      return callClearMessageAllForEveryone(
        state.workspaceId,
        state.channelId,
        state.userId,
      );
    }
    if (state is ChannelsStateClearMessageAllForEveryone) {
      return clearMessageAllForEveryone(state.response);
    }
    if (state is ChannelsStateRefreshClearMessage) {
      return refreshClearMessage();
    }
    if (state is ChannelsStateTranslatedMessages) {
      return translatedMessages(state.translatedMessages);
    }

    if (state is LoadedMeetingRoomList) {
      return loadedMeetingRoomList(state.mapMeetingChannelIds);
    }

    throw StateError('Unhandled ChannelsState: $state');
  }
}
