import 'package:flutter/material.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

class FriendListSkeleton extends StatelessWidget {
  const FriendListSkeleton({super.key, this.itemCount = 9});

  final int itemCount;

  @override
  Widget build(BuildContext context) {
    return ui.AppShimmerEffect(
      child: Column(
        children: List.generate(
          itemCount,
          (index) => ui.FriendSkeletonWidget(),
        ),
      ),
    );
  }
}
