import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

class ChannelCircleStatusSkeleton extends StatelessWidget {
  const ChannelCircleStatusSkeleton({super.key, this.itemCount = 9});

  final int itemCount;

  @override
  Widget build(BuildContext context) {
    return ui.AppShimmerEffect(
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: itemCount,
        itemBuilder: (context, index) {
          return Padding(
            padding: EdgeInsets.only(left: index == 0 ? 20.w : 5.w, right: 5.w),
            child: ui.ChannelCircleAvatarSkeletonWidget(),
          );
        },
      ),
    );
  }
}
