import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../../chat.dart';
import '../../../data/repositories/database/enums/channel_type.dart';
import '../../channel_options/channel_options_bottom_sheet_interface.dart';

class ChannelOptionsImplement extends ChannelOptionsBottomSheetInterface {
  ChannelOptionsImplement({
    required this.context,
    required super.channelName,
    required this.channelId,
    required this.workspaceId,
    super.userId,
    super.avatarUrl,
    super.channelType = ChannelTypeEnum.BROADCAST,
  });

  final BuildContext context;
  final String channelId;
  final String workspaceId;

  @override
  void onChangeMuteChannel(bool value) {
    Log.d('[Channel options] onChangeMuteChannel: $value');
  }

  @override
  void onChangePinChannel(bool value) {
    Log.d('[Channel options] onChangePinChannel: $value');
  }

  @override
  void onTapDeleteChat() {
    Log.d('[Channel options] onTapDeleteChat');
  }

  @override
  void onTapInviteToChannel() {
    Navigator.pop(context);
    ui.BottomSheetUtil.showDefaultBottomSheet(
      context: context,
      child: InviteToChannelBottomSheet(
        channelId: channelId,
        workspaceId: workspaceId,
      ),
    );
  }

  @override
  void onTapReportUser({String? userId, String? name}) {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    }
    ui.BottomSheetUtil.showReportBottomSheet(
      context: context,
      onClickCancel: () {
        Navigator.pop(context);
      },
      onClickSubmit: (context, reason, pretending, other) {
        Navigator.pop(context);
        this.context.read<UserReportBloc>().add(
              OnUserReportEvent(
                userId: userId!,
                name: name,
                pretendingTo: pretending.name.split('.')[0],
                reportCategory: reason.name.split('.')[0],
                reason: other,
              ),
            );
      },
    );
  }
}
