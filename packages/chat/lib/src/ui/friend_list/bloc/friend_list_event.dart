part of 'friend_list_bloc.dart';

sealed class FriendListEvent extends BaseBlocEvent {
  const FriendListEvent();
}

@freezed
sealed class InitiateFriendListEvent extends FriendListEvent
    with _$InitiateFriendListEvent {
  const InitiateFriendListEvent._();
  factory InitiateFriendListEvent({
    @Default(100) int limit,
  }) = _InitiateFriendListEvent;
}

@freezed
sealed class LoadedEvent extends FriendListEvent with _$LoadedEvent {
  const LoadedEvent._();
  factory LoadedEvent({
    @Default([]) List<ChatUser> friends,
    @Default(0) int totalFriendRequest,
    @Default(false) bool noMoreItems,
  }) = _LoadedEvent;
}

@freezed
sealed class LoadMoreFriendsEvent extends FriendListEvent
    with _$LoadMoreFriendsEvent {
  const LoadMoreFriendsEvent._();
  factory LoadMoreFriendsEvent({
    required List<ChatUser> friends,
    required String nextPageToken,
  }) = _LoadMoreFriendsEvent;
}

@freezed
sealed class UnSubscriptionEvent extends FriendListEvent
    with _$UnSubscriptionEvent {
  const UnSubscriptionEvent._();
  factory UnSubscriptionEvent() = _UnSubscriptionEvent;
}
