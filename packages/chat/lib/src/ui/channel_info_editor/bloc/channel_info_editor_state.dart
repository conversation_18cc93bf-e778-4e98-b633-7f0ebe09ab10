part of 'channel_info_editor_bloc.dart';

@freezed
sealed class ChannelInfoEditorState extends BaseBlocState
    with _$ChannelInfoEditorState {
  const ChannelInfoEditorState._();

  factory ChannelInfoEditorState.initial() = ChannelInfoEditorStateInitial;

  factory ChannelInfoEditorState.loaded({required Channel channel}) =
      ChannelInfoEditorStateLoaded;

  factory ChannelInfoEditorState.updated({required Channel channel}) =
      ChannelInfoEditorStateUploaded;

  factory ChannelInfoEditorState.changeChannelName({
    required String channelName,
  }) = ChannelInfoEditorStateChangeChannelName;
}

extension ChannelInfoEditorStateX on ChannelInfoEditorState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function(Channel channel)? loaded,
    T Function(Channel channel)? updated,
    T Function(String channelName)? changeChannelName,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is ChannelInfoEditorStateInitial && initial != null) {
      return initial();
    }
    if (state is ChannelInfoEditorStateLoaded && loaded != null) {
      return loaded(state.channel);
    }
    if (state is ChannelInfoEditorStateUploaded && updated != null) {
      return updated(state.channel);
    }
    if (state is ChannelInfoEditorStateChangeChannelName &&
        changeChannelName != null) {
      return changeChannelName(state.channelName);
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function(Channel channel) loaded,
    required T Function(Channel channel) updated,
    required T Function(String channelName) changeChannelName,
  }) {
    final state = this;

    if (state is ChannelInfoEditorStateInitial) {
      return initial();
    }
    if (state is ChannelInfoEditorStateLoaded) {
      return loaded(state.channel);
    }
    if (state is ChannelInfoEditorStateUploaded) {
      return updated(state.channel);
    }
    if (state is ChannelInfoEditorStateChangeChannelName) {
      return changeChannelName(state.channelName);
    }

    throw StateError('Unhandled ChannelInfoEditorState: $state');
  }
}
