part of 'channel_info_editor_bloc.dart';

sealed class ChannelInfoEditorEvent extends BaseBlocEvent {
  const ChannelInfoEditorEvent();
}

@freezed
sealed class InitiateChannelInfoEditorEvent extends ChannelInfoEditorEvent
    with _$InitiateChannelInfoEditorEvent {
  const InitiateChannelInfoEditorEvent._();
  factory InitiateChannelInfoEditorEvent({
    required String workspaceId,
    required String channelId,
  }) = _InitiateChannelInfoEditorEvent;
}

class OnChannelUpdatedEvent extends ChannelInfoEditorEvent {
  const OnChannelUpdatedEvent(this.channel);

  final Channel? channel;
}

@freezed
sealed class ChangeChannelNameEvent extends ChannelInfoEditorEvent
    with _$ChangeChannelNameEvent {
  const ChangeChannelNameEvent._();
  factory ChangeChannelNameEvent({
    required String workspaceId,
    required String channelId,
    required String channelName,
  }) = _ChangeChannelNameEvent;
}

@freezed
sealed class DeleteChannelAvatarEditEvent extends ChannelInfoEditorEvent
    with _$DeleteChannelAvatarEditEvent {
  const DeleteChannelAvatarEditEvent._();
  factory DeleteChannelAvatarEditEvent({
    required String workspaceId,
    required String channelId,
  }) = _DeleteChannelAvatarEditEvent;
}
