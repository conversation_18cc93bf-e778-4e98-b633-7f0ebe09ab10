import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:sticker/sticker.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../message_list/constants.dart';
import '../../message_list/reaction_handler.dart';
import 'base/base_fullscreen_widget.dart';

class StickerFullscreenWidgetImpl extends BaseFullscreenWidget {
  StickerFullscreenWidgetImpl({
    required super.messageItem,
    required super.message,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final (listReactions, totalReactions) =
        ReactionHandler.getListReactionsFullScreen(message);

    final quickReact =
        (message.reactions?[heartEmoji['emoji'] as String]?.isReacted ?? false);

    return ViewFullScreenStickerWidget(
      interface: this,
      messageItem: messageItem,
      emojiList: listReactions,
      quickReact: quickReact,
      totalReactions: ValueNotifier(totalReactions),
      stickerWidget: StickerWidget.fromUrl(
        lottieUrl: UrlUtils.parseSticker(
          message.mediaAttachments.first.sticker!.stickerUrl,
        ),
        size: StickerSize.x512,
      ),
    );
  }
}
