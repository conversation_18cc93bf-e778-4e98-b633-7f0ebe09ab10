import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../chat.dart' as chat;
import '../../../common/di/di.dart';
import '../../../domain/usecase/chat_user/get_me_use_case.dart';
import '../../message_list/constants.dart';
import '../../message_list/reaction_handler.dart';
import 'base/base_fullscreen_widget.dart';

class TextFullscreenWidgetImpl extends BaseFullscreenWidget {
  TextFullscreenWidgetImpl({
    required super.messageItem,
    required super.message,
    required this.messageContent,
    this.quoteMessageItem,
    this.onQuoteMessageClicked,
    this.onButtonTranslateClick,
    this.hasShimmerEffect = false,
    super.key,
  });

  final String messageContent;
  final QuoteMessage? quoteMessageItem;
  final void Function()? onQuoteMessageClicked;
  final void Function(MessageItem messageItem)? onButtonTranslateClick;
  final bool hasShimmerEffect;

  @override
  Widget build(BuildContext context) {
    final (listReactions, totalReactions) =
        ReactionHandler.getListReactionsFullScreen(message);

    final quickReact =
        (message.reactions?[heartEmoji['emoji'] as String]?.isReacted ?? false);

    final translateButtonStatus =
        messageItem.translateButtonStatus != TranslateButtonStatus.active;

    return ViewFullScreenTextWidget(
      interface: this,
      messageItem: messageItem,
      messageContent: messageContent,
      emojiList: listReactions,
      hasShimmerEffect: hasShimmerEffect,
      translateButtonStatus: translateButtonStatus,
      totalReactions: ValueNotifier(totalReactions),
      quickReact: quickReact,
      onQuoteClicked: onQuoteMessageClicked,
      quoteMessage: quoteMessageItem,
      onUsernameClicked: (String username) {
        onUsernameClicked(context, username);
      },
      mentionRegex: GlobalConfig.mentionRegex,
      transitionStatus: messageItem.translateButtonStatus,
    );
  }

  @override
  void onTranslateButtonClicked() {
    onButtonTranslateClick?.call(messageItem);
  }

  void onUsernameClicked(BuildContext context, String mention) async {
    final outputMe = await getIt.get<GetMeUseCase>().execute(GetMeInput());

    final currentUser = outputMe.user;
    if (currentUser == null) return;

    if (mention == '@${currentUser.username}' || mention == '@all') {
      return;
    }

    LoadingOverlayHelper.showLoading(context);

    final outputUser = await getIt
        .get<chat.GetChatUserByUsernameUseCase>()
        .execute(chat.GetChatUserByUsernameInput(userName: mention));
    final mentionedUser = outputUser.user;
    LoadingOverlayHelper.hideLoading(context);

    if (mentionedUser == null) {
      DialogUtils.showAccountUnavailableDialog(
        context,
        onFirstAction: (dialogContext) => dialogContext.maybePop(),
      );
      return;
    }

    if (mentionedUser.blocked ?? false) {
      DialogUtils.showAccountUnavailableDialog(
        context,
        onFirstAction: (dialogContext) => dialogContext.maybePop(),
      );
      return;
    }

    AppEventBus.publish(OnGoToUserProfileEvent(username: mention));
  }
}
