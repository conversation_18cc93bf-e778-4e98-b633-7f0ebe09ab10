import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../data/repositories/extensions/location_datamap_extensions.dart';
import '../../../data/repositories/extensions/message_extension.dart';
import '../../message_list/constants.dart';
import '../../message_list/reaction_handler.dart';
import 'base/base_fullscreen_widget.dart';

class LocationFullscreenWidgetImpl extends BaseFullscreenWidget {
  LocationFullscreenWidgetImpl({
    required super.messageItem,
    required super.message,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (!message.hasLocationData) {
      // Todo handle location data empty
      return SizedBox(
        child: ColoredBox(color: Colors.red),
      );
    }

    final locationData = message.firstEmbed!.locationData!;

    final (listReactions, totalReactions) =
        ReactionHandler.getListReactionsFullScreen(message);

    final quickReact =
        (message.reactions?[heartEmoji['emoji'] as String]?.isReacted ?? false);

    void onClickLink(String link) {
      AppEventBus.publish(
        OnLinkClickedEvent(
          link: link,
          workspaceId: message.workspaceId,
          channelId: message.channelId,
          userId: message.userId,
          messageId: message.messageId,
        ),
      );
    }

    void onLocationClicked() {
      if (!message.hasLocationData) return;

      final locationData = message.firstEmbed!.locationData!;

      onClickLink(locationData.mapsLink);
    }

    return ViewFullScreenLocationWidget(
      interface: this,
      messageItem: messageItem,
      emojiList: listReactions,
      quickReact: quickReact,
      totalReactions: ValueNotifier(totalReactions),
      onLocationClicked: onLocationClicked,
      thumbnailUrl: locationData.thumbnailUrl ?? '',
      locationDescription: locationData.description ?? '',
    );
  }
}
