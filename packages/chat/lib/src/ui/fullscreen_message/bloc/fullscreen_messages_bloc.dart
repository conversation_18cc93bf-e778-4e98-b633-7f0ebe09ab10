import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';
import '../../../data/repositories/database/classes/reaction_data.dart';
import '../../../domain/usecase/message/load_messages_use_case.dart';
import '../../../domain/usecase/message/mark_as_read_use_case.dart';

part 'fullscreen_messages_bloc.freezed.dart';
part 'fullscreen_messages_event.dart';
part 'fullscreen_messages_state.dart';

@injectable
class FullscreenMessagesBloc
    extends Bloc<FullscreenMessagesEvent, FullscreenMessagesState> {
  FullscreenMessagesBloc(
    this._listAllMessagesUseCase,
    this._loadMessagesUseCase,
    this._messageRepository,
    this._getChannelUseCase,
    this._markAsReadUseCase,
    this._deleteMessagesForEveryOneUseCase,
    this._deleteMessagesForMeUseCase,
    this._localDeleteMessagesUseCase,
  ) : super(FullscreenMessagesState.initial()) {
    on<FullscreenMessagesEventInitiate>(_onInitiate);
    on<FullscreenMessagesEventLoadMore>(_onLoadMore);
    on<AddTempMessage>(_onAddTempMessage);
    on<AddMessage>(_onAddMessage);
    on<SaveTempMessage>(_onSaveTempMessage);
    on<UpdateMessage>(_onUpdateMessage);
    on<UpdateMessages>(_onUpdateMessages);
    on<Sync>(_onSyncMessage);
    on<MarkAsReadFullScreenMessageEvent>(_onMarkAsRead);
    on<ClearAllMessageEvent>(_onClearAllMessage);
    on<ClearMessageUnSubscription>(_onClearMessageUnSubscription);
    on<OnDeleteMessageForMeEvent>(_onDeleteMessageForMe);
    on<OnDeleteMessageForEveryOneEvent>(_onDeleteMessageForEveryOne);
    on<OnLocalDeleteMessageEvent>(_onLocalDeleteMessage);
    on<OnLoadPinUnPinMessage>(_onLoadPinUnPinMessage);
  }

  final DeleteMessagesForEveryoneUseCase _deleteMessagesForEveryOneUseCase;
  final LocalDeleteMessagesUseCase _localDeleteMessagesUseCase;
  final ListAllMessagesUseCase _listAllMessagesUseCase;
  final LoadMessagesUseCase _loadMessagesUseCase;
  final MessageRepository _messageRepository;
  final MarkAsReadUseCase _markAsReadUseCase;
  final DeleteMessagesForMeUseCase _deleteMessagesForMeUseCase;

  final GetChannelUseCase _getChannelUseCase;
  StreamSubscription? _clearMessageEventSubscription;
  StreamSubscription? _updateMessagesSubscription;
  StreamSubscription? _pinMessageEventSubscription;

  Future<void> _onInitiate(
    FullscreenMessagesEventInitiate event,
    Emitter<FullscreenMessagesState> emit,
  ) async {
    _updateMessagesSubscription = _messageRepository
        .getStreamMessagesFullScreen(
          workspaceId: event.workspaceId!,
          channelId: event.channelId!,
        )
        .listen(
          (messages) => add(
            FullscreenMessagesEvent.updateMessages(messages),
          ),
        );
    _listenPinMessageEvent(event.workspaceId!, event.channelId!);
  }

  void _onMarkAsRead(
    MarkAsReadFullScreenMessageEvent event,
    Emitter<FullscreenMessagesState> emit,
  ) {
    _markAsReadUseCase.execute(
      MarkAsReadInput(
        messageId: event.messageId,
        channelId: event.channelId,
        workspaceId: event.workspaceId,
        userId: event.userId,
      ),
    );
  }

  Future<void> _onLoadMore(
    FullscreenMessagesEventLoadMore event,
    Emitter<FullscreenMessagesState> emit,
  ) async {
    try {
      final output = await _listAllMessagesUseCase.execute(
        ListAllMessagesInput(
          workspaceId: event.workspaceId,
          channelId: event.channelId,
          userId: event.userId,
          limit: event.limit,
          nextPageToken: event.nextPageToken,
        ),
      );

      if (output.messages.isNotEmpty) {
        emit(
          FullscreenMessagesState.onLoadMore(
            messages: output.messages,
            hasNext: output.hasNext,
            nextPageToken: output.nextPageToken,
          ),
        );
        return;
      }

      final loadMessageOutput = await _loadMessagesUseCase.execute(
        LoadMessagesInput(
          workspaceId: event.workspaceId,
          channelId: event.channelId,
          userId: event.userId,
          nextPageToken: event.nextPageToken,
          limit: event.limit,
        ),
      );

      emit(
        FullscreenMessagesState.loaded(
          messages: loadMessageOutput.messages,
          hasNext: loadMessageOutput.hasNext,
          nextPageToken: loadMessageOutput.nextPageToken,
        ),
      );

      _updateLastMessageIsFirstFlag(loadMessageOutput);
      await _messageRepository.insertAll(loadMessageOutput.messages);
      if (!loadMessageOutput.hasNext) {
        _messageRepository.tagFirstMessage(
          workspaceId: event.workspaceId!,
          channelId: event.channelId!,
          messageId: loadMessageOutput.messages.isNotEmpty
              ? loadMessageOutput.messages.last.messageId
              : event.nextPageToken!,
        );
      }
    } catch (error) {
      emit(FullscreenMessagesState.error(error.toString()));
    }
  }

  void _updateLastMessageIsFirstFlag(LoadMessagesOutput output) {
    if (output.messages.isEmpty) return;

    output.messages.last.isFirstMessage = output.hasNext;
  }

  void _onAddTempMessage(
    AddTempMessage event,
    Emitter<FullscreenMessagesState> emit,
  ) {
    emit(FullscreenMessagesState.addTempMessage(event.message));
  }

  void _onAddMessage(AddMessage event, Emitter<FullscreenMessagesState> emit) {
    emit(FullscreenMessagesState.addMessage(event.message));
  }

  void _onUpdateMessage(
    UpdateMessage event,
    Emitter<FullscreenMessagesState> emit,
  ) {
    emit(FullscreenMessagesState.updateMessage(event.message));
  }

  void _onUpdateMessages(
    UpdateMessages event,
    Emitter<FullscreenMessagesState> emit,
  ) {
    emit(FullscreenMessagesState.updateMessages(event.messages));
  }

  FutureOr<void> _onSyncMessage(
    Sync event,
    Emitter<FullscreenMessagesState> emit,
  ) async {
    final loadMessageOutput = await _loadMessagesUseCase.execute(
      LoadMessagesInput(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        userId: event.userId,
        prevPageToken: event.prevPageToken,
        limit: 500,
      ),
    );

    if (loadMessageOutput.messages.isNotEmpty) {
      emit(
        FullscreenMessagesState.synced(
          messages: loadMessageOutput.messages,
        ),
      );

      if (loadMessageOutput.messages.isEmpty) return;

      await _messageRepository.insertAll(loadMessageOutput.messages);
    }
  }

  FutureOr<void> _onSaveTempMessage(
    SaveTempMessage event,
    Emitter<FullscreenMessagesState> emit,
  ) async {
    await _messageRepository.insert(event.message);
  }

  Channel? getChannel({
    String? workspaceId,
    String? channelId,
    String? userId,
  }) {
    return _getChannelUseCase
        .execute(
          GetChannelInput(
            workspaceId: workspaceId,
            channelId: channelId,
            userId: userId,
          ),
        )
        .channel;
  }

  Future<void> onReceivedFromClearMessageEvent(event) async {
    if (event is ClearMessageEvent) {
      add(
        FullscreenMessagesEvent.clearAllMessageEvent(
          workspaceId: event.workspaceId,
          channelId: event.channelId,
        ),
      );
    }
  }

  FutureOr<void> _onClearAllMessage(
    ClearAllMessageEvent event,
    Emitter<FullscreenMessagesState> emit,
  ) async {
    emit(
      FullscreenMessagesState.clearAllMessage(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
      ),
    );
  }

  FutureOr<void> _onClearMessageUnSubscription(
    ClearMessageUnSubscription event,
    Emitter<FullscreenMessagesState> emit,
  ) {
    if (_clearMessageEventSubscription != null) {
      _clearMessageEventSubscription?.cancel();
      _clearMessageEventSubscription = null;
    }
    _updateMessagesSubscription?.cancel();
    _pinMessageEventSubscription?.cancel();
  }

  FutureOr<void> _onDeleteMessageForMe(
    OnDeleteMessageForMeEvent event,
    Emitter<FullscreenMessagesState> emit,
  ) async {
    var delete = await _deleteMessagesForMeUseCase.execute(
      DeleteMessagesForMeInput(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        userId: event.userId,
        messageIds: event.messageIds ?? [],
      ),
    );
    if (delete.ok == true) {
      AppEventBus.publish(
        DeleteMessageEvent(
          workspaceId: event.workspaceId!,
          channelId: event.channelId!,
          messageIds: event.messageIds ?? [],
        ),
      );
      await _localDeleteMessagesUseCase.execute(
        LocalDeleteMessagesInput(
          workspaceId: event.workspaceId!,
          channelId: event.channelId!,
          messageIds: event.messageIds ?? [],
        ),
      );
    }
    emit(FullscreenMessagesState.deleteMessage(response: delete.ok));
  }

  FutureOr<void> _onDeleteMessageForEveryOne(
    OnDeleteMessageForEveryOneEvent event,
    Emitter<FullscreenMessagesState> emit,
  ) async {
    var delete = await _deleteMessagesForEveryOneUseCase.execute(
      DeleteMessagesForEveryoneInput(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        userId: event.userId,
        messageIds: event.messageIds ?? [],
      ),
    );
    if (delete.ok == true) {
      AppEventBus.publish(
        DeleteMessageEvent(
          workspaceId: event.workspaceId!,
          channelId: event.channelId!,
          messageIds: event.messageIds ?? [],
        ),
      );
      await _localDeleteMessagesUseCase.execute(
        LocalDeleteMessagesInput(
          workspaceId: event.workspaceId!,
          channelId: event.channelId!,
          messageIds: event.messageIds ?? [],
        ),
      );
    }
    emit(FullscreenMessagesState.deleteMessage(response: delete.ok));
  }

  FutureOr<void> _onLocalDeleteMessage(
    OnLocalDeleteMessageEvent event,
    Emitter<FullscreenMessagesState> emit,
  ) async {
    var workspaceId = event.workspaceId;
    var channelId = event.channelId;
    if (event.userId != null) {
      var channel = getChannel(userId: event.userId);
      workspaceId = channel?.workspaceId;
      channelId = channel?.channelId;
    }
    AppEventBus.publish(
      DeleteMessageEvent(
        workspaceId: workspaceId!,
        channelId: channelId!,
        messageIds: event.messageIds ?? [],
      ),
    );
    await _localDeleteMessagesUseCase.execute(
      LocalDeleteMessagesInput(
        workspaceId: workspaceId,
        channelId: channelId,
        messageIds: event.messageIds ?? [],
      ),
    );
  }

  void _listenPinMessageEvent(
    String workspaceId,
    String channelId,
  ) async {
    _pinMessageEventSubscription?.cancel();
    _pinMessageEventSubscription ??= _messageRepository
        .streamPinMessages(workspaceId: workspaceId, channelId: channelId)
        .listen((messages) {
      add(FullscreenMessagesEvent.OnLoadPinUnPinMessage(messages));
    });
  }

  void _onLoadPinUnPinMessage(
    OnLoadPinUnPinMessage event,
    Emitter<FullscreenMessagesState> emit,
  ) {
    emit(FullscreenMessagesState.loadPinUnPinUpdateMessage(event.messages));
  }
}
