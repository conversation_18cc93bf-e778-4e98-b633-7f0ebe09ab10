part of 'fullscreen_messages_bloc.dart';

@freezed
sealed class FullscreenMessagesState extends BaseBlocState
    with _$FullscreenMessagesState {
  const FullscreenMessagesState._();

  factory FullscreenMessagesState.initial() = FullscreenMessagesStateInitial;

  factory FullscreenMessagesState.waiting() = FullscreenMessagesStateWaiting;

  factory FullscreenMessagesState.loaded({
    required List<Message> messages,
    required bool hasNext,
    String? nextPageToken,
  }) = FullscreenMessagesStateLoaded;

  factory FullscreenMessagesState.onLoadMore({
    required List<Message> messages,
    required bool hasNext,
    String? nextPageToken,
  }) = FullscreenMessagesStateOnLoadMore;

  factory FullscreenMessagesState.synced({
    required List<Message> messages,
  }) = Synced;

  factory FullscreenMessagesState.addTempMessage(Message message) =
      FullscreenMessagesStateAddTempMessage;

  factory FullscreenMessagesState.addMessage(Message message) =
      FullscreenMessagesStateAddMessage;

  factory FullscreenMessagesState.updateMessage(Message message) =
      FullscreenMessagesStateUpdateMessage;

  factory FullscreenMessagesState.updateMessages(List<Message> messages) =
      FullscreenMessagesStateUpdateMessages;

  factory FullscreenMessagesState.error(String error) =
      FullscreenMessagesStateError;

  factory FullscreenMessagesState.clearAllMessage({
    String? workspaceId,
    String? channelId,
  }) = FullscreenMessagesStateClearAllMessage;

  factory FullscreenMessagesState.deleteMessage({
    required bool response,
  }) = FullscreenMessagesStateDeleteMessage;

  factory FullscreenMessagesState.loadPinUnPinUpdateMessage(
    List<Message> message,
  ) = LoadPinUnPinUpdateMessage;
}

extension FullscreenMessagesStateX on FullscreenMessagesState {
  T when<T>({
    required T Function() initial,
    required T Function() waiting,
    required T Function(
      List<Message> messages,
      bool hasNext,
      String? nextPageToken,
    ) loaded,
    required T Function(
      List<Message> messages,
      bool hasNext,
      String? nextPageToken,
    ) onLoadMore,
    required T Function(List<Message> messages) synced,
    required T Function(Message message) addTempMessage,
    required T Function(Message message) addMessage,
    required T Function(Message message) updateMessage,
    required T Function(List<Message> messages) updateMessages,
    required T Function(String error) error,
    required T Function(String? workspaceId, String? channelId) clearAllMessage,
    required T Function(bool response) deleteMessage,
    required T Function(List<Message> message) loadPinUnPinUpdateMessage,
  }) {
    final state = this;

    return switch (state) {
      FullscreenMessagesStateInitial() => initial(),
      FullscreenMessagesStateWaiting() => waiting(),
      FullscreenMessagesStateLoaded(
        :final messages,
        :final hasNext,
        :final nextPageToken
      ) =>
        loaded(messages, hasNext, nextPageToken),
      FullscreenMessagesStateOnLoadMore(
        :final messages,
        :final hasNext,
        :final nextPageToken
      ) =>
        onLoadMore(messages, hasNext, nextPageToken),
      Synced(:final messages) => synced(messages),
      FullscreenMessagesStateAddTempMessage(:final message) =>
        addTempMessage(message),
      FullscreenMessagesStateAddMessage(:final message) => addMessage(message),
      FullscreenMessagesStateUpdateMessage(:final message) =>
        updateMessage(message),
      FullscreenMessagesStateUpdateMessages(:final messages) =>
        updateMessages(messages),
      FullscreenMessagesStateError(error: final e) => error(e),
      FullscreenMessagesStateClearAllMessage(
        :final workspaceId,
        :final channelId
      ) =>
        clearAllMessage(workspaceId, channelId),
      FullscreenMessagesStateDeleteMessage(:final response) =>
        deleteMessage(response),
      LoadPinUnPinUpdateMessage(:final message) =>
        loadPinUnPinUpdateMessage(message),
    };
  }

  T maybeWhen<T>({
    T Function()? initial,
    T Function()? waiting,
    T Function(List<Message> messages, bool hasNext, String? nextPageToken)?
        loaded,
    T Function(List<Message> messages, bool hasNext, String? nextPageToken)?
        onLoadMore,
    T Function(List<Message> messages)? synced,
    T Function(Message message)? addTempMessage,
    T Function(Message message)? addMessage,
    T Function(Message message)? updateMessage,
    T Function(List<Message> messages)? updateMessages,
    T Function(String error)? error,
    T Function(String? workspaceId, String? channelId)? clearAllMessage,
    T Function(bool response)? deleteMessage,
    T Function(List<Message> message)? loadPinUnPinUpdateMessage,
    required T Function() orElse,
  }) {
    final state = this;

    return switch (state) {
      FullscreenMessagesStateInitial() when initial != null => initial(),
      FullscreenMessagesStateWaiting() when waiting != null => waiting(),
      FullscreenMessagesStateLoaded(
        :final messages,
        :final hasNext,
        :final nextPageToken
      )
          when loaded != null =>
        loaded(messages, hasNext, nextPageToken),
      FullscreenMessagesStateOnLoadMore(
        :final messages,
        :final hasNext,
        :final nextPageToken
      )
          when onLoadMore != null =>
        onLoadMore(messages, hasNext, nextPageToken),
      Synced(:final messages) when synced != null => synced(messages),
      FullscreenMessagesStateAddTempMessage(:final message)
          when addTempMessage != null =>
        addTempMessage(message),
      FullscreenMessagesStateAddMessage(:final message)
          when addMessage != null =>
        addMessage(message),
      FullscreenMessagesStateUpdateMessage(:final message)
          when updateMessage != null =>
        updateMessage(message),
      FullscreenMessagesStateUpdateMessages(:final messages)
          when updateMessages != null =>
        updateMessages(messages),
      FullscreenMessagesStateError(error: final e) when error != null =>
        error(e),
      FullscreenMessagesStateClearAllMessage(
        :final workspaceId,
        :final channelId
      )
          when clearAllMessage != null =>
        clearAllMessage(workspaceId, channelId),
      FullscreenMessagesStateDeleteMessage(:final response)
          when deleteMessage != null =>
        deleteMessage(response),
      LoadPinUnPinUpdateMessage(:final message)
          when loadPinUnPinUpdateMessage != null =>
        loadPinUnPinUpdateMessage(message),
      _ => orElse(),
    };
  }
}
