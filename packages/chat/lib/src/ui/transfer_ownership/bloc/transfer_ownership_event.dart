part of 'transfer_ownership_bloc.dart';

sealed class TransferOwnershipEvent extends BaseBlocEvent {
  const TransferOwnershipEvent();
}

@freezed
sealed class InitiateTransferOwnershipEvent extends TransferOwnershipEvent
    with _$InitiateTransferOwnershipEvent {
  const InitiateTransferOwnershipEvent._();
  factory InitiateTransferOwnershipEvent({
    required String channelId,
    required String workspaceId,
  }) = _InitiateTransferOwnershipEvent;
}

@freezed
sealed class HandleTransferOwnershipEvent extends TransferOwnershipEvent
    with _$HandleTransferOwnershipEvent {
  const HandleTransferOwnershipEvent._();
  factory HandleTransferOwnershipEvent({
    required String channelId,
    required String workspaceId,
    required String userId,
    required bool isTransferAndLeave,
  }) = _TransferOwmerShip;
}
