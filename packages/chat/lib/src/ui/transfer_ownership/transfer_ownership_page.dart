import 'package:app_core/core.dart';
import 'package:dartx/dartx.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../chat.dart';
import '../../data/repositories/database/enums/user_badge_enum.dart';
import '../member_settings/member_settings_handler.dart';
import '../member_settings/model_member_setting_profile.dart';
import 'bloc/transfer_ownership_bloc.dart';

class TransferOwnershipPage extends StatefulWidget {
  const TransferOwnershipPage({
    super.key,
    required this.isTransferAndLeave,
    required this.channelId,
    required this.workspaceId,
    this.onBack,
  });

  final bool isTransferAndLeave;
  final String channelId;
  final String workspaceId;
  final VoidCallback? onBack;

  @override
  State<TransferOwnershipPage> createState() => _TransferOwnershipPageState();
}

class _TransferOwnershipPageState
    extends BasePageState<TransferOwnershipPage, TransferOwnershipBloc>
    implements ui.TransferOwnershipPageInterface {
  Map<String, Member> _members = {};
  Map<String, ChatUser> _users = {};
  Map<String, UserPrivateData> _userPrivateDataMap = {};

  @override
  void initState() {
    super.initState();
    bloc.add(
      InitiateTransferOwnershipEvent(
        channelId: widget.channelId,
        workspaceId: widget.workspaceId,
      ),
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocListener<TransferOwnershipBloc, TransferOwnershipState>(
      listener: (context, state) {
        state.when(
          initial: () {},
          loaded: (members, users, privateData) {
            setState(() {
              _members = members;
              _users = users;
              _userPrivateDataMap = privateData;
            });
          },
          processing: () {
            LoadingOverlayHelper.showLoading(context);
          },
          transferred: () {
            LoadingOverlayHelper.hideLoading(context);
            if (widget.isTransferAndLeave) {
              AppEventBus.publish(OnGoToHomeEvent());
            } else {
              onClickBack();
            }
          },
          error: (String errorMessage) {
            LoadingOverlayHelper.hideLoading(context);
            if (errorMessage.isEmpty || !kDebugMode) return;
            SnackBarOverlayHelper().showSnackBar(
              widgetBuilder: (T) {
                return ui.SnackBarUtilV2.showFloatingSnackBar(
                  context: context,
                  content: errorMessage,
                  snackBarType: ui.SnackBarType.danger,
                );
              },
            );
          },
        );
      },
      child: ui.TransferOwnershipPage(interface: this),
    );
  }

  @override
  List<ui.ItemMemberSettingProfile> listMemberToTransfer() {
    final memberProfiles = _members.values.map(
      (member) {
        final user = _users[member.userId]!;
        final badgeEnum = user.profile?.userBadgeType ?? 0;
        final userBadgeType =
            UserBadgeEnumExtension.getEnumByValue(badgeEnum).toUserBadgeType();
        final profile = ModelMemberSettingProfile(
          userId: member.userId,
          userBadgeType: userBadgeType,
          workspaceId: member.workspaceId,
          channelId: member.channelId,
          username: user.username ?? '',
          aliasName: _userPrivateDataMap[member.userId]?.aliasName,
          displayName: user.profile?.displayName,
          nickname: user.username == GlobalConfig.ghost ? '' : member.nickname,
          role: MemberSettingsHandler.getRoleFromName(member.role),
          avatarUrl:
              UrlUtils.parseAvatar(_users[member.userId]!.profile?.avatar),
          isOnline: false,
        );
        return profile;
      },
    ).filter((profile) => profile.username != GlobalConfig.ghost);
    return memberProfiles
        .map((profile) => profile.toItemMemberSettingProfile())
        .toList();
  }

  @override
  void onClickBack() {
    widget.onBack?.call();
  }

  @override
  void onClickClose(BuildContext context) {
    onClickBack();
  }

  @override
  void onClickTransfer(
    BuildContext context,
    ui.ItemMemberSettingProfile member,
  ) {
    bloc.add(
      HandleTransferOwnershipEvent(
        channelId: widget.channelId,
        workspaceId: widget.workspaceId,
        userId: member.userId,
        isTransferAndLeave: widget.isTransferAndLeave,
      ),
    );
  }
}
