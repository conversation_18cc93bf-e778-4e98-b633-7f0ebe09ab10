part of 'translate_to_bloc.dart';

@freezed
sealed class TranslateToState extends BaseBlocState with _$TranslateToState {
  const TranslateToState._();

  factory TranslateToState.initial() = TranslateToStateInitial;

  factory TranslateToState.loading() = TranslateToStateLoading;

  factory TranslateToState.loaded({
    ChannelLocalMetadata? metadata,
    @Default([]) List<TranslatedResult> translatedResults,
  }) = TranslateToStateLoaded;

  factory TranslateToState.metaDataDeleted() = TranslateToStateMetaDataDeleted;

  factory TranslateToState.error(String message) = TranslateToStateError;
}

extension TranslateToStateX on TranslateToState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function()? loading,
    T Function(ChannelLocalMetadata? metadata, List<TranslatedResult> results)?
        loaded,
    T Function()? metaDataDeleted,
    T Function(String message)? error,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is TranslateToStateInitial && initial != null) return initial();
    if (state is TranslateToStateLoading && loading != null) return loading();
    if (state is TranslateToStateLoaded && loaded != null) {
      return loaded(state.metadata, state.translatedResults);
    }
    if (state is TranslateToStateMetaDataDeleted && metaDataDeleted != null) {
      return metaDataDeleted();
    }
    if (state is TranslateToStateError && error != null) {
      return error(state.message);
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function() loading,
    required T Function(
      ChannelLocalMetadata? metadata,
      List<TranslatedResult> results,
    ) loaded,
    required T Function() metaDataDeleted,
    required T Function(String message) error,
  }) {
    final state = this;

    if (state is TranslateToStateInitial) return initial();
    if (state is TranslateToStateLoading) return loading();
    if (state is TranslateToStateLoaded)
      return loaded(state.metadata, state.translatedResults);
    if (state is TranslateToStateMetaDataDeleted) return metaDataDeleted();
    if (state is TranslateToStateError) return error(state.message);

    throw StateError('Unhandled state: $state');
  }
}
