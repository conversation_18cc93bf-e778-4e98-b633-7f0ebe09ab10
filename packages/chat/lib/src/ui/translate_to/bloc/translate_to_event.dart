part of 'translate_to_bloc.dart';

/// Event gốc của TranslateToBloc.
sealed class TranslateToEvent extends BaseBlocEvent {
  const TranslateToEvent._();
  const TranslateToEvent();
}

// ----------------------------------------------------------------------
// Ví dụ: Event khởi tạo, tuân thủ cấu trúc tương tự `InitiateChannelInfoEvent`
// ----------------------------------------------------------------------
@freezed
sealed class InitiateTranslateToEvent extends TranslateToEvent
    with _$InitiateTranslateToEvent {
  const InitiateTranslateToEvent._();
  factory InitiateTranslateToEvent({
    required String? workspaceId,
    required String? channelId,
  }) = _InitiateTranslateToEvent;
}

// ----------------------------------------------------------------------
// Ví dụ: Sự kiện ChannelLocalMetadata đã thay đổi
// ----------------------------------------------------------------------
@freezed
sealed class ChannelLocalMetadataChangedEvent extends TranslateToEvent
    with _$ChannelLocalMetadataChangedEvent {
  const ChannelLocalMetadataChangedEvent._();
  factory ChannelLocalMetadataChangedEvent({
    ChannelLocalMetadata? metadata,
  }) = _ChannelLocalMetadataChangedEvent;
}

// ----------------------------------------------------------------------
// Ví dụ: Sự kiện danh sách TranslatedResult đã thay đổi
// ----------------------------------------------------------------------
@freezed
sealed class TranslatedResultsChangedEvent extends TranslateToEvent
    with _$TranslatedResultsChangedEvent {
  const TranslatedResultsChangedEvent._();
  factory TranslatedResultsChangedEvent({
    @Default([]) List<TranslatedResult> results,
  }) = _TranslatedResultsChangedEvent;
}

// ----------------------------------------------------------------------
// CRUD cho ChannelLocalMetadata
// ----------------------------------------------------------------------
@freezed
sealed class InsertOrUpdateChannelLocalMetadataEvent extends TranslateToEvent
    with _$InsertOrUpdateChannelLocalMetadataEvent {
  const InsertOrUpdateChannelLocalMetadataEvent._();
  factory InsertOrUpdateChannelLocalMetadataEvent({
    required ChannelLocalMetadata metadata,
  }) = _InsertOrUpdateChannelLocalMetadataEvent;
}

@freezed
sealed class DeleteChannelLocalMetadataEvent extends TranslateToEvent
    with _$DeleteChannelLocalMetadataEvent {
  const DeleteChannelLocalMetadataEvent._();
  factory DeleteChannelLocalMetadataEvent({
    required String workspaceId,
    required String channelId,
  }) = _DeleteChannelLocalMetadataEvent;
}

// ----------------------------------------------------------------------
// CRUD cho TranslatedResult
// ----------------------------------------------------------------------
@freezed
sealed class InsertOrUpdateTranslatedResultEvent extends TranslateToEvent
    with _$InsertOrUpdateTranslatedResultEvent {
  const InsertOrUpdateTranslatedResultEvent._();
  factory InsertOrUpdateTranslatedResultEvent({
    required TranslatedResult result,
  }) = _InsertOrUpdateTranslatedResultEvent;
}

@freezed
sealed class InsertOrUpdateTranslatedResultEventTimeout extends TranslateToEvent
    with _$InsertOrUpdateTranslatedResultEventTimeout {
  const InsertOrUpdateTranslatedResultEventTimeout._();
  factory InsertOrUpdateTranslatedResultEventTimeout({
    required TranslatedResult result,
    required Duration duration,
  }) = _InsertOrUpdateTranslatedResultEventTimeout;
}

@freezed
sealed class DeleteTranslatedResultEvent extends TranslateToEvent
    with _$DeleteTranslatedResultEvent {
  const DeleteTranslatedResultEvent._();
  factory DeleteTranslatedResultEvent({
    required String workspaceId,
    required String channelId,
    required String messageId,
    String? sessionKey,
  }) = _DeleteTranslatedResultEvent;
}

@freezed
sealed class CancelTranslatedLastMessageEvent extends TranslateToEvent
    with _$CancelTranslatedLastMessageEvent {
  const CancelTranslatedLastMessageEvent._();
  factory CancelTranslatedLastMessageEvent({
    required String workspaceId,
    required String channelId,
    required String messageId,
    String? sessionKey,
  }) = _CancelTranslatedLastMessageEvent;
}

// ----------------------------------------------------------------------
// Ví dụ: Event báo lỗi chung (tương tự OnErrorEvent)
// ----------------------------------------------------------------------
@freezed
sealed class OnTranslateErrorEvent extends TranslateToEvent
    with _$OnTranslateErrorEvent {
  const OnTranslateErrorEvent._();
  factory OnTranslateErrorEvent({
    required String errorMessage,
  }) = _OnTranslateErrorEvent;
}

// ----------------------------------------------------------------------
// Ví dụ: Event thường không cần @freezed, vẫn được
// ----------------------------------------------------------------------
class SomethingHappenedEvent extends TranslateToEvent {
  const SomethingHappenedEvent({
    required this.detail,
  });

  final String detail;
}
