class InvitableUser {
  InvitableUser({
    required this.userId,
    required this.username,
    this.avatar,
    this.displayName,
    this.aliasName,
  });

  final String userId;
  final String username;
  final String? avatar;
  late String? aliasName;
  final String? displayName;

  String get name => aliasName?.isNotEmpty == true
      ? aliasName!
      : displayName?.isNotEmpty == true
          ? displayName!
          : username;

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "username": username,
        "aliasName": aliasName,
        "displayName": displayName,
      };
}
