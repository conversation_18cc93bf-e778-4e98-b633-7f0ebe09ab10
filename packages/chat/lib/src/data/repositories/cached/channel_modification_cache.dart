import 'package:cached_annotation/cached_annotation.dart';

part 'channel_modification_cache.cached.dart';

@WithCache(useStaticCache: true)
abstract mixin class ChannelModificationCache
    implements _$ChannelModificationCache {
  factory ChannelModificationCache() = _ChannelModificationCache;

  @Cached(
    syncWrite: true,
    ttl: 180,
    limit: 100,
  )
  Future<String> setCache({
    required String workspaceId,
    required String channelId,
    @ignore required String updateTime,
  }) async {
    return Future.value(updateTime);
  }

  @CachePeek("setCache")
  String? peekCached({
    required String workspaceId,
    required String channelId,
  });

  @ClearCached("setCache")
  void cleanCache();
}
