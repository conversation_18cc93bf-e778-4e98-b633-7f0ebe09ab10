/// translate_to_repository_impl.dart
import 'dart:async';

import 'package:injectable/injectable.dart' hide Order;

import '../../../chat.dart'; // Ví dụ: Chứa class ChatStore, Config
import 'database/database.dart';
import 'database/entities/channel_local_metadata.dart';
import 'database/entities/translated_result.dart';
import 'database/enums/translated_status_enum.dart';
import 'database/generated/objectbox.g.dart';
import 'translate_to_repository.dart';

@LazySingleton(as: TranslateToRepository)
class TranslateToRepositoryImpl implements TranslateToRepository {
  TranslateToRepositoryImpl(this._chatStore);

  final ChatStore _chatStore;

  Box<ChannelLocalMetadata> get _metaBox =>
      _chatStore.box<ChannelLocalMetadata>();

  Box<TranslatedResult> get _resultBox => _chatStore.box<TranslatedResult>();

  // <PERSON><PERSON> trường hợp bạn có sessionKey active
  String get _sessionKey => Config.getInstance().activeSessionKey ?? '';

  // --------------------------------------------------
  // A. NHÓM PHƯƠNG THỨC CHO ChannelLocalMetadata
  // --------------------------------------------------

  @override
  ChannelLocalMetadata? getChannelLocalMetadata({
    required String workspaceId,
    required String channelId,
    String? sessionKey,
  }) {
    final sKey = sessionKey ?? _sessionKey;
    final query = _metaBox
        .query(
          ChannelLocalMetadata_.workspaceId
              .equals(workspaceId)
              .and(ChannelLocalMetadata_.channelId.equals(channelId))
              .and(ChannelLocalMetadata_.sessionKey.equals(sKey)),
        )
        .build();
    final metadata = query.findFirst();
    query.close();
    return metadata;
  }

  @override
  int insertOrUpdateChannelLocalMetadata(ChannelLocalMetadata metadata) {
    // Kiểm tra đã có chưa
    final existing = getChannelLocalMetadata(
      workspaceId: metadata.workspaceId,
      channelId: metadata.channelId,
      sessionKey: metadata.sessionKey,
    );
    if (existing != null) {
      // Copy ID để cập nhật
      metadata.id = existing.id;
    }
    return _metaBox.put(metadata);
  }

  @override
  bool deleteChannelLocalMetadata({
    required String workspaceId,
    required String channelId,
    String? sessionKey,
  }) {
    final sKey = sessionKey ?? _sessionKey;

    final query = _metaBox
        .query(
          ChannelLocalMetadata_.workspaceId
              .equals(workspaceId)
              .and(ChannelLocalMetadata_.channelId.equals(channelId))
              .and(ChannelLocalMetadata_.sessionKey.equals(sKey)),
        )
        .build();

    final existing = query.findFirst();
    if (existing != null) {
      final success = _metaBox.remove(existing.id);
      query.close();
      return success;
    }
    query.close();
    return false;
  }

  @override
  int deleteAllLocalMetadataOfSession(String sessionKey) {
    final query = _metaBox
        .query(ChannelLocalMetadata_.sessionKey.equals(sessionKey))
        .build();

    final count = query.remove();
    query.close();
    return count;
  }

  @override
  StreamSubscription observerChannelLocalMetadata(
    String workspaceId,
    String channelId,
    void Function(ChannelLocalMetadata? metadata) listener,
  ) {
    // Tạo query
    final watchedQuery = _metaBox
        .query(
          ChannelLocalMetadata_.workspaceId
              .equals(workspaceId)
              .and(ChannelLocalMetadata_.channelId.equals(channelId))
              .and(ChannelLocalMetadata_.sessionKey.equals(_sessionKey)),
        )
        .watch(triggerImmediately: true);

    // Lắng nghe
    return watchedQuery.listen((query) {
      final metadata = query.findFirst();
      listener(metadata);
    });
  }

  // --------------------------------------------------
  // B. NHÓM PHƯƠNG THỨC CHO TranslatedResult
  // --------------------------------------------------

  @override
  TranslatedResult? getTranslatedResult({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required String sessionKey,
  }) {
    final query = _resultBox
        .query(
          TranslatedResult_.workspaceId
              .equals(workspaceId)
              .and(TranslatedResult_.channelId.equals(channelId))
              .and(TranslatedResult_.messageId.equals(messageId))
              .and(TranslatedResult_.sessionKey.equals(sessionKey)),
        )
        .build();

    final result = query.findFirst();
    query.close();
    return result;
  }

  @override
  int insertOrUpdateTranslatedResult(TranslatedResult translatedResult) {
    final existing = getTranslatedResult(
      workspaceId: translatedResult.workspaceId,
      channelId: translatedResult.channelId,
      messageId: translatedResult.messageId,
      sessionKey: translatedResult.sessionKey,
    );
    if (existing != null) {
      translatedResult.id = existing.id;
    }
    return _resultBox.put(translatedResult);
  }

  @override
  Future<List<int>> insertOrUpdateAllTranslatedResults(
    List<TranslatedResult> translatedResults,
  ) async {
    // 1. Tìm các bản ghi đã tồn tại
    _checkAndUpdateExisting(translatedResults);
    // 2. Chèn/cập nhật
    return _resultBox.putMany(translatedResults);
  }

  /// Hàm nội bộ để cập nhật ID cho những bản ghi đã tồn tại
  void _checkAndUpdateExisting(List<TranslatedResult> translatedResults) {
    if (translatedResults.isEmpty) return;

    final sessionKey = translatedResults.first.sessionKey;
    final workspaceIds =
        translatedResults.map((e) => e.workspaceId).toSet().toList();
    final channelIds =
        translatedResults.map((e) => e.channelId).toSet().toList();
    final messageIds =
        translatedResults.map((e) => e.messageId).toSet().toList();

    final query = _resultBox
        .query(
          TranslatedResult_.sessionKey.equals(sessionKey).and(
                TranslatedResult_.workspaceId.oneOf(workspaceIds).and(
                      TranslatedResult_.channelId.oneOf(channelIds).and(
                            TranslatedResult_.messageId.oneOf(messageIds),
                          ),
                    ),
              ),
        )
        .build();

    final existingList = query.find();
    query.close();

    for (final existing in existingList) {
      final match = translatedResults.firstWhere(
        (e) =>
            e.workspaceId == existing.workspaceId &&
            e.channelId == existing.channelId &&
            e.messageId == existing.messageId &&
            e.sessionKey == existing.sessionKey,
        orElse: () => TranslatedResult(
          workspaceId: '',
          channelId: '',
          messageId: '',
          sessionKey: '',
          originalContent: '',
          translatedContent: '',
          originalLanguage: '',
          targetLanguage: '',
        ),
      );
      if (match.workspaceId.isNotEmpty) {
        match.id = existing.id;
      }
    }
  }

  @override
  bool deleteTranslatedResult({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required String sessionKey,
  }) {
    final query = _resultBox
        .query(
          TranslatedResult_.workspaceId
              .equals(workspaceId)
              .and(TranslatedResult_.channelId.equals(channelId))
              .and(TranslatedResult_.messageId.equals(messageId))
              .and(TranslatedResult_.sessionKey.equals(sessionKey)),
        )
        .build();

    final existing = query.findFirst();
    if (existing != null) {
      // Xoá bản ghi
      final success = _resultBox.remove(existing.id);
      query.close();
      return success;
    }
    query.close();
    return false;
  }

  @override
  int deleteAllTranslatedResultsInChannel({
    required String workspaceId,
    required String channelId,
    required String sessionKey,
  }) {
    final query = _resultBox
        .query(
          TranslatedResult_.workspaceId
              .equals(workspaceId)
              .and(TranslatedResult_.channelId.equals(channelId))
              .and(TranslatedResult_.sessionKey.equals(sessionKey)),
        )
        .build();

    final count = query.remove();
    query.close();
    return count;
  }

  @override
  int deleteAllTranslatedResultsOfSession(String sessionKey) {
    final query = _resultBox
        .query(TranslatedResult_.sessionKey.equals(sessionKey))
        .build();

    final count = query.remove();
    query.close();
    return count;
  }

  @override
  bool isEmptyTranslatedResult({required String sessionKey}) {
    final query = _resultBox
        .query(TranslatedResult_.sessionKey.equals(sessionKey))
        .build();
    final count = query.count();
    query.close();
    return count == 0;
  }

  @override
  bool updateTranslationStatus({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required String sessionKey,
    required int newStatusValue,
  }) {
    final query = _resultBox
        .query(
          TranslatedResult_.workspaceId
              .equals(workspaceId)
              .and(TranslatedResult_.channelId.equals(channelId))
              .and(TranslatedResult_.messageId.equals(messageId))
              .and(TranslatedResult_.sessionKey.equals(sessionKey)),
        )
        .build();

    final existing = query.findFirst();
    if (existing != null) {
      existing.statusRaw = newStatusValue;
      _resultBox.put(existing);
      query.close();
      return true;
    }
    query.close();
    return false;
  }

  @override
  StreamSubscription observerTranslatedResult(
    String workspaceId,
    String channelId,
    String messageId,
    String sessionKey,
    void Function(TranslatedResult? result) listener,
  ) {
    final watchedQuery = _resultBox
        .query(
          TranslatedResult_.workspaceId
              .equals(workspaceId)
              .and(TranslatedResult_.channelId.equals(channelId))
              .and(TranslatedResult_.messageId.equals(messageId))
              .and(TranslatedResult_.sessionKey.equals(sessionKey)),
        )
        .watch(triggerImmediately: true);

    return watchedQuery.listen((query) {
      final result = query.findFirst();
      listener(result);
    });
  }

  @override
  StreamSubscription observerAllTranslatedResultsInChannel(
    String workspaceId,
    String channelId,
    String sessionKey,
    void Function(List<TranslatedResult> results) listener,
  ) {
    final watchedQuery = _resultBox
        .query(
          TranslatedResult_.workspaceId
              .equals(workspaceId)
              .and(TranslatedResult_.channelId.equals(channelId))
              .and(TranslatedResult_.sessionKey.equals(sessionKey)),
        )
        .watch(triggerImmediately: true);

    return watchedQuery.listen((query) {
      final results = query.find();
      listener(results);
    });
  }

  @override
  StreamSubscription observerTranslatedResultsByMessageIds(
    Set<String> messageIds,
    String sessionKey,
    void Function(List<TranslatedResult> results) listener,
  ) {
    final watchedQuery = _resultBox
        .query(
          TranslatedResult_.sessionKey
              .equals(sessionKey)
              .and(TranslatedResult_.messageId.oneOf(messageIds.toList())),
        )
        .watch(triggerImmediately: true);

    return watchedQuery.listen((query) {
      final results = query.find();
      listener(results);
    });
  }

  @override
  List<TranslatedResult> translatingMessages(String sessionKey) {
    final query = _resultBox
        .query(
          TranslatedResult_.sessionKey.equals(sessionKey).and(
                TranslatedResult_.statusRaw
                    .equals(TranslatedStatusEnum.TRANSLATING.value),
              ),
        )
        .build();

    final results = query.find();
    query.close();
    return results;
  }
}
