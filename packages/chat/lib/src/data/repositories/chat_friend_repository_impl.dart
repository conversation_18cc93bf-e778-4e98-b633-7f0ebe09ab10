import 'dart:async';

import 'package:app_core/core.dart';
import 'package:injectable/injectable.dart' hide Order;
import 'package:shared/shared.dart';

import 'cached/chat_friend_modification_cache.dart';
import 'chat_friend_repository.dart';
import 'database/database.dart';
import 'database/entities/chat_friend.dart';
import 'database/generated/objectbox.g.dart';

@LazySingleton(as: ChatFriendRepository)
class ChatFriendRepositoryImpl extends ChatFriendRepository {
  ChatFriendRepositoryImpl(this._store, this._cache);

  final ChatStore _store;
  final ChatFriendModificationCache _cache;

  Box<ChatFriend> get _chatFriendBox => _store.box<ChatFriend>();

  @override
  int insert(ChatFriend friend) {
    final friendsToUpdate = filterFriendsToUpdate([friend]);

    if (friendsToUpdate.isNotEmpty) {
      _existsAndUpdate(friendsToUpdate);
      updateCacheForFriend(friendsToUpdate.first);
      return _chatFriendBox.put(friendsToUpdate.first);
    }
    return friend.id;
  }

  @override
  int forceInsert(ChatFriend friend) {
    _existsAndUpdate([friend]);
    updateCacheForFriend(friend);
    return _chatFriendBox.put(friend);
  }

  @override
  Future<List<int>> insertAll(List<ChatFriend> friends) async {
    print(['ChatFriendRepositoryImpl.insertAll 0', friends]);
    final friendsToUpdate = filterFriendsToUpdate(friends);

    print(['ChatFriendRepositoryImpl.insertAll 1', friendsToUpdate]);
    if (friendsToUpdate.isNotEmpty) {
      _existsAndUpdate(friendsToUpdate);
      friendsToUpdate.forEach(updateCacheForFriend);
      return _chatFriendBox.putMany(friendsToUpdate);
    }
    return friends.map((friend) => friend.id).toList();
  }

  @override
  Future<List<int>> forceInsertAll(List<ChatFriend> friends) async {
    _existsAndUpdate(friends);
    friends.forEach(updateCacheForFriend);
    friends.forEach((item) {
      Log.e(name: 'ChatFriendRepositoryImpl.forceInsertAll', item.toJson());
    });
    return _chatFriendBox.putMany(friends);
  }

  List<ChatFriend> filterFriendsToUpdate(List<ChatFriend> friends) {
    return friends.where(shouldInsertFriend).toList();
  }

  bool shouldInsertFriend(ChatFriend friend) {
    final cachedTime = _cache.peekCached(
      sessionKey: Config.getInstance().activeSessionKey ?? '',
      chatFriendId: friend.friendId.toString(),
    );
    return cachedTime != friend.updateTime;
  }

  void updateCacheForFriend(ChatFriend friend) {
    _cache.setCache(
      sessionKey: Config.getInstance().activeSessionKey ?? '',
      chatFriendId: friend.friendId.toString(),
      updateTime: friend.updateTime!,
    );
  }

  @override
  Stream<List<ChatFriend>> getChatFriendsStream() {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    return _chatFriendBox
        .query(ChatFriend_.sessionKey.equals(sessionKey))
        .watch(triggerImmediately: true)
        .map((query) => query.find());
  }

  @override
  List<ChatFriend> getChatFriends() {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    return _chatFriendBox
        .query(
          ChatFriend_.sessionKey
              .equals(sessionKey)
              .and(ChatFriend_.status.equals(5)),
        )
        .build()
        .find();
  }

  @override
  List<ChatFriend> getFriendRequests() {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    return _chatFriendBox
        .query(
          ChatFriend_.sessionKey
              .equals(sessionKey)
              .and(ChatFriend_.status.equals(3)),
        )
        .build()
        .find();
  }

  @override
  ChatFriend? getChatFriend(String friendId) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final query = _chatFriendBox
        .query(
          ChatFriend_.sessionKey.equals(sessionKey) &
              ChatFriend_.friendId.equals(friendId),
        )
        .build();
    final session = query.findFirst();
    query.close();
    return session;
  }

  @override
  bool deleteChatFriend(String friendId) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final query = _chatFriendBox
        .query(
          ChatFriend_.sessionKey.equals(sessionKey) &
              ChatFriend_.friendId.equals(friendId),
        )
        .build();
    final session = query.findFirst();
    if (session != null) {
      return _chatFriendBox.remove(session.id);
    }
    return false;
  }

  @override
  bool deleteChatFriendByUserId(String userId) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final query = _chatFriendBox
        .query(
          ChatFriend_.sessionKey.equals(sessionKey) &
              ChatFriend_.participantIds.containsElement(
                userId,
              ),
        )
        .build();
    final session = query.findFirst();
    if (session != null) {
      return _chatFriendBox.remove(session.id);
    }
    return false;
  }

  void _existsAndUpdate(List<ChatFriend> friends) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final existingQuery = _chatFriendBox
        .query(
          ChatFriend_.sessionKey.equals(sessionKey).and(
                ChatFriend_.friendId
                    .oneOf(friends.map((f) => f.friendId).toSet().toList()),
              ),
        )
        .build();

    final existingFriends = existingQuery.find();
    existingQuery.close();
    if (existingFriends.length == 0) return;

    for (final friend in friends) {
      final index =
          existingFriends.indexWhere((e) => e.friendId == friend.friendId);
      if (index != -1) {
        friend.id = existingFriends[index].id;
      }
    }
  }

  @override
  StreamSubscription observerChatFriends({
    required void Function(List<ChatFriend> friends) listener,
  }) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';

    final watchedQuery = _chatFriendBox
        .query(
          ChatFriend_.sessionKey
              .equals(sessionKey)
              .and(ChatFriend_.status.equals(5)),
        )
        .watch(triggerImmediately: true);

    return watchedQuery.listen((query) {
      final chatFriends = query.find();
      listener(chatFriends);
    });
  }

  @override
  StreamSubscription observerFriendRequests({
    required void Function(List<ChatFriend> friends) listener,
  }) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';

    final watchedQuery = _chatFriendBox
        .query(
          ChatFriend_.sessionKey
              .equals(sessionKey)
              .and(ChatFriend_.status.equals(3)),
        )
        .order(ChatFriend_.createTime, flags: Order.descending)
        .watch(triggerImmediately: true);

    return watchedQuery.listen((query) {
      final chatFriends = query.find();
      listener(chatFriends);
    });
  }

  @override
  bool deleteSession(String sessionKey) {
    final query =
        _chatFriendBox.query(ChatFriend_.sessionKey.equals(sessionKey)).build();

    final removedCount = query.remove();

    query.close();
    return removedCount > 0;
  }
}
