import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../../chat.dart';
import '../../../ui/message_list/constants.dart';
import '../../../utils/name_utils.dart';

extension MessageExstention on Message {
  String? argsContent() {
    return _translateSystemMessage();
  }

  ui.MessageItem createMessageItem({
    required Member? member,
    required ChatUser? user,
    required String? recipientId,
    required bool use24HourFormat,
    bool? hasEditedMessage,
    ui.MessageErrorReason? messageErrorReason,
    bool hasTranslate = false,
    ui.TranslateStatus? translateStatus,
    ui.TranslateButtonStatus translateButtonStatus =
        ui.TranslateButtonStatus.inActive,
    bool isFullScreenMessage = false,
    AppLocalizations? appLocalizations,
    String? appLocale,
  }) {
    final isReacted = reactions?.isNotEmpty ?? false;
    int totalReaction = 0;
    List<String> listReactions = [];
    reactions?.forEach(
      ((emoji, data) {
        totalReaction += data.total ?? 0;
        listReactions.add(emoji);
      }),
    );
    listReactions = listReactions.reversed.toList();
    final isChannel = recipientId == null;
    return ui.MessageItem(
      messageType: messageView(messageViewType),
      translateButtonStatus: translateButtonStatus,
      workspaceId: workspaceId,
      channelId: channelId,
      userId: userId,
      messageId: messageId,
      content: argsContent(),
      mentions: mentions,
      isPinnedMessage: isPinned,
      name: NameUtils.parseName(user, member),
      createTime: isFullScreenMessage
          ? createTime!.toLocal().toLocaleTimeFullScreen(
                appLocalizations,
                appLocale,
                use24Hour: use24HourFormat,
              )
          : createTime!.toLocal().toStringTime(use24Hour: use24HourFormat),
      avatar: user != null ? user.profile!.CDNAvatar : '',
      hasTranslate: hasTranslate,
      messageStatus: _mapMessageStatus(),
      isReacted: isReacted,
      totalReaction: totalReaction,
      use24HourFormat: use24HourFormat,
      listReactions: getFirstItems(listReactions, 3).join(),
      isChannel: isChannel,
      isLastMessage: isLastReceiveMessage,
      hasEditedMessage: hasEditedMessage ?? false,
      recipientId: recipientId,
      translateStatus: translateStatus,
      messageErrorReason: messageErrorReason,
    );
  }

  List<T> getFirstItems<T>(List<T> list, int count) {
    return list.sublist(0, list.length < count ? list.length : count);
  }

  ui.MessageStatus _mapMessageStatus() {
    switch (messageStatus) {
      case MessageStatus.UNRECOGNIZED:
        return ui.MessageStatus.unrecognize;
      case MessageStatus.PENDING:
        return ui.MessageStatus.sending;
      case MessageStatus.SUCCESS:
        return ui.MessageStatus.sent;
      case MessageStatus.FAILURE:
        return ui.MessageStatus.failed;
    }
  }

  bool get hasEmbed => embed != null && embed!.isNotEmpty;

  Embed? get firstEmbed => hasEmbed ? embed!.first : null;

  bool get hasLocationData => firstEmbed?.hasLocationData ?? false;

  bool get hasEmbedData => firstEmbed?.hasEmbedData ?? false;

  bool get hasInvitationData => firstEmbed?.hasInvitationData ?? false;

  bool get isMe => userId == Config.getInstance().activeSessionKey;

  bool get hasMention => mentions != null && mentions!.isNotEmpty;

  bool get hasContentArguments =>
      contentArguments != null && contentArguments!.isNotEmpty;

  List<Map<String, dynamic>> get emojiList {
    if (reactions?.isEmpty ?? true) {
      return defaultEmojiList;
    }
    final emojiList = List<Map<String, dynamic>>.from(defaultEmojiList);
    reactions?.forEach((emoji, data) {
      final index = emojiList.indexWhere((e) => e['emoji'] == emoji);
      if (index != -1) {
        emojiList[index] = {'emoji': emoji, 'isSelected': data.isReacted};
      }
    });

    return emojiList;
  }

  String? _translateSystemMessage() {
    return TranslateContentUtils.translateContent(
      content ?? '',
      contentArguments ?? [],
    );
  }

  ui.MessageType messageView(MessageViewType messageViewType) {
    return switch (messageViewType) {
      MessageViewType.text => ui.MessageType.text,
      MessageViewType.loading => ui.MessageType.loading,
      MessageViewType.systemTime => ui.MessageType.systemTime,
      MessageViewType.system => ui.MessageType.system,
      MessageViewType.images => ui.MessageType.image,
      MessageViewType.video => ui.MessageType.video,
      MessageViewType.wave => ui.MessageType.wave,
      MessageViewType.ziiVoice => ui.MessageType.ziiVoice,
      MessageViewType.ziiShort => ui.MessageType.ziiShort,
      MessageViewType.link => ui.MessageType.link,
      MessageViewType.sticker => ui.MessageType.sticker,
      MessageViewType.textOwner => ui.MessageType.text,
      MessageViewType.imagesOwner => ui.MessageType.image,
      MessageViewType.videoOwner => ui.MessageType.video,
      MessageViewType.waveOwner => ui.MessageType.wave,
      MessageViewType.ziiVoiceOwner => ui.MessageType.ziiVoice,
      MessageViewType.ziiShortsOwner => ui.MessageType.ziiShort,
      MessageViewType.linkOwner => ui.MessageType.link,
      MessageViewType.stickerOwner => ui.MessageType.sticker,
      MessageViewType.invitation => ui.MessageType.invitation,
      MessageViewType.invitationOwner => ui.MessageType.invitation,
      MessageViewType.unreadMessageDivider =>
        ui.MessageType.unreadMessageDivider,
      MessageViewType.locationOwner => ui.MessageType.shareLocation,
      MessageViewType.location => ui.MessageType.shareLocation,
      MessageViewType.fileOwner => ui.MessageType.file,
      MessageViewType.file => ui.MessageType.file,
    };
  }

  bool hasMentionBySomeThing(Set<String> something) {
    if (mentions == null) return false;
    return mentions!.any(
      (mention) => something.contains(mention.replaceAll("@", "")),
    );
  }

  bool hasContentArgumentsBySomeThing(Set<String> something) {
    if (contentArguments == null) return false;
    return contentArguments!.any(
      (arguments) => something.contains(arguments.replaceAll("@", "")),
    );
  }
}
