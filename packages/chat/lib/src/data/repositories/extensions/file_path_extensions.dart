import 'dart:convert';

import '../database/classes/media_object.dart';
import '../database/entities/attachment.dart';
import '../database/entities/message.dart';

/// Extension for Message to handle preserving filePath
extension MessageFilePathExtension on Message {
  /// Update filePath from old message to new message
  void preserveLocalFilePaths(Message oldMessage) {
    // If there are no attachments in old or new message, no processing needed
    if (oldMessage.mediaAttachments.isEmpty || mediaAttachments.isEmpty) {
      return;
    }

    // Find and update filePath for each attachment
    for (final newAttachment in mediaAttachments) {
      final oldAttachment = oldMessage.findMatchingAttachment(newAttachment);

      if (oldAttachment != null) {
        newAttachment.preserveLocalFilePaths(oldAttachment);
      }
    }
  }

  /// Find old attachment corresponding to new attachment
  Attachment? findMatchingAttachment(Attachment newAttachment) {
    // Create maps for easy lookup of old attachments based on attachmentId and fileRef
    final oldAttachmentByIdMap = <String, Attachment>{};
    final oldAttachmentByRefMap = <String, Attachment>{};

    for (final attachment in mediaAttachments) {
      if (attachment.attachmentId != null) {
        oldAttachmentByIdMap[attachment.attachmentId!] = attachment;
      }
      if (attachment.ref != null) {
        oldAttachmentByRefMap[attachment.ref!] = attachment;
      }
    }

    // Check for matching attachmentId first
    if (newAttachment.attachmentId != null &&
        oldAttachmentByIdMap.containsKey(newAttachment.attachmentId)) {
      return oldAttachmentByIdMap[newAttachment.attachmentId];
    }
    // If no match by attachmentId, check for matching ref
    else if (newAttachment.ref != null &&
        oldAttachmentByRefMap.containsKey(newAttachment.ref)) {
      return oldAttachmentByRefMap[newAttachment.ref];
    }

    return null;
  }
}

/// Extension for Attachment to handle preserving filePath
extension AttachmentFilePathExtension on Attachment {
  /// Update filePath from old attachment to new attachment
  void preserveLocalFilePaths(Attachment oldAttachment) {
    // Update filePath for each type of media object
    MediaObjectFilePathExtension.updateFilePathForMediaObject(
      oldAttachment.photo,
      photo,
    );
    MediaObjectFilePathExtension.updateFilePathForMediaObject(
      oldAttachment.audio,
      audio,
    );
    MediaObjectFilePathExtension.updateFilePathForMediaObject(
      oldAttachment.video,
      video,
    );
    MediaObjectFilePathExtension.updateFilePathForMediaObject(
      oldAttachment.file,
      file,
    );
    MediaObjectFilePathExtension.updateFilePathForMediaObject(
      oldAttachment.voiceMessage,
      voiceMessage,
    );
    MediaObjectFilePathExtension.updateFilePathForMediaObject(
      oldAttachment.videoMessage,
      videoMessage,
    );
    MediaObjectFilePathExtension.updateFilePathForMediaObject(
      oldAttachment.mediaMessage,
      mediaMessage,
    );
    MediaObjectFilePathExtension.updateFilePathForMediaObject(
      oldAttachment.undefined,
      undefined,
    );

    // Directly update raw fields
    updateRawFields(oldAttachment);
  }

  /// Update filePath in raw fields
  void updateRawFields(Attachment oldAttachment) {
    try {
      // Update photoRaw if needed
      updateRawField(
        oldAttachment.photo,
        photo,
        oldAttachment.photoRaw,
        photoRaw,
        (value) => photoRaw = value,
        'photoRaw',
      );

      // Update audioRaw if needed
      updateRawField(
        oldAttachment.audio,
        audio,
        oldAttachment.audioRaw,
        audioRaw,
        (value) => audioRaw = value,
        'audioRaw',
      );

      // Update videoRaw if needed
      updateRawField(
        oldAttachment.video,
        video,
        oldAttachment.videoRaw,
        videoRaw,
        (value) => videoRaw = value,
        'videoRaw',
      );

      // Update fileMessageRaw if needed
      updateRawField(
        oldAttachment.file,
        file,
        oldAttachment.fileMessageRaw,
        fileMessageRaw,
        (value) => fileMessageRaw = value,
        'fileMessageRaw',
      );

      // Update voiceMessageRaw if needed
      updateRawField(
        oldAttachment.voiceMessage,
        voiceMessage,
        oldAttachment.voiceMessageRaw,
        voiceMessageRaw,
        (value) => voiceMessageRaw = value,
        'voiceMessageRaw',
      );

      // Update videoMessageRaw if needed
      updateRawField(
        oldAttachment.videoMessage,
        videoMessage,
        oldAttachment.videoMessageRaw,
        videoMessageRaw,
        (value) => videoMessageRaw = value,
        'videoMessageRaw',
      );

      // Update mediaMessageRaw if needed
      updateRawField(
        oldAttachment.mediaMessage,
        mediaMessage,
        oldAttachment.mediaMessageRaw,
        mediaMessageRaw,
        (value) => mediaMessageRaw = value,
        'mediaMessageRaw',
      );

      // Update undefinedMessageRaw if needed
      updateRawField(
        oldAttachment.undefined,
        undefined,
        oldAttachment.undefinedMessageRaw,
        undefinedMessageRaw,
        (value) => undefinedMessageRaw = value,
        'undefinedMessageRaw',
      );
    } catch (e) {
      // Error handling without debug print
    }
  }

  /// Update a specific raw field
  void updateRawField(
    MediaObject? oldMedia,
    MediaObject? newMedia,
    String? oldRaw,
    String? newRaw,
    Function(String) setter,
    String fieldName,
  ) {
    if (oldMedia != null &&
        oldMedia.filePath != null &&
        oldMedia.filePath!.isNotEmpty &&
        newMedia != null) {
      try {
        // Get current JSON
        final Map<String, dynamic> json = jsonDecode(newRaw ?? '{}');
        // Update filePath
        json['filePath'] = oldMedia.filePath;
        // Write back to raw field
        setter(jsonEncode(json));
      } catch (e) {
        // Error handling without debug print
      }
    }
  }
}

/// Extension for MediaObject to handle preserving filePath
extension MediaObjectFilePathExtension on MediaObject {
  /// Update filePath from old mediaObject to new mediaObject
  static void updateFilePathForMediaObject(
    MediaObject? oldMedia,
    MediaObject? newMedia,
  ) {
    if (oldMedia == null || newMedia == null) {
      return;
    }

    // Only update filePath if oldMedia has filePath and newMedia doesn't have it or it's empty
    if (oldMedia.filePath != null &&
        oldMedia.filePath!.isNotEmpty &&
        (newMedia.filePath == null || newMedia.filePath!.isEmpty)) {
      try {
        // Update directly to MediaObject
        newMedia.filePath = oldMedia.filePath;
      } catch (e) {
        // Error handling without debug print
      }
    }
  }
}
