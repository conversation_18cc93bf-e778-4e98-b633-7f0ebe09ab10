import 'package:freezed_annotation/freezed_annotation.dart';

enum ChatFriendStatusEnum {
  @JsonValue(0)
  UNSPECIFIED,
  @JsonValue(1)
  NOT_FRIEND,
  @JsonValue(2)
  REQUEST_SENT,
  @JsonValue(3)
  REQUEST_RECEIVED,
  @JsonValue(4)
  REQUEST_DELETED,
  @JsonValue(5)
  FRIEND,
}

extension ChatFriendStatusEnumExtension on ChatFriendStatusEnum {
  static ChatFriendStatusEnum getEnumByValue(int? value) {
    return ChatFriendStatusEnum.values.firstWhere(
      (e) => e.toValue() == value,
      orElse: () => ChatFriendStatusEnum.UNSPECIFIED,
    );
  }

  int toValue() {
    switch (this) {
      case ChatFriendStatusEnum.NOT_FRIEND:
        return 1;
      case ChatFriendStatusEnum.REQUEST_SENT:
        return 2;
      case ChatFriendStatusEnum.REQUEST_RECEIVED:
        return 3;
      case ChatFriendStatusEnum.REQUEST_DELETED:
        return 4;
      case ChatFriendStatusEnum.FRIEND:
        return 5;
      default:
        return 0;
    }
  }
}
