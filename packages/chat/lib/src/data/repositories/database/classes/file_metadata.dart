import 'package:json_annotation/json_annotation.dart';

part 'file_metadata.g.dart';

@JsonSerializable(explicitToJson: true)
class FileMetadata {
  @J<PERSON><PERSON>ey(name: 'filename')
  String? filename;

  @J<PERSON><PERSON>ey(name: 'filesize')
  int? filesize;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'extension')
  String? extension_;

  @Json<PERSON>ey(name: 'mimetype')
  String? mimetype;

  @Json<PERSON>ey(name: 'dimensions')
  Dimensions? dimensions;

  @Json<PERSON>ey(name: 'duration')
  int? duration;

  FileMetadata({
    this.filename,
    this.filesize,
    this.extension_,
    this.mimetype,
    this.dimensions,
    this.duration,
  });

  factory FileMetadata.fromJson(Map<String, dynamic> json) =>
      _$FileMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$FileMetadataToJson(this);
}

@JsonSerializable(explicitToJson: true)
class Dimensions {
  @JsonKey(name: 'height')
  int? height;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'width')
  int? width;

  Dimensions({
    this.height,
    this.width,
  });

  factory Dimensions.fromJson(Map<String, dynamic> json) =>
      _$DimensionsFromJson(json);

  Map<String, dynamic> toJson() => _$DimensionsToJson(this);
}
