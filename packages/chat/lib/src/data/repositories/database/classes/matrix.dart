import 'package:json_annotation/json_annotation.dart';

part 'matrix.g.dart';

@JsonSerializable(explicitToJson: true)
class Matrix {
  @Json<PERSON>ey(name: 'row')
  int? row;

  @Json<PERSON>ey(name: 'column')
  int? column;

  Matrix({
    this.row,
    this.column,
  });

  factory Matrix.fromJson(Map<String, dynamic> json) => _$Matrix<PERSON>rom<PERSON>son(json);

  Map<String, dynamic> toJson() => _$MatrixToJson(this);
}
