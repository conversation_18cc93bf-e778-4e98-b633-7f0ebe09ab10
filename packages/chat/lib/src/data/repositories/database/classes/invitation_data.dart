import 'package:json_annotation/json_annotation.dart';

part 'invitation_data.g.dart';

@JsonSerializable(explicitToJson: true)
class InvitationData {
  @<PERSON><PERSON><PERSON><PERSON>(name: 'channel')
  Map<String, dynamic>? channel;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'code')
  String? code;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'isExpired')
  bool? isExpired;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'expireTime')
  String? expireTime;

  @<PERSON>son<PERSON><PERSON>(name: 'isJoined')
  bool? isJoined;

  @<PERSON>son<PERSON><PERSON>(name: 'invitationLink')
  String? invitationLink;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'createTime')
  String? createTime;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'updateTime')
  String? updateTime;

  InvitationData({
    this.channel,
    this.code,
    this.isExpired,
    this.expireTime,
    this.isJoined,
    this.invitationLink,
    this.createTime,
    this.updateTime,
  });

  factory InvitationData.fromJson(Map<String, dynamic> json) =>
      _$InvitationDataFromJson(json);

  Map<String, dynamic> toJson() => _$InvitationDataToJson(this);
}
