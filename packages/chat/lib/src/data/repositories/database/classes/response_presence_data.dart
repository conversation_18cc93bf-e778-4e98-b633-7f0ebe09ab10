import 'package:json_annotation/json_annotation.dart';

part 'response_presence_data.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponsePresenceData {
  final String? lastUpdateTime;
  final int? lastUpdateInSeconds;
  final int? presenceState;

  ResponsePresenceData({
    this.lastUpdateTime,
    this.lastUpdateInSeconds,
    this.presenceState,
  });

  factory ResponsePresenceData.fromJson(Map<String, dynamic> json) =>
      _$ResponsePresenceDataFromJson(json);

  Map<String, dynamic> toJson() => _$ResponsePresenceDataToJson(this);
}
