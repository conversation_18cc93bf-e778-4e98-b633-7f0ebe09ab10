import 'package:json_annotation/json_annotation.dart';

part 'response_channel_metadata.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseChannelMetadata {
  final int? unreadCount;
  final String? lastMessageId;
  final bool? notificationStatus;

  ResponseChannelMetadata({
    this.unreadCount,
    this.lastMessageId,
    this.notificationStatus,
  });

  factory ResponseChannelMetadata.fromJson(Map<String, dynamic> json) =>
      _$ResponseChannelMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseChannelMetadataToJson(this);
}
