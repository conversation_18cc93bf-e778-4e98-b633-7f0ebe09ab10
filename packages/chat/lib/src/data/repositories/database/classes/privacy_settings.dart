import 'package:json_annotation/json_annotation.dart';

import 'restrict_saving_content.dart';

part 'privacy_settings.g.dart';

@JsonSerializable(explicitToJson: true)
class PrivacySettings {
  final RestrictSavingContent restrictSavingContent;

  PrivacySettings({required this.restrictSavingContent});

  factory PrivacySettings.fromJson(Map<String, dynamic> json) =>
      _$PrivacySettingsFromJson(json);

  Map<String, dynamic> toJson() => _$PrivacySettingsToJson(this);
}
