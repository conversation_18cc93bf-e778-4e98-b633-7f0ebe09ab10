import 'package:json_annotation/json_annotation.dart';

part 'response_matrix.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseMatrix {
  @JsonKey(name: 'row')
  int? row;

  @J<PERSON><PERSON>ey(name: 'column')
  int? column;

  ResponseMatrix({
    this.row,
    this.column,
  });

  factory ResponseMatrix.fromJson(Map<String, dynamic> json) =>
      _$ResponseMatrixFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseMatrixToJson(this);
}
