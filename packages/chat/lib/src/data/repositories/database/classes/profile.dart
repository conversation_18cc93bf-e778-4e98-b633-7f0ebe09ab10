import 'package:json_annotation/json_annotation.dart';

part 'profile.g.dart';

@JsonSerializable(explicitToJson: true)
class Profile {
  final String? avatar;
  final String? displayName;
  final String? cover;
  final String? originalAvatar;

  Profile({
    this.avatar,
    this.displayName,
    this.cover,
    this.originalAvatar,
  });

  factory Profile.fromJson(Map<String, dynamic> json) =>
      _$ProfileFromJson(json);

  Map<String, dynamic> toJson() => _$ProfileToJson(this);
}
