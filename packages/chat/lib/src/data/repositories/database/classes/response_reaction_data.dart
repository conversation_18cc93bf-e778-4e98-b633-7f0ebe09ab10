import 'package:json_annotation/json_annotation.dart';

part 'response_reaction_data.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseReactionData {
  @Json<PERSON>ey(name: 'isReacted')
  bool? isReacted;

  @Json<PERSON>ey(name: 'total')
  int? total;

  ResponseReactionData({
    this.isReacted,
    this.total,
  });

  factory ResponseReactionData.fromJson(Map<String, dynamic> json) =>
      _$ResponseReactionDataFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseReactionDataToJson(this);
}
