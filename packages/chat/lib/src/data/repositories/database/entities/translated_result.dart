import 'package:objectbox/objectbox.dart';

import '../enums/translated_status_enum.dart';

@Entity()
class TranslatedResult {
  TranslatedResult({
    required this.workspaceId,
    required this.channelId,
    required this.messageId,
    required this.session<PERSON><PERSON>,
    required this.originalContent,
    required this.translatedContent,
    required this.originalLanguage,
    required this.targetLanguage,
    this.statusRaw,
    this.isShowTranslateResult = true,
  });

  @Id(assignable: true)
  int id = 0;

  @Property(uid: 17001)
  @Index(type: IndexType.value)
  String workspaceId;

  @Property(uid: 17002)
  @Index(type: IndexType.value)
  String channelId;

  @Property(uid: 17003)
  @Index(type: IndexType.value)
  String sessionKey;

  @Property(uid: 17004)
  @Index(type: IndexType.value)
  String messageId;

  @Property(uid: 17005)
  String? originalContent;

  @Property(uid: 17006)
  String? translatedContent;

  @Property(uid: 17007)
  String? originalLanguage;

  @Property(uid: 17008)
  String? targetLanguage;

  @Property(uid: 17009)
  int? statusRaw;

  @Property(uid: 17010)
  bool isShowTranslateResult;

  TranslatedStatusEnum get getStatus =>
      TranslatedStatusEnum.getEnumByValue(statusRaw);

  TranslatedResult copyWith({
    int? id,
    String? workspaceId,
    String? channelId,
    String? sessionKey,
    String? messageId,
    String? originalContent,
    String? translatedContent,
    String? originalLanguage,
    String? targetLanguage,
    int? statusRaw,
    bool? isShowTranslateResult,
  }) {
    return TranslatedResult(
      workspaceId: workspaceId ?? this.workspaceId,
      channelId: channelId ?? this.channelId,
      messageId: messageId ?? this.messageId,
      sessionKey: sessionKey ?? this.sessionKey,
      originalContent: originalContent ?? this.originalContent,
      translatedContent: translatedContent ?? this.translatedContent,
      originalLanguage: originalLanguage ?? this.originalLanguage,
      targetLanguage: targetLanguage ?? this.targetLanguage,
      statusRaw: statusRaw ?? this.statusRaw,
      isShowTranslateResult:
          isShowTranslateResult ?? this.isShowTranslateResult,
    )..id = id ?? this.id; // Copy giá trị `id` nếu cần
  }
}
