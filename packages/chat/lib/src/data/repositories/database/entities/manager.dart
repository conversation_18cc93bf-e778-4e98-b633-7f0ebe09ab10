import 'package:objectbox/objectbox.dart';

@Entity()
class Manager {
  Manager({
    this.id = 0,
    required this.sessionKey,
    this.closedMessageRequestWarning = false,
    this.loadedAllChannels = false,
    this.loadedAllFriends = false,
    this.loadedAllFriendRequests = false,
    this.closedListBlockUserWarning = false,
    this.userStatusEmojis = defaultUserStatusEmojis,
  });

  @Id(assignable: true)
  int id;
  @Property(uid: 7001)
  String sessionKey;
  @Property(uid: 7002)
  bool loadedAllChannels;
  @Property(uid: 7003)
  bool loadedAllFriends;
  @Property(uid: 7004)
  bool loadedAllFriendRequests;
  @Property(uid: 7005)
  bool closedMessageRequestWarning;
  @Property(uid: 7006)
  bool closedListBlockUserWarning;
  @Property(uid: 7007)
  List<String> userStatusEmojis;
}

const List<String> defaultUserStatusEmojis = [
  '❤️',
  '😂',
  '😢',
  '😡',
  '😮',
  '🥰',
  '🔥',
  '😋',
  '😈',
];
