import 'package:json_annotation/json_annotation.dart';

import '../enums/chat_friend_status.dart';

part 'chat_friend_data.g.dart';

@JsonSerializable(explicitToJson: true)
class ChatFriendData {
  ChatFriendData({
    this.status,
    this.acceptTime,
    this.deleteTime,
    this.createTime,
  });

  ChatFriendStatusEnum? status;
  String? acceptTime;
  String? deleteTime;
  String? createTime;

  factory ChatFriendData.fromJson(Map<String, dynamic> json) =>
      _$ChatFriendDataFromJson(json);

  Map<String, dynamic> toJson() => _$ChatFriendDataToJson(this);
}
