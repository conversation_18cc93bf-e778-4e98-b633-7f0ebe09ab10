import 'package:json_annotation/json_annotation.dart';
import 'package:shared/shared.dart';

part 'chat_profile.g.dart';

@JsonSerializable(explicitToJson: true)
class ChatProfile {
  ChatProfile({
    this.avatar,
    this.cover,
    this.displayName,
    this.originalAvatar,
    this.email,
    this.phoneNumber,
    this.userBadgeType,
  });

  String? avatar;
  String? cover;
  String? displayName;
  String? originalAvatar;
  String? phoneNumber;
  String? email;
  int? userBadgeType;

  factory ChatProfile.fromJson(Map<String, dynamic> json) =>
      _$ChatProfileFromJson(json);

  Map<String, dynamic> toJson() => _$ChatProfileToJson(this);

  String get CDNAvatar => UrlUtils.parseAvatar(avatar);

  String get CDNOriginalAvatar => UrlUtils.parseAvatar(originalAvatar);
}
