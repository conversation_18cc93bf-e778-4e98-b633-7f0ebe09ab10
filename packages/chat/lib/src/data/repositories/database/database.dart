import 'package:flutter/foundation.dart';
import 'package:objectbox/internal.dart';
import 'package:objectbox/objectbox.dart';
import 'package:shared/shared.dart';

class ChatDatabase {
  ChatDatabase(this.store) {
    if (Admin.isAvailable() && kDebugMode && GlobalConfig.enableChatBoxAdmin) {
      admin = ChatAdmin(store, bindUri: 'http://127.0.0.1:8093');
    }
  }

  ChatAdmin? admin;

  final ChatStore store;
}

class ChatAdmin extends Admin {
  ChatAdmin(this.store, {required this.bindUri})
      : super(store, bindUri: bindUri) {}

  final String bindUri;

  final Store store;
}

class ChatStore extends Store {
  ChatStore(ModelDefinition modelDefinition, {required String directory})
      : super(modelDefinition, directory: directory);
}
