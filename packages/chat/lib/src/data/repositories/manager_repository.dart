abstract class ManagerRepository {
  int updateLoadedAllChannelsStatus(bool loadedAllChannels);

  int updateLoadedAllFriendsStatus(bool loadedAllFriends);

  int updateLoadedAllFriendRequestsStatus(bool loadedAllFriendRequests);

  int updateClosedMessageRequestWarningStatus(bool closedMessageRequestWarning);

  int updateClosedListBlockUserWarningStatus(bool closedMessageRequestWarning);

  int updateUserStatusEmojis(List<String> emojis);

  bool getLoadedAllChannelsStatus();

  bool getLoadedAllFriendsStatus();

  bool getLoadedAllFriendRequestsStatus();

  bool getClosedMessageRequestWarningStatus();

  bool getClosedListBlockUserWarningStatus();

  List<String> getUserStatusEmojis();

  void deleteSession(String sessionKey);
}
