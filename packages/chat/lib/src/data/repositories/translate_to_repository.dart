import 'dart:async';

import 'database/entities/channel_local_metadata.dart';
import 'database/entities/translated_result.dart';

/// translate_to_repository.dart
abstract class TranslateToRepository {
  // -----------------------------
  // A. NHÓM PHƯƠNG THỨC CHO ChannelLocalMetadata
  // -----------------------------

  /// Lấy thông tin cài đặt dịch cho một channel
  ChannelLocalMetadata? getChannelLocalMetadata({
    required String workspaceId,
    required String channelId,
    String? sessionKey,
  });

  /// Tạo mới hoặc cập nhật cài đặt dịch cho channel
  int insertOrUpdateChannelLocalMetadata(ChannelLocalMetadata metadata);

  /// Xoá cài đặt dịch cho một channel
  bool deleteChannelLocalMetadata({
    required String workspaceId,
    required String channelId,
    String? sessionKey,
  });

  /// <PERSON><PERSON><PERSON> tất cả cài đặt dịch (ChannelLocalMetadata) của một sessionKey
  int deleteAllLocalMetadataOfSession(String sessionKey);

  /// Lắng nghe thay đổi cài đặt dịch của một channel
  /// để cập nhật UI/logic real-time (nếu cần).
  StreamSubscription observerChannelLocalMetadata(
    String workspaceId,
    String channelId,
    void Function(ChannelLocalMetadata? metadata) listener,
  );

  // -----------------------------
  // B. NHÓM PHƯƠNG THỨC CHO TranslatedResult
  // -----------------------------

  /// Lấy kết quả dịch của một tin nhắn
  TranslatedResult? getTranslatedResult({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required String sessionKey,
  });

  /// Thêm hoặc cập nhật kết quả dịch cho 1 tin nhắn
  int insertOrUpdateTranslatedResult(TranslatedResult translatedResult);

  /// Thêm hoặc cập nhật hàng loạt kết quả dịch
  Future<List<int>> insertOrUpdateAllTranslatedResults(
    List<TranslatedResult> translatedResults,
  );

  /// Xoá kết quả dịch của 1 tin nhắn
  bool deleteTranslatedResult({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required String sessionKey,
  });

  /// Xoá tất cả kết quả dịch trong 1 channel
  int deleteAllTranslatedResultsInChannel({
    required String workspaceId,
    required String channelId,
    required String sessionKey,
  });

  /// Xoá tất cả kết quả dịch của 1 sessionKey
  int deleteAllTranslatedResultsOfSession(String sessionKey);

  /// Kiểm tra DB kết quả dịch có rỗng hay không (theo sessionKey)
  bool isEmptyTranslatedResult({required String sessionKey});

  /// Cập nhật trạng thái dịch (đang dịch, dịch thành công, lỗi, retry,...)
  bool updateTranslationStatus({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required String sessionKey,
    required int newStatusValue, // Hoặc TruncatedStatusEnum
  });

  /// Lắng nghe thay đổi của kết quả dịch cho 1 tin nhắn cụ thể
  StreamSubscription observerTranslatedResult(
    String workspaceId,
    String channelId,
    String messageId,
    String sessionKey,
    void Function(TranslatedResult? result) listener,
  );

  /// Lắng nghe toàn bộ kết quả dịch trong 1 channel
  StreamSubscription observerAllTranslatedResultsInChannel(
    String workspaceId,
    String channelId,
    String sessionKey,
    void Function(List<TranslatedResult> results) listener,
  );

  /// Listen to translation results of specific messages
  StreamSubscription observerTranslatedResultsByMessageIds(
    Set<String> messageIds,
    String sessionKey,
    void Function(List<TranslatedResult> results) listener,
  );

  /// List translating messages
  List<TranslatedResult> translatingMessages(String sessionKey);
}
