import 'dart:async';

class TimeoutManager {
  final Map<String, Timer> _messageTimers = {};
  final Duration timeoutDuration;

  TimeoutManager(this.timeoutDuration);

  void addMessage(String messageId, Function(String) onTimeout) {
    _messageTimers[messageId]?.cancel();

    Timer timer = Timer(timeoutDuration, () {
      _messageTimers.remove(messageId);
      onTimeout(messageId);
    });

    _messageTimers[messageId] = timer;
  }

  void completeMessage(String messageId) {
    _messageTimers[messageId]?.cancel();
    _messageTimers.remove(messageId);
  }

  void dispose() {
    for (var timer in _messageTimers.values) {
      timer.cancel();
    }
    _messageTimers.clear();
  }
}
