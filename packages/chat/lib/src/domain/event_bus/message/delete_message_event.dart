import 'package:shared/shared.dart';

class DeleteMessageEvent extends BaseEvent {
  DeleteMessageEvent({
    required this.workspaceId,
    required this.channelId,
    required this.messageIds,
    super.source = BaseEvent.LOCAL_SOURCE,
    super.id = 'MESSAGE_UPDATED',
  });

  final String workspaceId;
  final String channelId;
  final List<String> messageIds;

  @override
  Map<String, dynamic> toJson() => {
        'workspaceId': workspaceId,
        'channelId': channelId,
        'messageId': messageIds,
      };
}
