import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:member_view_api/member_view_api.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

part 'load_all_member_use_case.freezed.dart';

@Injectable()
class LoadAllMemberUseCase
    extends BaseFutureUseCase<LoadAllMemberInput, LoadAllMemberOutput> {
  const LoadAllMemberUseCase();

  @override
  Future<LoadAllMemberOutput> buildUseCase(
    LoadAllMemberInput input,
  ) async {
    final members = await _loadListMembers(
      workspaceId: input.workspaceId,
      channelId: input.channelId,
    );
    return LoadAllMemberOutput(members: members);
  }

  Future<List<Member>> _loadListMembers({
    required String workspaceId,
    required String channelId,
    String? next_page_token,
  }) async {
    try {
      final response = await MemberViewClient().instance.listMembers(
            channelId: channelId,
            workspaceId: workspaceId,
            limit: 500,
            nextPageToken: next_page_token,
          );
      if (response.data?.ok ?? false) {
        final listMembers = response.data?.data?.toList() ?? [];
        final members = listMembers.map((member) {
          final json = jsonDecode(
            standardSerializers.toJson(
              V3MemberData.serializer,
              member,
            ),
          );
          return MemberSerializer.serializeFromJson(data: json['member'])!;
        });
        if (response.data?.paging?.hasNext ?? false) {
          final next_page_token = response.data?.paging?.nextPageToken;
          return [
            ...members,
            ...(await _loadListMembers(
              workspaceId: workspaceId,
              channelId: channelId,
              next_page_token: next_page_token,
            )),
          ];
        }
        return [...members];
      }
    } catch (ex) {
      Log.e(ex);
    }
    return [];
  }
}

@freezed
sealed class LoadAllMemberInput extends BaseInput with _$LoadAllMemberInput {
  const LoadAllMemberInput._();
  factory LoadAllMemberInput({
    required String workspaceId,
    required String channelId,
  }) = _LoadAllMemberInput;
}

@freezed
sealed class LoadAllMemberOutput extends BaseOutput with _$LoadAllMemberOutput {
  const LoadAllMemberOutput._();
  factory LoadAllMemberOutput({
    required List<Member>? members,
  }) = _LoadAllMemberOutput;
}
