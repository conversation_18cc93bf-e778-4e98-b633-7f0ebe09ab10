import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

part 'load_member_use_case.freezed.dart';

@Injectable()
class LoadMemberUseCase
    extends BaseFutureUseCase<LoadMemberInput, LoadMemberOutput> {
  const LoadMemberUseCase();

  @override
  Future<LoadMemberOutput> buildUseCase(
    LoadMemberInput input,
  ) async {
    // final cachedMembers =
    //     _repository.getCachedMembers(input.workspaceId, input.channelId);
    // if (cachedMembers != null && cachedMembers.isNotEmpty) {
    //   return cachedMembers;
    // }
    //
    // final members = _repository.loadMembersFromChannel(
    //   workspaceId: input.workspaceId,
    //   channelId: input.channelId,
    // );
    //
    // // Lưu vào cache để lần sau sử dụng
    // _repository.cacheMembers(input.workspaceId, input.channelId, members);

    return LoadMemberOutput(member: null);
  }
}

@freezed
sealed class LoadMemberInput extends BaseInput with _$LoadMemberInput {
  const LoadMemberInput._();
  factory LoadMemberInput({
    required String workspaceId,
    required String channelId,
    required String userId,
  }) = _LoadMemberInput;
}

@freezed
sealed class LoadMemberOutput extends BaseOutput with _$LoadMemberOutput {
  const LoadMemberOutput._();
  factory LoadMemberOutput({
    required Member? member,
  }) = _LoadMemberOutput;
}
