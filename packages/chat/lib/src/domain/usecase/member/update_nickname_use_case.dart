import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:member_api/member_api.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class UpdateNickNameUseCase
    extends BaseFutureUseCase<UpdateNickNameInput, UpdateNickNameOutput> {
  const UpdateNickNameUseCase(
    this._memberRepository,
  );

  final MemberRepository _memberRepository;

  @override
  Future<UpdateNickNameOutput> buildUseCase(UpdateNickNameInput input) async {
    final bodyBuilder = V3UpdateNicknameRequestBuilder();
    bodyBuilder
      ..userId = input.userId
      ..workspaceId = input.workspaceId
      ..channelId = input.channelId
      ..nickname = input.nickname;

    try {
      final response = await MemberClient()
          .instance
          .updateNickname(body: bodyBuilder.build());
      if (response.data?.ok ?? false) {
        final json = jsonDecode(
          standardSerializers.toJson(
            V3Member.serializer,
            response.data!.data!.member,
          ),
        );
        final member = MemberSerializer.serializeFromJson(data: json);
        if (member != null) {
          _memberRepository.forceInsert(member);
        }
        return UpdateNickNameOutput(member: member);
      }
    } catch (ex) {
      Log.e(ex);
    }
    AppEventBus.publish(DialogErrorOccurredEvent());
    return UpdateNickNameOutput();
  }
}

class UpdateNickNameInput extends BaseInput {
  const UpdateNickNameInput({
    required this.workspaceId,
    required this.channelId,
    required this.userId,
    required this.nickname,
  });

  final String userId;
  final String channelId;
  final String workspaceId;
  final String nickname;
}

class UpdateNickNameOutput extends BaseOutput {
  const UpdateNickNameOutput({
    this.member,
  });

  final Member? member;
}
