import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class GetMeetingRoomUseCase
    extends BaseFutureUseCase<GetMeetingRoomInput, GetMeetingRoomOutput> {
  GetMeetingRoomUseCase();

  @override
  Future<GetMeetingRoomOutput> buildUseCase(GetMeetingRoomInput input) async {
    final response = await CallClient().instance.getMeetingRoom(
          workspaceId: input.workspaceId,
          channelId: input.channelId,
        );
    if ((response.data?.ok ?? false) && response.data?.data != null) {
      return GetMeetingRoomOutput(
        ok: true,
        hasRoom: true,
        numParticipants: response.data!.data!.numParticipants ?? 0,
      );
    }
    return GetMeetingRoomOutput(ok: true, hasRoom: false);
  }
}

class GetMeetingRoomInput extends BaseInput {
  GetMeetingRoomInput({
    required this.workspaceId,
    required this.channelId,
  });

  final String workspaceId;
  final String channelId;
}

class GetMeetingRoomOutput extends BaseOutput {
  GetMeetingRoomOutput({
    required this.ok,
    required this.hasRoom,
    this.numParticipants = 0,
    this.error,
  });

  final bool ok;
  final bool hasRoom;
  final int numParticipants;
  final Object? error;
}
