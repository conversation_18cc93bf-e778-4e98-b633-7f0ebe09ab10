import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class LoadInvitableFriendsUseCase extends BaseFutureUseCase<
    LoadInvitableFriendsInput, LoadInvitableFriendsOutput> {
  const LoadInvitableFriendsUseCase();

  static const int _limit = 100;

  @override
  Future<LoadInvitableFriendsOutput> buildUseCase(
    LoadInvitableFriendsInput input,
  ) async {
    //TODO: use api get list DM channels and Friends
    final result = await FriendViewClient().instance.listFriends(
          limit: _limit,
          nextPageToken: input.nextPageToken,
        );
    List<InvitableUser> friends = [];
    bool hasNext = false;
    String? nextPageToken;
    if (result.data?.ok == true) {
      friends = result.data?.includes?.users
              ?.toList()
              .map(
                (user) => InvitableUser(
                  userId: user.userId!,
                  username: user.username!,
                  avatar: user.profile?.avatar,
                  displayName: user.profile?.displayName,
                  aliasName: null,
                ),
              )
              .toList() ??
          [];
      nextPageToken = result.data?.paging?.nextPageToken;
      hasNext = result.data?.paging?.hasNext ?? false;
    }
    return LoadInvitableFriendsOutput(
      friends: friends,
      nextPageToken: nextPageToken,
      hasNext: hasNext,
    );
  }
}

class LoadInvitableFriendsInput extends BaseInput {
  const LoadInvitableFriendsInput({
    this.nextPageToken,
  });

  final String? nextPageToken;
}

class LoadInvitableFriendsOutput extends BaseOutput {
  final List<InvitableUser> friends;
  final bool hasNext;
  final String? nextPageToken;

  const LoadInvitableFriendsOutput({
    required this.friends,
    required this.hasNext,
    this.nextPageToken,
  });
}
