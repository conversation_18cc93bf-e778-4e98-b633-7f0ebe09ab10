import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class CheckAndInsertOrUpdateMessageUseCase extends BaseFutureUseCase<
    CheckInsertMessageInput, CheckInsertMessageOutput> {
  CheckAndInsertOrUpdateMessageUseCase(
    this._messageRepository,
  );

  final MessageRepository _messageRepository;

  @override
  Future<CheckInsertMessageOutput> buildUseCase(
    CheckInsertMessageInput input,
  ) async {
    final result =
        await _messageRepository.checkAndInsertOrUpdate(input.message);
    return CheckInsertMessageOutput(ok: result > 0, insertedId: result);
  }
}

class CheckInsertMessageInput extends BaseInput {
  CheckInsertMessageInput({required this.message});

  final Message message;
}

class CheckInsertMessageOutput extends BaseOutput {
  CheckInsertMessageOutput({required this.ok, this.insertedId});

  final bool? ok;
  final int? insertedId;
}
