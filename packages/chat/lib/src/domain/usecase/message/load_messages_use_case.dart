import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:message_view_api/message_view_api.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class LoadMessagesUseCase
    extends BaseFutureUseCase<LoadMessagesInput, LoadMessagesOutput> {
  LoadMessagesUseCase();

  @override
  Future<LoadMessagesOutput> buildUseCase(LoadMessagesInput input) async {
    final (messages, paging) = await _loadFromApi(input);

    return LoadMessagesOutput(
      messages: messages,
      hasNext: paging.hasNext!,
      nextPageToken: paging.nextPageToken,
    );
  }

  Future<(List<Message>, V3Paging)> _loadFromApi(
    LoadMessagesInput input,
  ) async {
    var jsonData;
    V3Paging paging;
    if (input.isDm()) {
      final response = await MessageViewClient().instance.listDMMessages(
            userId: input.userId,
            limit: input.limit,
            nextPageToken: input.nextPageToken,
            prevPageToken: input.prevPageToken,
          );
      paging = response.data!.paging!;

      jsonData = jsonDecode(
        standardSerializers.toJson(
          V3ListDMMessagesResponse.serializer,
          response.data,
        ),
      );
    } else {
      final response = await MessageViewClient().instance.listMessages(
            workspaceId: input.workspaceId,
            channelId: input.channelId,
            limit: input.limit,
            nextPageToken: input.nextPageToken,
            prevPageToken: input.prevPageToken,
          );
      paging = response.data!.paging!;

      jsonData = jsonDecode(
        standardSerializers.toJson(
          V3ListMessagesResponse.serializer,
          response.data,
        ),
      );
    }

    List<dynamic> listMessage = jsonData['data'];
    Map<String, dynamic> includes = jsonData['includes'];
    ResponseIncludes responseIncludes = ResponseIncludes.fromJson(includes);
    List<Message> messages = [];
    for (final obj in listMessage) {
      var jsonMessage = obj['message'];
      messages.add(
        MessageSerializer.serializeFromJson(
          data: jsonMessage,
          includes: responseIncludes.toJson(),
        )!,
      );
    }

    return (messages, paging);
  }
}

class LoadMessagesInput extends BaseInput {
  LoadMessagesInput({
    this.workspaceId,
    this.channelId,
    this.userId,
    this.limit = 500,
    this.nextPageToken,
    this.prevPageToken,
  });

  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final int limit;
  final String? nextPageToken;
  final String? prevPageToken;

  bool isDm() => userId != null;
}

class LoadMessagesOutput extends BaseOutput {
  LoadMessagesOutput({
    required this.messages,
    required this.hasNext,
    this.error = false,
    this.nextPageToken,
  });

  final List<Message> messages;
  final bool hasNext;
  final bool error;
  final String? nextPageToken;
}
