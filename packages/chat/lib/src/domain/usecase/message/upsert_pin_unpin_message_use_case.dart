import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class UpsertPinUnPinMessageUseCase extends BaseFutureUseCase<
    UpsertPinUnPinMessageInput, UpsertPinUnPinMessageOutput> {
  UpsertPinUnPinMessageUseCase(this._messageRepository);

  final MessageRepository _messageRepository;

  @override
  Future<UpsertPinUnPinMessageOutput> buildUseCase(
    UpsertPinUnPinMessageInput input,
  ) async {
    /// get pinned message
    var pinnedMessages = _messageRepository.getPinMessages(
      workspaceId: input.workspaceId,
      channelId: input.channelId,
    );
    List<Message> changePinnedMessage = pinnedMessages.map((item) {
      item.isPinned = false;
      item.pinTime = null;
      return item;
    }).toList();
    _messageRepository.forceUpdateMessageAll(changePinnedMessage);

    /// handle pinning message
    final pinningMessage = _messageRepository.getMessage(
      workspaceId: input.workspaceId,
      channelId: input.channelId,
      messageId: input.messageId,
    );
    if (pinningMessage == null) {
      return UpsertPinUnPinMessageOutput();
    }
    pinningMessage.isPinned = input.status;
    pinningMessage.pinTime = input.pinTime;
    _messageRepository.forceUpdateMessageAll([pinningMessage]);

    return UpsertPinUnPinMessageOutput(message: pinningMessage);
  }
}

class UpsertPinUnPinMessageInput extends BaseInput {
  UpsertPinUnPinMessageInput({
    required this.workspaceId,
    required this.channelId,
    required this.messageId,
    required this.status,
    this.pinTime,
  });

  final String workspaceId;
  final String channelId;
  final String messageId;
  final bool status;
  final DateTime? pinTime;
}

class UpsertPinUnPinMessageOutput extends BaseOutput {
  UpsertPinUnPinMessageOutput({this.message});

  final Message? message;
}
