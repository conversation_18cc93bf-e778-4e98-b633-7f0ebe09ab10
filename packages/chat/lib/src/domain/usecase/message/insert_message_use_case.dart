import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class InsertMessageUseCase
    extends BaseFutureUseCase<InsertMessageInput, InsertMessageOutput> {
  InsertMessageUseCase(
    this._messageRepository,
  );

  final MessageRepository _messageRepository;

  @override
  Future<InsertMessageOutput> buildUseCase(InsertMessageInput input) async {
    _messageRepository.insert(input.message);
    return InsertMessageOutput(ok: true);
  }
}

class InsertMessageInput extends BaseInput {
  InsertMessageInput({required this.message});

  final Message message;
}

class InsertMessageOutput extends BaseOutput {
  InsertMessageOutput({required this.ok});

  final bool? ok;
}
