import '../../../../chat.dart';
import 'package:flutter/cupertino.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

class UpdateAllPendingMessagesToFailedInput extends BaseInput {
  UpdateAllPendingMessagesToFailedInput();
}

class UpdateAllPendingMessagesToFailedOutput extends BaseOutput {
  final int updatedCount;

  UpdateAllPendingMessagesToFailedOutput({
    required this.updatedCount,
  });
}

@injectable
class UpdateAllPendingMessagesToFailedUseCase extends BaseFutureUseCase<
    UpdateAllPendingMessagesToFailedInput,
    UpdateAllPendingMessagesToFailedOutput> {
  final MessageRepository _messageRepository;

  UpdateAllPendingMessagesToFailedUseCase(this._messageRepository);

  @override
  Future<UpdateAllPendingMessagesToFailedOutput> buildUseCase(
    UpdateAllPendingMessagesToFailedInput input,
  ) async {
    try {
      // Get all pending messages
      final pendingMessages =
          _messageRepository.getMessagesByStatus(MessageStatus.PENDING);

      debugPrint(
        'UpdateAllPendingMessagesToFailedUseCase.buildUseCase: ${pendingMessages.length}',
      );
      pendingMessages.forEach((message) {
        debugPrint(
          'UpdateAllPendingMessagesToFailedUseCase.buildUseCase: ${message.messageId}',
        );
      });

      if (pendingMessages.isEmpty) {
        return UpdateAllPendingMessagesToFailedOutput(updatedCount: 0);
      }

      // Update all pending messages to failed
      final updatedMessages = pendingMessages.map((message) {
        message.messageStatusRaw = MessageStatus.FAILURE.rawValue();
        message.messageErrorReasonRaw = MessageErrorReason.OTHER.rawValue();
        return message;
      }).toList();

      // Save updated messages
      await _messageRepository.forceUpdateMessageAll(updatedMessages);

      return UpdateAllPendingMessagesToFailedOutput(
        updatedCount: updatedMessages.length,
      );
    } catch (e) {
      Log.e(
        name: 'UpdateAllPendingMessagesToFailedUseCase',
        'Error updating pending messages to failed: $e',
      );
      return UpdateAllPendingMessagesToFailedOutput(updatedCount: 0);
    }
  }
}
