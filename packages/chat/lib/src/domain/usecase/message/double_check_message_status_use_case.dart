import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

/// Input for the DoubleCheckMessageStatusUseCase
/// Contains a list of message references that should remain in pending state
class DoubleCheckMessageStatusInput extends BaseInput {
  final List<String> activeMessageRefs;

  DoubleCheckMessageStatusInput({
    required this.activeMessageRefs,
  });
}

/// Output for the DoubleCheckMessageStatusUseCase
/// Contains the count of messages that were updated to failed state
class DoubleCheckMessageStatusOutput extends BaseOutput {
  final int updatedCount;

  DoubleCheckMessageStatusOutput({
    required this.updatedCount,
  });
}

/// UseCase that checks all pending messages and updates those not in the active refs list to failed state
/// This ensures that messages that are no longer being processed are properly marked as failed
@injectable
class DoubleCheckMessageStatusUseCase extends BaseFutureUseCase<
    DoubleCheckMessageStatusInput, DoubleCheckMessageStatusOutput> {
  final MessageRepository _messageRepository;

  DoubleCheckMessageStatusUseCase(this._messageRepository);

  @override
  Future<DoubleCheckMessageStatusOutput> buildUseCase(
    DoubleCheckMessageStatusInput input,
  ) async {
    try {
      // Get all messages with pending status
      final allPendingMessages = _messageRepository.getMessagesByStatus(
        MessageStatus.PENDING,
      );

      if (allPendingMessages.isEmpty) {
        Log.d(
          name: 'DoubleCheckMessageStatusUseCase',
          'No pending messages found',
        );
        return DoubleCheckMessageStatusOutput(updatedCount: 0);
      }

      Log.d(
        name: 'DoubleCheckMessageStatusUseCase',
        'Found ${allPendingMessages.length} pending messages',
      );

      // Create a set of active message refs for efficient lookup
      final activeRefs = Set<String>.from(
          input.activeMessageRefs.where((ref) => ref.isNotEmpty));

      Log.d(
        name: 'DoubleCheckMessageStatusUseCase',
        'activeRefs: ${activeRefs}',
      );

      // Get current time for timeout check
      final now = DateTime.now();

      // Filter messages that should be updated to failed status
      final messagesToUpdate = allPendingMessages.where((message) {
        final messageRef = message.ref;

        // Check if message creation time exceeds timeout duration
        final isTimedOut = message.createTime != null &&
            now.difference(message.createTime!) >
                GlobalConfig.sendTimeoutDuration;

        // Log timeout information for debugging
        if (isTimedOut) {
          Log.d(
            name: 'DoubleCheckMessageStatusUseCase',
            'Message ${message.messageId} (ref: ${message.ref}) timed out. Created: ${message.createTime}, age: ${now.difference(message.createTime!).inSeconds}s',
          );
        }

        // Update to failed if:
        // 1. Message has no ref, or
        // 2. Message ref is not in active refs list, or
        // 3. Message creation time exceeds timeout duration
        return messageRef == null ||
            messageRef.isEmpty ||
            !activeRefs.contains(messageRef) ||
            isTimedOut;
      }).toList();

      if (messagesToUpdate.isEmpty) {
        Log.d(
          name: 'DoubleCheckMessageStatusUseCase',
          'No pending messages need to be updated to failed',
        );
        return DoubleCheckMessageStatusOutput(updatedCount: 0);
      }

      final timedOutCount = messagesToUpdate
          .where(
            (m) =>
                m.createTime != null &&
                now.difference(m.createTime!) >
                    GlobalConfig.sendTimeoutDuration,
          )
          .length;

      final notInActiveRefsCount = messagesToUpdate
          .where(
            (m) =>
                m.ref != null &&
                m.ref!.isNotEmpty &&
                !activeRefs.contains(m.ref),
          )
          .length;

      final noRefCount = messagesToUpdate
          .where(
            (m) => m.ref == null || m.ref!.isEmpty,
          )
          .length;

      Log.d(
        name: 'DoubleCheckMessageStatusUseCase',
        'Updating ${messagesToUpdate.length} pending messages to failed: ' +
            '$timedOutCount timed out, $notInActiveRefsCount not in active refs, $noRefCount with no ref',
      );

      // Update all filtered messages and their attachments to failed status
      final updatedMessages = messagesToUpdate.map((message) {
        // Update message status to failed
        message.messageStatusRaw = MessageStatus.FAILURE.rawValue();

        // Set appropriate error reason based on timeout
        final isTimedOut = message.createTime != null &&
            now.difference(message.createTime!) >
                GlobalConfig.sendTimeoutDuration;
        message.messageErrorReasonRaw = isTimedOut
            ? MessageErrorReason.TIMEOUT.rawValue()
            : MessageErrorReason.OTHER.rawValue();

        // Update all attachments to failed status
        for (final attachment in message.mediaAttachments) {
          if (attachment.attachmentStatus == AttachmentStatusEnum.UPLOADING) {
            attachment.attachmentStatusRaw =
                AttachmentStatusEnum.FAILURE.rawValue();
          }
        }

        return message;
      }).toList();

      // Save the updated messages
      await _messageRepository.forceUpdateMessageAll(updatedMessages);

      return DoubleCheckMessageStatusOutput(
        updatedCount: updatedMessages.length,
      );
    } catch (e) {
      Log.e(
        name: 'DoubleCheckMessageStatusUseCase',
        'Error updating pending messages to failed: $e',
      );
      return DoubleCheckMessageStatusOutput(updatedCount: 0);
    }
  }
}
