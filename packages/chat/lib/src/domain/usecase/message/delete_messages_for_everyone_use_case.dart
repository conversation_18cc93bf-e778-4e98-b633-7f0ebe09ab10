import 'package:built_collection/built_collection.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class DeleteMessagesForEveryoneUseCase extends BaseFutureUseCase<
    DeleteMessagesForEveryoneInput, DeleteMessagesForEveryoneOutput> {
  DeleteMessagesForEveryoneUseCase();

  @override
  Future<DeleteMessagesForEveryoneOutput> buildUseCase(
    DeleteMessagesForEveryoneInput input,
  ) async {
    var messageIds = BuiltList<String>.from(input.messageIds);
    var response;
    if (input.userId != null) {
      if (messageIds.length > 1) {
        response = await MessageClient().instance.deleteDMMessagesForEveryone(
              userId: input.userId,
              messageIds: messageIds,
            );
      } else {
        response = await MessageClient().instance.deleteDMMessageForEveryone(
              userId: input.userId,
              messageId: messageIds.first,
            );
      }
    } else {
      if (messageIds.length > 1) {
        response = await MessageClient().instance.deleteMessagesForEveryone(
              workspaceId: input.workspaceId,
              channelId: input.channelId,
              messageIds: messageIds,
            );
      } else {
        response = await MessageClient().instance.deleteMessageForEveryone(
              workspaceId: input.workspaceId,
              channelId: input.channelId,
              messageId: messageIds.first,
            );
      }
    }

    return DeleteMessagesForEveryoneOutput(ok: response.data?.ok ?? false);
  }
}

class DeleteMessagesForEveryoneInput extends BaseInput {
  DeleteMessagesForEveryoneInput({
    this.workspaceId,
    this.channelId,
    this.userId,
    required this.messageIds,
  });

  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final List<String> messageIds;
}

class DeleteMessagesForEveryoneOutput extends BaseOutput {
  DeleteMessagesForEveryoneOutput({
    required this.ok,
  });

  final bool ok;
}
