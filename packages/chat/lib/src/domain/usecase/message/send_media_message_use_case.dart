import 'dart:convert';

import 'package:built_collection/built_collection.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:message_api/message_api.dart';
import 'package:shared/shared.dart';
import 'package:upload_manager/upload_manager.dart';

import '../../../../chat.dart';

part 'send_media_message_use_case.freezed.dart';

@Injectable()
class SendMediaMessageUseCase
    extends BaseFutureUseCase<SendMediaMessageInput, SendMediaMessageOutput> {
  const SendMediaMessageUseCase(this._messageRepository);

  final MessageRepository _messageRepository;

  bool isDm(input) => input.userId != null;

  @protected
  @override
  Future<SendMediaMessageOutput> buildUseCase(
    SendMediaMessageInput input,
  ) async {
    Message? message;
    ListBuilder<V3MediaObject> mediaList = _prepareMediaList(input);

    if (isDm(input)) {
      final body = V3SendDmMessageMediaRequestBuilder()
        ..userId = input.userId
        ..ref = input.ref
        ..attachmentType = input.attachmentType
        ..mediaObjects = mediaList;

      final response = await MessageClient().instance.sendDmMessageMedia(
            body: body.build(),
          );

      if (response.data?.ok ?? false) {
        final json = jsonDecode(
          standardSerializers.toJson(
            V3SendDmMessageMediaResponse.serializer,
            response.data,
          ),
        );

        var apiResponse = APIResponse.fromJson(json);

        message = MessageSerializer.serializeFromJson(
          data: apiResponse.data!.message!.toJson(),
          includes: apiResponse.includes!.toJson(),
        );
      } else {
        final msg = response.data?.error?.message ?? '';
        Log.e("Create send media message false: ${msg}");
      }
    } else {
      final body = V3SendMessageMediaRequestBuilder()
        ..workspaceId = input.workspaceId
        ..channelId = input.channelId
        ..ref = input.ref
        ..attachmentType = input.attachmentType
        ..mediaObjects = mediaList;

      final response = await MessageClient().instance.sendMessageMedia(
            body: body.build(),
          );

      if (response.data?.ok ?? false) {
        final json = jsonDecode(
          standardSerializers.toJson(
            V3SendMessageMediaResponse.serializer,
            response.data,
          ),
        );

        var apiResponse = APIResponse.fromJson(json);

        message = MessageSerializer.serializeFromJson(
          data: apiResponse.data!.message!.toJson(),
          includes: apiResponse.includes!.toJson(),
        );
      } else {
        final msg = response.data?.error?.message ?? '';
        Log.e("Create send media message channel false: ${msg}");
      }
    }

    if (message != null) {
      _messageRepository.insert(message);
    }
    return SendMediaMessageOutput(message: message);
  }

  ListBuilder<V3MediaObject> _prepareMediaList(SendMediaMessageInput input) {
    final List<V3MediaObject> mediaList = [];

    final dimensions = input.file?.mediaMetaData?.dimensions == null
        ? null
        : (V3DimensionsBuilder()
          ..height = input.file?.mediaMetaData?.dimensions?.height.toInt()
          ..width = input.file?.mediaMetaData?.dimensions?.width.toInt());

    final fileMeta = V3FileMetadataBuilder()
      ..mimetype = input.file?.metaData?.type
      ..filename = input.file?.metaData?.name
      ..extension_ = input.file?.metaData?.fileType
      ..filesize = input.file?.metaData?.fileSize
      ..dimensions = dimensions
      ..duration = input.file?.mediaMetaData?.duration != null
          ? (input.file!.mediaMetaData!.duration! / 1000).toInt()
          : 0;

    final fileMedia = V3MediaObjectBuilder()
      ..fileUrl = input.file?.fileUrl
      ..fileRef = input.file?.fileRef
      ..attachmentType = input.attachmentType
      ..thumbnailUrl = input.file?.mediaMetaData?.thumbnailUrl
      ..fileMetadata = fileMeta;

    mediaList.add(fileMedia.build());
    return BuiltList<V3MediaObject>.from(mediaList).toBuilder();
  }
}

@freezed
sealed class SendMediaMessageInput extends BaseInput
    with _$SendMediaMessageInput {
  const SendMediaMessageInput._();
  factory SendMediaMessageInput({
    final String? workspaceId,
    final String? channelId,
    final String? userId,
    final String? ref,
    final FileToUpload? file,
    @Default(V3AttachmentTypeEnum.ATTACHMENT_TYPE_ENUM_PHOTO)
    V3AttachmentTypeEnum? attachmentType,
  }) = _SendMediaMessageInput;
}

@freezed
sealed class SendMediaMessageOutput extends BaseOutput
    with _$SendMediaMessageOutput {
  const SendMediaMessageOutput._();
  factory SendMediaMessageOutput({
    final Message? message,
  }) = _SendMediaMessageOutput;
}
