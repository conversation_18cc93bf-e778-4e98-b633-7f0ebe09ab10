import 'dart:convert';

import 'package:friend_view_api/friend_view_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class LoadFriendRequestsUseCase extends BaseFutureUseCase<
    LoadFriendRequestsInput, LoadFriendRequestsOutput> {
  LoadFriendRequestsUseCase();

  @override
  Future<LoadFriendRequestsOutput> buildUseCase(
    LoadFriendRequestsInput input,
  ) async {
    final sessionKey = Config.getInstance().activeSessionKey;
    try {
      final response =
          await FriendViewClient().instance.listInComingFriendRequests();
      final paging = response.data!.paging!;

      if (response.data?.ok != true) {
        return LoadFriendRequestsOutput(friends: [], hasNext: false);
      }
      final includes = jsonDecode(
        standardSerializers.toJson(
          V3DataInclude.serializer,
          response.data!.includes,
        ),
      );

      List<ChatUser> chatUsers = [];
      var responseIncludes = ResponseIncludes.fromJson(includes);
      final userIncludes = responseIncludes.users ?? [];
      for (final userInclude in userIncludes) {
        final userSer =
            ChatUserSerializer.serializeFromJson(data: userInclude.toJson());

        if (userSer != null && userSer.userId != sessionKey) {
          chatUsers.add(userSer);
        }
      }

      return LoadFriendRequestsOutput(
        friends: chatUsers,
        hasNext: paging.hasNext ?? false,
        nextPageToken: paging.nextPageToken,
      );
    } on Exception catch (_) {
      return LoadFriendRequestsOutput(friends: [], hasNext: false);
    }
  }
}

class LoadFriendRequestsInput extends BaseInput {
  LoadFriendRequestsInput({
    this.limit = 500,
    this.offset = 0,
    this.nextPageToken,
  });
  final int limit;
  final int offset;
  final String? nextPageToken;
}

class LoadFriendRequestsOutput extends BaseOutput {
  LoadFriendRequestsOutput({
    required this.friends,
    required this.hasNext,
    this.nextPageToken,
  });

  final List<ChatUser> friends;
  final bool hasNext;
  final String? nextPageToken;
}
