import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/chat_friend_repository.dart';

@Injectable()
class DeleteChatFriends
    extends BaseFutureUseCase<DeleteChatFriendsInput, DeleteChatFriendsOutput> {
  const DeleteChatFriends(this._chatFriendRepository);

  final ChatFriendRepository _chatFriendRepository;

  @protected
  @override
  Future<DeleteChatFriendsOutput> buildUseCase(
    DeleteChatFriendsInput input,
  ) async {
    input.usersId.forEach((userId) async {
      _chatFriendRepository.deleteChatFriendByUserId(
        userId,
      );
    });
    return DeleteChatFriendsOutput();
  }
}

class DeleteChatFriendsInput extends BaseInput {
  final List<String> usersId;

  DeleteChatFriendsInput({
    required this.usersId,
  });
}

class DeleteChatFriendsOutput extends BaseOutput {
  DeleteChatFriendsOutput();
}
