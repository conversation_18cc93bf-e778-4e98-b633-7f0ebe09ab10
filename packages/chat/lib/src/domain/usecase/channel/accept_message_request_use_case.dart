import 'dart:convert';

import 'package:channel_api/channel_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class AcceptMessageRequestUseCase extends BaseFutureUseCase<
    AcceptMessageRequestInput, AcceptMessageRequestOutput> {
  AcceptMessageRequestUseCase();

  @override
  Future<AcceptMessageRequestOutput> buildUseCase(
    AcceptMessageRequestInput input,
  ) async {
    try {
      final bodyBuilder = V3AcceptMessageRequestRequestBuilder();
      bodyBuilder.userId = input.userId;
      final response = await ChannelClient()
          .instance
          .acceptMessageRequest(body: bodyBuilder.build());
      if (response.data?.ok ?? false) {
        final includes = jsonDecode(
          standardSerializers.toJson(
            V3DataInclude.serializer,
            response.data!.includes,
          ),
        );

        final json = jsonDecode(
          standardSerializers.toJson(
            Sharedv3ChannelData.serializer,
            response.data!.data,
          ),
        );

        final channel = ChannelSerializer.serializeFromJson(
          data: json['channel'],
          metadata: json['channelMetadata'],
          includes: includes,
        );
        return AcceptMessageRequestOutput(
          channel: channel,
        );
      }
    } catch (ex) {
      Log.e(ex);
    }
    return AcceptMessageRequestOutput();
  }
}

class AcceptMessageRequestInput extends BaseInput {
  AcceptMessageRequestInput({
    required this.userId,
  });

  final String userId;
}

class AcceptMessageRequestOutput extends BaseOutput {
  AcceptMessageRequestOutput({
    this.channel,
  });

  final Channel? channel;
}
