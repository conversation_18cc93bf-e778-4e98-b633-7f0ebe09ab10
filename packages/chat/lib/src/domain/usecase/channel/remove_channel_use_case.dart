import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/channel_repository.dart';
import '../../../data/repositories/member_repository.dart';
import '../../../data/repositories/message_repository.dart';

@Injectable()
class RemoveChannelUseCase
    extends BaseFutureUseCase<RemoveChannelInput, RemoveChannelOutput> {
  RemoveChannelUseCase(
    this._channelRepository,
    this._messageRepository,
    this._memberRepository,
  );

  final ChannelRepository _channelRepository;
  final MessageRepository _messageRepository;
  final MemberRepository _memberRepository;

  @override
  Future<RemoveChannelOutput> buildUseCase(
    RemoveChannelInput input,
  ) async {
    _messageRepository.deleteAllMessageOnChannel(
      workspaceId: input.workspaceId,
      channelId: input.channelId,
    );
    _channelRepository.deleteChannel(
      workspaceId: input.workspaceId,
      channelId: input.channelId,
    );
    _memberRepository.deleteMembersOfChannel(
      workspaceId: input.workspaceId,
      channelId: input.channelId,
    );
    return RemoveChannelOutput(ok: true);
  }
}

class RemoveChannelInput extends BaseInput {
  RemoveChannelInput({
    required this.workspaceId,
    required this.channelId,
  });

  final String workspaceId;
  final String channelId;
}

class RemoveChannelOutput extends BaseOutput {
  RemoveChannelOutput({
    required this.ok,
  });

  final bool ok;
}
