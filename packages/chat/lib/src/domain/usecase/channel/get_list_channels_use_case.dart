import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/channel_repository.dart';
import '../../../data/repositories/database/entities/channel.dart';

@Injectable()
class GetListChannelsUseCase
    extends BaseSyncUseCase<GetListChannelsInput, GetListChannelsOutput> {
  GetListChannelsUseCase(
    this._chatRepository,
  );

  final ChannelRepository _chatRepository;

  @override
  GetListChannelsOutput buildUseCase(GetListChannelsInput input) {
    final channels = _chatRepository.getAllChannel(
      limit: input.limit,
      offset: input.offset,
    );
    return GetListChannelsOutput(
      channels: channels,
      hasNext: channels.length == input.limit,
      nextPageToken: null,
    );
  }
}

class GetListChannelsInput extends BaseInput {
  GetListChannelsInput({
    this.limit = 50,
    this.offset = 0,
    this.nextPageToken,
  });

  final int limit;
  final int offset;
  final String? nextPageToken;
}

class GetListChannelsOutput extends BaseOutput {
  GetListChannelsOutput({
    required this.channels,
    required this.hasNext,
    this.nextPageToken,
  });

  final List<Channel> channels;
  final bool hasNext;
  final String? nextPageToken;
}
