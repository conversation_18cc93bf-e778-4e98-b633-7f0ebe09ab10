import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';
import '../../../data/repositories/channel_metadata_repository.dart';

@Injectable()
class UpdateLastSeenMessageUseCase extends BaseFutureUseCase<
    UpdateLastSeenMessageInput, UpdateLastSeenMessageOutput> {
  UpdateLastSeenMessageUseCase(
    this._channelMetaDataRepository,
    this._channelRepository,
  );

  final ChannelMetaDataRepository _channelMetaDataRepository;
  final ChannelRepository _channelRepository;

  @override
  Future<UpdateLastSeenMessageOutput> buildUseCase(
    UpdateLastSeenMessageInput input,
  ) async {
    var workspaceId = input.workspaceId;
    var channelId = input.channelId;
    if (workspaceId == null && channelId == null) {
      final channel =
          _channelRepository.getDMChannel(recipientId: input.userId!)!;
      workspaceId = channel.workspaceId;
      channelId = channel.channelId;
    }
    _channelMetaDataRepository.updateChannelMetadata(
      workspaceId: workspaceId!,
      channelId: channelId!,
      lastSeenMessageId: input.lastSeenMessageId,
    );
    return UpdateLastSeenMessageOutput(ok: true);
  }
}

class UpdateLastSeenMessageInput extends BaseInput {
  UpdateLastSeenMessageInput({
    this.workspaceId,
    this.channelId,
    this.userId,
    required this.lastSeenMessageId,
  }) : assert(userId != null || (channelId != null && workspaceId != null));

  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final String lastSeenMessageId;
}

class UpdateLastSeenMessageOutput extends BaseOutput {
  UpdateLastSeenMessageOutput({required this.ok});

  final bool ok;
}
