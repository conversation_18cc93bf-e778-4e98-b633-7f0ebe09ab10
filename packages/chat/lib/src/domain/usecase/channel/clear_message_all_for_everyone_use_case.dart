import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class ClearMessageAllForEveryoneUseCase extends BaseFutureUseCase<
    ClearMessageAllForEveryoneInput, ClearMessageAllForEveryoneOutput> {
  ClearMessageAllForEveryoneUseCase();

  @override
  Future<ClearMessageAllForEveryoneOutput> buildUseCase(
    ClearMessageAllForEveryoneInput input,
  ) async {
    if (input.userId != null) {
      final respone = await MessageClient()
          .instance
          .deleteAllDMMessagesForEveryone(userId: input.userId);
      return ClearMessageAllForEveryoneOutput(ok: respone.data?.ok ?? false);
    } else {
      final respone = await MessageClient().instance.deleteAllMessagesOnlyMe(
            workspaceId: input.workspaceId,
            channelId: input.channelId,
          );
      return ClearMessageAllForEveryoneOutput(ok: respone.data?.ok ?? false);
    }
  }
}

class ClearMessageAllForEveryoneInput extends BaseInput {
  ClearMessageAllForEveryoneInput({
    this.workspaceId,
    this.channelId,
    this.userId,
  });

  final String? workspaceId;
  final String? channelId;
  final String? userId;
}

class ClearMessageAllForEveryoneOutput extends BaseOutput {
  ClearMessageAllForEveryoneOutput({
    required this.ok,
  });

  final bool ok;
}
