import 'dart:convert';

import 'package:channel_view_api/channel_view_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class LoadMessageRequestUseCase extends BaseFutureUseCase<
    LoadMessageRequestInput, LoadMessageRequestOutput> {
  LoadMessageRequestUseCase();

  @override
  Future<LoadMessageRequestOutput> buildUseCase(
    LoadMessageRequestInput input,
  ) async {
    final response =
        await ChannelViewClient().instance.listInComingMessageRequests(
              limit: input.limit,
              nextPageToken: input.nextPageToken,
            );

    final apiChannels = response.data?.data?.toList() ?? [];
    final paging = response.data!.paging!;

    final includes = jsonDecode(
      standardSerializers.toJson(
        V3DataInclude.serializer,
        response.data!.includes,
      ),
    );

    List<Channel> channels = [];
    for (final item in apiChannels) {
      final json = jsonDecode(
        standardSerializers.toJson(
          Sharedv3ChannelData.serializer,
          item,
        ),
      );

      final channel = ChannelSerializer.serializeFromJson(
        data: json['channel'],
        metadata: json['channelMetadata'],
        includes: includes,
      );
      channels.add(channel!);
    }
    return LoadMessageRequestOutput(
      channels: channels,
      hasNext: paging.hasNext ?? false,
      nextPageToken: paging.nextPageToken,
    );
  }
}

class LoadMessageRequestInput extends BaseInput {
  LoadMessageRequestInput({
    this.limit = 500,
    this.nextPageToken,
  });

  final int limit;
  final String? nextPageToken;
}

class LoadMessageRequestOutput extends BaseOutput {
  LoadMessageRequestOutput({
    required this.channels,
    required this.hasNext,
    this.nextPageToken,
  });

  final List<Channel> channels;
  final bool hasNext;
  final String? nextPageToken;
}
