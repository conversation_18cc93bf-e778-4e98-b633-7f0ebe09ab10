import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class DeleteChannelAvatarUseCase extends BaseFutureUseCase<
    DeleteChannelAvatarInput, DeleteChannelAvatarOutput> {
  DeleteChannelAvatarUseCase();

  @override
  Future<DeleteChannelAvatarOutput> buildUseCase(
    DeleteChannelAvatarInput input,
  ) async {
    final response = await ChannelClient().instance.deleteChannelAvatar(
          workspaceId: input.workspaceId,
          channelId: input.channelId,
        );

    return DeleteChannelAvatarOutput(ok: response.data?.ok ?? false);
  }
}

class DeleteChannelAvatarInput extends BaseInput {
  DeleteChannelAvatarInput({
    required this.workspaceId,
    required this.channelId,
  });

  final String workspaceId;
  final String channelId;
}

class DeleteChannelAvatarOutput extends BaseOutput {
  DeleteChannelAvatarOutput({
    required this.ok,
  });

  final bool ok;
}
