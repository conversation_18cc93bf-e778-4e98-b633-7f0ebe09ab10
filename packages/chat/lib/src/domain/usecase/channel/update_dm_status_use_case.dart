import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';
import '../../../data/repositories/database/enums/dm_status.dart';

@Injectable()
class UpdateDMStatusUseCase
    extends BaseSyncUseCase<UpdateDMStatusInput, UpdateDMStatusOutput> {
  UpdateDMStatusUseCase(this._channelRepository);

  final ChannelRepository _channelRepository;

  @override
  UpdateDMStatusOutput buildUseCase(
    UpdateDMStatusInput input,
  ) {
    final messageRequestExist = _channelRepository.getDMChannel(
      recipientId: input.userId,
    );
    if (messageRequestExist == null) {
      return UpdateDMStatusOutput();
    }

    messageRequestExist.dmStatus = DMStatusEnum.CONTACTED;
    _channelRepository.insert(messageRequestExist);
    return UpdateDMStatusOutput();
  }
}

class UpdateDMStatusInput extends BaseInput {
  UpdateDMStatusInput({
    required this.userId,
  });

  final String userId;
}

class UpdateDMStatusOutput extends BaseOutput {
  UpdateDMStatusOutput();
}
