import 'package:channel_api/channel_api.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

part 'update_channel_avatar_use_case.freezed.dart';

@Injectable()
class UpdateChannelAvatarUseCase extends BaseFutureUseCase<
    UpdateChannelAvatarInput, UpdateChannelAvatarOutput> {
  UpdateChannelAvatarUseCase();

  @protected
  @override
  Future<UpdateChannelAvatarOutput> buildUseCase(
    UpdateChannelAvatarInput input,
  ) async {
    final body = V3UpdateChannelAvatarRequestBuilder()
      ..workspaceId = input.workspaceId
      ..channelId = input.channelId
      ..avatarPath = input.avatarPath;

    final builtBody = body.build();

    final response = await ChannelClient().instance.updateChannelAvatar(
          body: builtBody,
        );

    if (response.data?.ok ?? false) {
      final channelData = response.data!.data!;

      return UpdateChannelAvatarOutput(
        success: true,
        avatarPath: channelData.channel!.avatar,
      );
    }
    return UpdateChannelAvatarOutput(
      success: false,
      error: response.data?.error,
    );
  }
}

@freezed
sealed class UpdateChannelAvatarInput extends BaseInput
    with _$UpdateChannelAvatarInput {
  const UpdateChannelAvatarInput._();
  factory UpdateChannelAvatarInput({
    required String? workspaceId,
    required String? channelId,
    required String? avatarPath,
  }) = _UpdateChannelAvatarInput;
}

@freezed
sealed class UpdateChannelAvatarOutput extends BaseOutput
    with _$UpdateChannelAvatarOutput {
  const UpdateChannelAvatarOutput._();
  factory UpdateChannelAvatarOutput({
    required bool success,
    String? avatarPath,
    final V3Error? error,
  }) = _UpdateChannelAvatarOutput;
}
