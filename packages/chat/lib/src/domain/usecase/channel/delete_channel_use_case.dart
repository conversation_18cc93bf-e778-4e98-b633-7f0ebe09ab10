import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class DeleteChannelUseCase
    extends BaseFutureUseCase<DeleteChannelInput, DeleteChannelOutput> {
  DeleteChannelUseCase(
    this._channelRepository,
    this._messageRepository,
    this._memberRepository,
  );

  final ChannelRepository _channelRepository;
  final MessageRepository _messageRepository;
  final MemberRepository _memberRepository;

  @override
  Future<DeleteChannelOutput> buildUseCase(
    DeleteChannelInput input,
  ) async {
    final response = await ChannelClient().instance.deleteChannel(
          workspaceId: input.workspaceId,
          channelId: input.channelId,
        );
    if (response.data?.ok ?? false) {
      _messageRepository.deleteAllMessageOnChannel(
        workspaceId: input.workspaceId,
        channelId: input.channelId,
      );
      _channelRepository.deleteChannel(
        workspaceId: input.workspaceId,
        channelId: input.channelId,
      );
      _memberRepository.deleteMembersOfChannel(
        workspaceId: input.workspaceId,
        channelId: input.channelId,
      );
      return DeleteChannelOutput(ok: true);
    }
    return DeleteChannelOutput(ok: false);
  }
}

class DeleteChannelInput extends BaseInput {
  DeleteChannelInput({
    required this.workspaceId,
    required this.channelId,
  });

  final String workspaceId;
  final String channelId;
}

class DeleteChannelOutput extends BaseOutput {
  DeleteChannelOutput({
    required this.ok,
  });

  final bool ok;
}
