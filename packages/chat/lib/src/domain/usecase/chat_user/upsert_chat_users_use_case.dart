import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class UpsertChatUsersUseCase
    extends BaseFutureUseCase<UpsertChatUsersInput, UpsertChatUsersOutput> {
  const UpsertChatUsersUseCase(this._userRepository);

  final ChatUserRepository _userRepository;

  @protected
  @override
  Future<UpsertChatUsersOutput> buildUseCase(UpsertChatUsersInput input) async {
    await _userRepository.forceInsertAll(input.users);
    return UpsertChatUsersOutput();
  }
}

class UpsertChatUsersInput extends BaseInput {
  final List<ChatUser> users;

  UpsertChatUsersInput({required this.users});
}

class UpsertChatUsersOutput extends BaseOutput {
  UpsertChatUsersOutput();
}
