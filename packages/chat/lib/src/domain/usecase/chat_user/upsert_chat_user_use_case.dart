import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class UpsertChatUserUseCase
    extends BaseFutureUseCase<UpsertChatUserInput, UpsertChatUserOutput> {
  const UpsertChatUserUseCase(this._userRepository);

  final ChatUserRepository _userRepository;

  @protected
  @override
  Future<UpsertChatUserOutput> buildUseCase(UpsertChatUserInput input) async {
    await _userRepository.forceInsert(input.user);
    return UpsertChatUserOutput();
  }
}

class UpsertChatUserInput extends BaseInput {
  final ChatUser user;

  UpsertChatUserInput({required this.user});
}

class UpsertChatUserOutput extends BaseOutput {
  UpsertChatUserOutput();
}
