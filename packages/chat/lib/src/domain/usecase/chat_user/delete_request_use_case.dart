import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

part 'delete_request_use_case.freezed.dart';

@Injectable()
class DeleteRequestUseCase
    extends BaseFutureUseCase<DeleteRequestInput, DeleteRequestOutput> {
  @protected
  @override
  Future<DeleteRequestOutput> buildUseCase(
    DeleteRequestInput input,
  ) async {
    try {
      final result = await FriendClient()
          .instance
          .deleteFriendRequest(userId: input.userId);

      if (result.data?.ok ?? false) {
        await GetIt.instance.get<DeleteChatFriendByUserId>().execute(
              DeleteChatFriendByUserIdInput(userId: input.userId!),
            );

        return DeleteRequestOutput(code: null, message: null);
      }
      return DeleteRequestOutput(
          code: result.data?.error?.code, message: result.data?.error?.message,);
    } on Exception catch (_) {
      return DeleteRequestOutput();
    }
  }
}

@freezed
sealed class DeleteRequestInput extends BaseInput with _$DeleteRequestInput {
  const DeleteRequestInput._();

  factory DeleteRequestInput({@Default('') String? userId}) =
      _DeleteRequestInput;
}

@freezed
sealed class DeleteRequestOutput extends BaseOutput with _$DeleteRequestOutput {
  const DeleteRequestOutput._();

  factory DeleteRequestOutput({
    @Default(null) int? code,
    @Default(null) String? message,
  }) = _DeleteRequestOutput;
}
