import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class UpsertChatFriendsUseCase
    extends BaseFutureUseCase<UpsertChatFriendsInput, UpsertChatFriendsOutput> {
  const UpsertChatFriendsUseCase(this._chatFriendRepository);

  final ChatFriendRepository _chatFriendRepository;

  @protected
  @override
  Future<UpsertChatFriendsOutput> buildUseCase(
    UpsertChatFriendsInput input,
  ) async {
    if(input.friends.isEmpty){
      var friendsRequest =_chatFriendRepository.getFriendRequests();
      friendsRequest.forEach((item){
        /// update all friend request local will become not friend
        item.status = 1;
      });
      List<int> ids = await _chatFriendRepository.forceInsertAll(friendsRequest);
      return UpsertChatFriendsOutput(total: ids.length);
    }
    final friends = input.friends
        .map(
          (friend) =>
              friend..sessionKey = Config.getInstance().activeSessionKey ?? '',
        )
        .toList();

    List<int> ids = await _chatFriendRepository.forceInsertAll(friends);
    return UpsertChatFriendsOutput(total: ids.length);
  }
}

class UpsertChatFriendsInput extends BaseInput {
  final List<ChatFriend> friends;

  UpsertChatFriendsInput({required this.friends});
}

class UpsertChatFriendsOutput extends BaseOutput {
  final int total;

  UpsertChatFriendsOutput({required this.total});
}
