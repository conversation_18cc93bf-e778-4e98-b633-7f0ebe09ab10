import 'dart:convert';

import 'package:app_core/core.dart';
import 'package:dio/dio.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:friend_api/friend_api.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';
import '../../../data/repositories/database/enums/chat_friend_status.dart';

part 'unfriend_use_case.freezed.dart';

@Injectable()
class UnfriendUseCase extends BaseFutureUseCase<UnfriendInput, UnfriendOutput> {
  @protected
  @override
  Future<UnfriendOutput> buildUseCase(
    UnfriendInput input,
  ) async {
    try {
      var body = V3UnfriendRequestBuilder()..userId = input.user?.userId;
      final result = await FriendClient().instance.unfriend(body: body.build());

      if (result.data?.ok ?? false) {
        final user = input.user
          ?..chatFriendDataRaw = jsonEncode(
            ChatFriendData(
              status: ChatFriendStatusEnum.NOT_FRIEND,
            ).toJson(),
          );

        await GetIt.instance.get<UpsertChatUserUseCase>().execute(
              UpsertChatUserInput(user: user!),
            );
        await GetIt.instance.get<ConvertChatUserAndUpsertUserUseCase>().execute(
              ConvertChatUserAndUpsertUserInput(chatUser: user),
            );
        await GetIt.instance.get<DeleteChatFriendByUserId>().execute(
              DeleteChatFriendByUserIdInput(
                userId: user.userId,
              ),
            );

        return UnfriendOutput(
          user: user,
        );
      }
      return UnfriendOutput(
        user: null,
        code: result.data?.error?.code,
        message: result.data?.error?.message,
      );
    } on DioException catch (e) {
      final statusCode = e.response?.statusCode;
      final message = e.message;
      return UnfriendOutput(
        user: null,
        code: statusCode,
        message: message,
      );
    } on Exception catch (ex) {
      if (ex is AppUncaughtException && ex.rootError is DioException) {
        final rootEx = ex.rootError as DioException;
        final statusCode = rootEx.response?.statusCode;
        final message = rootEx.message;
        return UnfriendOutput(
          user: null,
          code: statusCode,
          message: message,
        );
      }
      return UnfriendOutput(
        user: null,
      );
    }
  }
}

@freezed
sealed class UnfriendInput extends BaseInput with _$UnfriendInput {
  const UnfriendInput._();
  factory UnfriendInput({@Default(null) ChatUser? user}) = _UnfriendInput;
}

@freezed
sealed class UnfriendOutput extends BaseOutput with _$UnfriendOutput {
  const UnfriendOutput._();
  factory UnfriendOutput({
    @Default(null) ChatUser? user,
    @Default(null) int? code,
    @Default(null) String? message,
  }) = _UnfriendOutput;
}
