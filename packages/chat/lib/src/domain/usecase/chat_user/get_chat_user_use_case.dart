import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_view_api/user_view_api.dart';

import '../../../../chat.dart';

part 'get_chat_user_use_case.freezed.dart';

@Injectable()
class GetChatUserUseCase
    extends BaseFutureUseCase<GetChatUserInput, GetChatUserOutput> {
  const GetChatUserUseCase();

  @protected
  @override
  Future<GetChatUserOutput> buildUseCase(
    GetChatUserInput input,
  ) async {
    final sessionKey = Config.getInstance().activeSessionKey!;
    final user = input.userId == sessionKey
        ? await _getMe(input)
        : await _getUser(input, sessionKey);
    return GetChatUserOutput(user: user);
  }

  Future<ChatUser?> _getUser(GetChatUserInput input, String sessionKey) async {
    try {
      final result =
          await UserViewClient().instance.getUser(userId: input.userId);
      if (result.data?.ok ?? false) {
        final json = jsonDecode(
          standardSerializers.toJson(
            V3UserView.serializer,
            result.data!.data,
          ),
        );
        json['sessionKey'] = sessionKey;
        return ChatUser.fromJson(json);
      }
      return null;
    } catch (_) {
      return null;
    }
  }

  Future<ChatUser?> _getMe(GetChatUserInput input) async {
    try {
      final result = await UserViewClient().instance.getMe();
      if (result.data?.ok ?? false) {
        final json = jsonDecode(
          standardSerializers.toJson(
            V3Me.serializer,
            result.data!.data,
          ),
        );
        json['sessionKey'] = input.userId;
        return ChatUser.fromJson(json);
      }
      return null;
    } catch (_) {
      return null;
    }
  }
}

@freezed
sealed class GetChatUserInput extends BaseInput with _$GetChatUserInput {
  const GetChatUserInput._();
  factory GetChatUserInput({required String userId}) = _GetChatUserInput;
}

@freezed
sealed class GetChatUserOutput extends BaseOutput with _$GetChatUserOutput {
  const GetChatUserOutput._();
  factory GetChatUserOutput({
    @Default(null) ChatUser? user,
  }) = _GetChatUserOutput;
}
