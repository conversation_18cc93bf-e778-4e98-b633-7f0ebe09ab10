import 'dart:convert';

import 'package:shared/shared.dart';
import 'package:sticker/src/data/repositories/database/entities/sticker.dart';

import '../../chat.dart';
import '../data/repositories/database/classes/location_data.dart';
import '../data/repositories/database/classes/sticker_object.dart';
import '../data/repositories/database/enums/message_type.dart';

class TempMessageFactory {
  static Message createTextMessage({
    required String? workspaceId,
    required String? channelId,
    required String? userId,
    required String content,
  }) {
    return createBaseMessage(
      workspaceId: workspaceId,
      channelId: channelId,
      userId: userId,
      content: content,
      messageViewType: MessageViewType.textOwner,
      messageType: MessageType.DEFAULT,
    );
  }

  static Message createQuoteMessage({
    required String? workspaceId,
    required String? channelId,
    required String? userId,
    required String content,
    required String? originalMessageRaw,
  }) {
    return createBaseMessage(
      workspaceId: workspaceId,
      channelId: channelId,
      userId: userId,
      content: content,
      messageViewType: MessageViewType.textOwner,
      messageType: MessageType.DEFAULT,
      originalMessageRaw: originalMessageRaw,
    );
  }

  static Message createStickerMessage({
    required String? workspaceId,
    required String? channelId,
    required String? userId,
    required Sticker sticker,
  }) {
    var baseMessage = createBaseMessage(
      workspaceId: workspaceId,
      channelId: channelId,
      userId: userId,
      content: GlobalConfig.CONTENT_STICKER.format([sticker.defaultEmoji]),
      messageViewType: MessageViewType.stickerOwner,
      messageType: MessageType.DEFAULT,
    );

    String ref = RandomUtils.randomUlId();

    Attachment mediaAttachment = Attachment(
      attachmentId: ref,
      ref: ref,
      isTemp: true,
      attachmentStatusRaw: AttachmentStatusEnum.UPLOADING.rawValue(),
    );
    mediaAttachment.sticker = StickerObject(
      collectionId: sticker.collectionId,
      stickerId: sticker.stickerId,
      attachmentType: AttachmentType.STICKER.rawValue(),
      stickerUrl: sticker.stickerUrl,
      attachmentId: mediaAttachment.attachmentId,
      fileRef: ref,
    );

    baseMessage.mediaAttachments.add(mediaAttachment);
    baseMessage.attachmentCount = baseMessage.mediaAttachments.length;
    return baseMessage;
  }

  static Message createPokeMessage({
    required String? workspaceId,
    required String? channelId,
    required String? userId,
    required Sticker sticker,
  }) {
    var baseMessage = createBaseMessage(
      workspaceId: workspaceId,
      channelId: channelId,
      userId: userId,
      content: GlobalConfig.CONTENT_POKED,
      messageViewType: MessageViewType.stickerOwner,
      messageType: MessageType.DEFAULT,
    );

    String ref = RandomUtils.randomUlId();

    Attachment mediaAttachment = Attachment(
      attachmentId: ref,
      ref: ref,
      isTemp: true,
      attachmentStatusRaw: AttachmentStatusEnum.UPLOADING.rawValue(),
    );
    mediaAttachment.sticker = StickerObject(
      collectionId: sticker.collectionId,
      stickerId: sticker.stickerId,
      attachmentType: AttachmentType.STICKER.rawValue(),
      stickerUrl: sticker.stickerUrl,
      attachmentId: mediaAttachment.attachmentId,
      fileRef: ref,
    );

    baseMessage.mediaAttachments.add(mediaAttachment);
    baseMessage.attachmentCount = baseMessage.mediaAttachments.length;
    return baseMessage;
  }

  static Message createPhotosMessage({
    required String? workspaceId,
    required String? channelId,
    required String? userId,
    required Sticker sticker,
  }) {
    var baseMessage = createBaseMessage(
      workspaceId: workspaceId,
      channelId: channelId,
      userId: userId,
      content: GlobalConfig.CONTENT_STICKER,
      messageViewType: MessageViewType.stickerOwner,
      messageType: MessageType.DEFAULT,
    );

    Attachment mediaAttachment = Attachment(
      attachmentId: baseMessage.ref!,
      ref: baseMessage.ref!,
      isTemp: true,
      attachmentStatusRaw: AttachmentStatusEnum.UPLOADING.rawValue(),
    );
    mediaAttachment.sticker = StickerObject(
      collectionId: sticker.collectionId,
      stickerId: sticker.stickerId,
      attachmentType: AttachmentType.STICKER.rawValue(),
      stickerUrl: sticker.stickerUrl,
      attachmentId: RandomUtils.randomUlId(),
      fileRef: RandomUtils.randomId(),
    );

    baseMessage.mediaAttachments.add(mediaAttachment);
    baseMessage.attachmentCount = baseMessage.mediaAttachments.length;
    return baseMessage;
  }

  static Message createSystemTimeMessage({
    required String? workspaceId,
    required String? channelId,
    required DateTime createTime,
  }) {
    var baseMessage = createBaseMessage(
      workspaceId: workspaceId,
      channelId: channelId,
      userId: Config.getInstance().activeSessionKey,
      content: createTime.toIso8601String(),
      messageViewType: MessageViewType.systemTime,
      messageType: MessageType.DEFAULT,
    );

    baseMessage.createTime = createTime;

    return baseMessage;
  }

  static Message createSystemMessage({
    required String? workspaceId,
    required String? channelId,
    required DateTime? createTime,
    required String content,
  }) {
    var baseMessage = createBaseMessage(
      workspaceId: workspaceId,
      channelId: channelId,
      userId: Config.getInstance().activeSessionKey,
      content: content,
      messageViewType: MessageViewType.system,
      messageType: MessageType.DEFAULT,
      createTime: createTime,
    );

    baseMessage.createTime = createTime;

    return baseMessage;
  }

  static Message createLocationMessage({
    required String? workspaceId,
    required String? channelId,
    required String? userId,
    required String address,
    required double? latitude,
    required double? longitude,
  }) {
    var baseMessage = createBaseMessage(
      workspaceId: workspaceId,
      channelId: channelId,
      userId: userId,
      content: address,
      messageViewType: MessageViewType.locationOwner,
      messageType: MessageType.DEFAULT,
    );
    Embed embed = Embed(
      provider: "",
      type: 1,
      locationData: LocationData(
        latitude: latitude,
        longitude: longitude,
        description: address,
        thumbnailUrl: "",
      ),
    );
    baseMessage.embedRaw = jsonEncode([embed.toJson()]);
    return baseMessage;
  }

  static Message createBaseMessage({
    required String? workspaceId,
    required String? channelId,
    required String? userId,
    required String content,
    required MessageViewType messageViewType,
    MessageType messageType = MessageType.DEFAULT,
    DateTime? createTime,
    String? originalMessageRaw,
  }) {
    final msgRef = RandomUtils.randomUlId();
    return Message(
      workspaceId: workspaceId ?? '',
      channelId: channelId ?? '',
      messageId: msgRef,
      sessionKey: Config.getInstance().activeSessionKey ?? '',
      userId: Config.getInstance().activeSessionKey!,
      messageViewTypeRaw: messageViewType.rawValue(),
      messageTypeRaw: messageType.rawValue(),
      messageStatusRaw: MessageStatus.PENDING.rawValue(),
      attachmentTypeRaw: AttachmentType.UNSPECIFIED.rawValue(),
      isThread: false,
      reportCount: 0,
      isReported: false,
      attachmentCount: 0,
      content: content,
      contentLocale: 'UNS',
      ref: msgRef,
      createTime: createTime ?? TimeUtils.now(),
      updateTime: TimeUtils.now(),
      isTemp: true,
      originalMessageRaw: originalMessageRaw,
    );
  }
}
