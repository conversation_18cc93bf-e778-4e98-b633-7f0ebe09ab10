import 'package:get_it/get_it.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';

class TranslateContentUtils {
  static String translateContent(
    String content,
    List<String> contentArguments,
  ) {
    final localizations = GetIt.instance.get<AppLocalizations>();

    if (content.contains('%s sent an invitation')) {
      final argument = contentArguments.length < 1 ? '%s' : contentArguments[0];
      return content.replaceAll(
        '%s sent an invitation',
        localizations.sentAnInvitation(argument),
      );
    }
    if (content.contains('Sticker')) {
      return content.replaceAll(
        'Sticker',
        localizations.stickerMessage,
      );
    }
    if (content.contains('Photo(s)')) {
      return content.replaceAll(
        'Photo(s)',
        localizations.photos,
      );
    }
    if (content.contains('Video(s)')) {
      return content.replaceAll(
        'Video(s)',
        localizations.videos,
      );
    }
    if (content.contains('Location')) {
      return content.replaceAll(
        'Location',
        localizations.locationMessage,
      );
    }
    if (content.contains('File')) {
      return content.replaceAll(
        'File',
        localizations.file,
      );
    }
    if (content.contains('Poked')) {
      return content.replaceAll(
        'Poked',
        localizations.poked,
      );
    }

    switch (content) {
      case '%s sent an invitation':
        return localizations.sentAnInvitation(contentArguments[0]);
      case 'Welcome to ZiiChat! We\'re glad to meet you here. ZiiChat offers a unique experience which enables you to share your moments promptly through short-form videos in a safer and easier way. Also, QR codes make it simple and quick to meet new people.':
        return localizations.welcomeToZiiChatWereGladToMeetYou;
      case '%s created this channel':
        return localizations.createdThisChannel(contentArguments[0]);
      case '%s joined this channel':
        return localizations.joinedThisChannel(contentArguments[0]);
      case '%s set a nickname for %s to %s':
        return localizations.setANicknameForTo(
          contentArguments[0],
          contentArguments[1],
          contentArguments[2],
        );
      case '%s left this channel':
        return localizations.leftThisChannel(contentArguments[0]);
      case '%s assigned %s as an admin':
        return localizations.assignedAsAnAdmin(
          contentArguments[0],
          contentArguments[1],
        );
      case '%s removed the admin right of %s':
        return localizations.removedTheAdminRightOf(
          contentArguments[0],
          contentArguments[1],
        );
      case '%s removed the nickname of %s':
        return localizations.removedTheNicknameOf(
          contentArguments[0],
          contentArguments[1],
        );
      case '%s transferred channel ownership to %s':
        return localizations.transferredChannelOwnershipTo(
          contentArguments[0],
          contentArguments[1],
        );
      case '%s boosted this channel':
        return localizations.boostedThisChannel(contentArguments[0]);
      case '%s canceled boost this channel':
        return localizations.canceledBoostThisChannel(contentArguments[0]);
      case '%s wants to share media':
        return localizations.wantsToShareMedia(contentArguments[0]);
      case '%s pinned a message':
        return localizations.pinnedAMessage(contentArguments[0]);
      case '%s unpinned a message':
        return localizations.unpinnedAMessage(contentArguments[0]);
      case '%s stick a message to the top.':
        return localizations.stickAMessageToTheTop(contentArguments[0]);
      case '%s unstick a message to the top.':
        return localizations.unstickAMessageToTheTop(contentArguments[0]);
      case '%s sent you a friend request':
        return localizations.sentYouAFriendRequest(contentArguments[0]);
      case '%s changed this channel name to %s':
        return localizations.changedThisChannelNameTo(
          contentArguments[0],
          contentArguments[1],
        );
      case '%s removed %s from this channel':
        return localizations.removedFromThisChannel(
          contentArguments[0],
          contentArguments[1],
        );
      case '%s viewed your personal profile':
        return localizations.viewedYourPersonalProfile(contentArguments[0]);
      case '%s created this broadcast channel':
        return localizations.createdThisBroadcastChannel(contentArguments[0]);
      case "This person isn't receiving messages right now":
        return localizations.thisPersonIsntReceivingMessagesRightNow;
      case "You’ve reached the maximum message limit for strangers. Please wait for this person to accept your message before continuing the conversation.":
        return localizations.youHaveReachedTheMaximumMessageLimitForStrangers;
      case "%s changed this channel avatar":
        return localizations.changedThisChannelAvatar(contentArguments[0]);
      case "%s removed the channel's profile avatar":
        return localizations.removeChannelAvatar(contentArguments[0]);
      case "%s accepted your friend request":
        return localizations
            .acceptedYourFriendRequestWithParam(contentArguments[0]);
      case "%s pinned a message.":
        return localizations.usernamePinnedAMessage(contentArguments[0]);
      case "%s unpinned a message.":
        return localizations.usernameUnpinnedAMessage(contentArguments[0]);
      case "%s starts a call":
        return localizations.usernameStartsAVideoCall(contentArguments[0]);
      case "Call ended":
        return localizations.callEnded;
      case "Channel created":
        return localizations.channelCreated;
      default:
        return content;
    }
  }
}
