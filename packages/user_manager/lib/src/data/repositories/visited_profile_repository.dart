import 'dart:async';

import 'database/entities/visited_profile.dart';

abstract class VisitedProfileRepository {
  int insert(VisitedProfile visitedProfile);

  Future<List<int>> insertVisitedProfiles(List<VisitedProfile> visitedProfile);

  Stream<List<VisitedProfile>> getVisitedProfileStream();

  List<VisitedProfile> getVisitedProfiles();

  VisitedProfile? getVisitedProfile(String userId);

  bool deleteVisitedProfile(String userId);

  StreamSubscription observerVisitedProfile({
    required void Function(List<VisitedProfile>? visitedProfile) listener,
  });

  int forceInsert(VisitedProfile visitedProfile);
  List<int> updateAllIsReadVisitedProfile(bool isRead);
}
