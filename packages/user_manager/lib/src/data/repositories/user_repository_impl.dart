import 'dart:async';

import 'package:app_core/core.dart';
import 'package:injectable/injectable.dart';

import 'cache/user_modification_cache.dart';
import 'database/entities/user.dart';
import 'database/entities/user_status.dart';
import 'database/generated/objectbox.g.dart';
import 'database/user_database.dart';
import 'user_repository.dart';

@LazySingleton(as: UserRepository)
class UserRepositoryImpl extends UserRepository {
  UserRepositoryImpl(this._store, this._cache);

  final UserStore _store;
  final UserModificationCache _cache;

  Box<User> get _userBox => _store.box<User>();

  @override
  int insert(User user) {
    final usersToUpdate = filterUsersToUpdate([user]);

    if (usersToUpdate.isNotEmpty) {
      _existsAndUpdate(usersToUpdate, keep: true);
      updateCacheForUser(usersToUpdate.first);
      return _userBox.put(usersToUpdate.first);
    }
    return user.id;
  }

  void _handleKeepFriendData(User newUser, User oldUser) {
    newUser.id = newUser.id;
    if (newUser.friendData == null) {
      newUser.friendData = oldUser.friendData;
    }
  }

  @override
  Future<List<int>> insertAll(List<User> users) async {
    final usersToUpdate = filterUsersToUpdate(users);

    if (usersToUpdate.isNotEmpty) {
      _existsAndUpdate(usersToUpdate, keep: true);
      usersToUpdate.forEach(updateCacheForUser);
      return _userBox.putMany(usersToUpdate);
    }

    return users.map((user) => user.id).toList();
  }

  List<User> filterUsersToUpdate(List<User> users) {
    return users.where(shouldInsertUser).toList();
  }

  bool shouldInsertUser(User user) {
    final cachedTime = _cache.peekCached(
      sessionKey: Config.getInstance().activeSessionKey ?? '',
      userId: user.userId,
    );
    return cachedTime != user.updateTime;
  }

  void updateCacheForUser(User user) {
    if (user.updateTime == null) return;

    _cache.setCache(
      sessionKey: Config.getInstance().activeSessionKey ?? '',
      userId: user.userId,
      updateTime: user.updateTime!,
    );
  }

  @override
  Stream<List<User>> getUsersStream() {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    return _userBox
        .query(User_.sessionKey.equals(sessionKey))
        .watch(triggerImmediately: true)
        .map((query) => query.find());
  }

  @override
  List<User> getUsers() {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    return _userBox.query(User_.sessionKey.equals(sessionKey)).build().find();
  }

  @override
  User? getUser(String userId) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final query = _userBox
        .query(
          User_.sessionKey.equals(sessionKey) & User_.userId.equals(userId),
        )
        .build();
    final session = query.findFirst();
    query.close();
    return session;
  }

  @override
  Stream<User?> getUserStream(String userId) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    return _userBox
        .query(
          User_.sessionKey.equals(sessionKey).and(User_.userId.equals(userId)),
        )
        .watch(triggerImmediately: true)
        .map((query) => query.findFirst());
  }

  @override
  bool deleteUser(String userId) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final query = _userBox
        .query(
          User_.sessionKey.equals(sessionKey) & User_.userId.equals(userId),
        )
        .build();
    final session = query.findFirst();
    if (session != null) {
      return _userBox.remove(session.id);
    }
    return false;
  }

  void _existsAndUpdate(List<User> users, {bool? keep}) {
    users = removeDuplicateUsers(users);
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final existingQuery = _userBox
        .query(
          User_.sessionKey.equals(sessionKey).and(
                User_.userId.oneOf(users.map((u) => u.userId).toSet().toList()),
              ),
        )
        .build();

    final existingUsers = existingQuery.find();
    existingQuery.close();
    if (existingUsers.length == 0) return;

    for (final user in users) {
      final existingUser = existingUsers.where(
        (e) => e.userId == user.userId,
      );
      if (existingUser.isEmpty) continue;
      user.id = existingUser.first.id;
      user.userConnectLink =
          user.userConnectLink ?? existingUser.first.userConnectLink;

      if (keep == true) {
        _handleKeepFriendData(user, existingUser.first);
      }
      if (user.partial == true) {
        user.statusData = existingUser.first.statusData;
      }
    }
  }

  List<User> removeDuplicateUsers(List<User> users) {
    final seenUserIds = <String>{};
    return users.where((user) => seenUserIds.add(user.userId)).toList();
  }

  @override
  User? getUserByUsername(String username) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final query = _userBox
        .query(
          User_.sessionKey.equals(sessionKey) & User_.username.equals(username),
        )
        .build();
    final user = query.findFirst();
    query.close();
    return user;
  }

  @override
  int updateUserConnectLink(String userId, String link) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final existingQuery = _userBox
        .query(
          User_.sessionKey.equals(sessionKey).and(
                User_.userId.equals(userId),
              ),
        )
        .build();

    final existingUser = existingQuery.findFirst();
    existingQuery.close();
    if (existingUser != null) {
      existingUser.userConnectLink = link;
      return _userBox.put(existingUser);
    }
    return 0;
  }

  @override
  int updateAvatar(String userId, String avatarPath) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final existingQuery = _userBox
        .query(
          User_.sessionKey.equals(sessionKey).and(
                User_.userId.equals(userId),
              ),
        )
        .build();

    final existingUser = existingQuery.findFirst();
    existingQuery.close();
    if (existingUser != null) {
      existingUser.profile?.avatar = avatarPath;
      existingUser.profile?.originalAvatar = avatarPath;
      return _userBox.put(existingUser);
    }
    return 0;
  }

  @override
  StreamSubscription observerUser({
    required void Function(User? user) listener,
  }) {
    final sessionKey = Config.getInstance().activeSessionKey ?? '';

    final watchedQuery = _userBox
        .query(
          User_.sessionKey.equals(sessionKey) & User_.userId.equals(sessionKey),
        )
        .watch(triggerImmediately: true);

    return watchedQuery.listen((userQuery) {
      final user = userQuery.findFirst();
      listener(user);
    });
  }

  @override
  List<User> getUsersMySessionKeys(List<String> sessionKeys) {
    final query = _userBox
        .query(
          User_.sessionKey
              .oneOf(sessionKeys)
              .and(User_.userId.oneOf(sessionKeys)),
        )
        .build();
    final users = query.find();
    query.close();
    return users;
  }

  @override
  int updateCover(String userId, String coverPath) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final existingQuery = _userBox
        .query(
          User_.sessionKey.equals(sessionKey).and(
                User_.userId.equals(userId),
              ),
        )
        .build();

    final existingUser = existingQuery.findFirst();
    existingQuery.close();
    if (existingUser != null) {
      existingUser.profile?.cover = coverPath;
      return _userBox.put(existingUser);
    }
    return 0;
  }

  @override
  int forceInsert(User user) {
    _existsAndUpdate([user]);
    updateCacheForUser(user);
    return _userBox.put(user);
  }

  @override
  List<int> forceInserts(List<User> users) {
    _existsAndUpdate(users);
    users.forEach(updateCacheForUser);
    return _userBox.putMany(users);
  }

  @override
  bool deleteSession(String sessionKey) {
    final query = _userBox.query(User_.sessionKey.equals(sessionKey)).build();

    final removedCount = query.remove();

    query.close();
    return removedCount > 0;
  }

  @override
  void updateMyStatus(UserStatus? status) {
    final me = getUser(Config.getInstance().activeSessionKey ?? '');
    if (me != null) {
      me.statusData = status;
      _userBox.put(me);
    }
  }
}
