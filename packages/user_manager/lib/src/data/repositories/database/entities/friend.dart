import 'package:json_annotation/json_annotation.dart';

import '../../../../../user_manager.dart';

part 'friend.g.dart';

@Entity()
@JsonSerializable(explicitToJson: true)
class Friend {
  Friend({
    required this.friendId,
    required this.sessionKey,
    required this.userId,
    this.requestedFromUserId,
    this.requestedToUserId,
    this.statusRow,
    this.createTime,
    this.updateTime,
  }) : status = FriendStatusEnumExtension.getEnumByValue(statusRow);

  factory Friend.fromJson(Map<String, dynamic> json) {
    final friend = _$FriendFromJson(json);
    friend.statusRow = json['statusRow'];
    return friend;
  }

  Map<String, dynamic> toJson() {
    final json = _$FriendToJson(this);
    json['statusRow'] = status != null ? status!.toValue() : null;
    return json;
  }

  @Id(assignable: true)
  @JsonKey(defaultValue: 0)
  int id = 0;

  @Property(uid: 9001)
  String friendId;

  @Index()
  @Property(uid: 9002)
  String sessionKey;

  @Property(uid: 9003)
  String userId;

  @Property(uid: 9004)
  String? requestedFromUserId;

  @Property(uid: 9005)
  String? requestedToUserId;

  @Transient()
  @JsonKey(unknownEnumValue: FriendStatusEnum.UNSPECIFIED)
  final FriendStatusEnum? status;

  @Property(uid: 9006)
  int? statusRow;

  @Property(uid: 9007)
  String? createTime;

  @Property(uid: 9008)
  String? updateTime;
}
