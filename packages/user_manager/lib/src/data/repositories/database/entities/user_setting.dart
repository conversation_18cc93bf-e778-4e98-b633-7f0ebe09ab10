import 'package:json_annotation/json_annotation.dart';

import 'user_setting_security.dart';

part 'user_setting.g.dart';

@JsonSerializable(explicitToJson: true)
class UserSetting {
  UserSetting({
    this.security,
  });

  UserSettingSecurity? security;

  factory UserSetting.fromJson(Map<String, dynamic> json) =>
      _$UserSettingFromJson(json);

  Map<String, dynamic> toJson() => _$UserSettingToJson(this);
}
