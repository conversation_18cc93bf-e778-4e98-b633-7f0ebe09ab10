import 'package:json_annotation/json_annotation.dart';
import 'package:objectbox/objectbox.dart';

import '../../../interface/base_private_data.dart';
import 'private_data.dart';

part 'call_log_private_data.g.dart';

@Entity()
@JsonSerializable(explicitToJson: true)
class CallLogPrivateData implements BasePrivateData {
  CallLogPrivateData({
    required this.sessionKey,
    required this.callId,
    required this.callerId,
    required this.calleeId,
    this.callState,
    this.endedReason,
    this.callTimeInSeconds,
    this.isOutgoing,
    this.readTime,
    this.endedTime,
    this.source,
    this.version = 0,
    this.createTime,
  });

  @Id(assignable: true)
  @JsonKey(defaultValue: 0, includeFromJson: false, includeToJson: false)
  int id = 0;

  @Property(uid: 13001)
  @JsonKey(includeFromJson: true, includeToJson: false)
  String sessionKey;

  @Property(uid: 13002)
  @Json<PERSON>ey(name: 'id')
  String callId;

  @Property(uid: 13003)
  String callerId;

  @Property(uid: 13004)
  String calleeId;

  @Property(uid: 13005)
  int? callState;

  @Property(uid: 13006)
  int? endedReason;

  @Property(uid: 13007)
  int? callTimeInSeconds;

  @Property(uid: 13008)
  bool? isOutgoing;

  @Property(uid: 13009)
  String? readTime;

  @Property(uid: 13010)
  String? endedTime;

  @Property(uid: 13011)
  String? source;

  @Property(uid: 13012)
  int version;

  @Property(uid: 13013)
  String? createTime;

  final ToOne<PrivateData> privateData = ToOne<PrivateData>();

  factory CallLogPrivateData.fromJson(Map<String, dynamic> json) =>
      _$CallLogPrivateDataFromJson(json);

  Map<String, dynamic> toJson() => _$CallLogPrivateDataToJson(this);

  @override
  String get objectId => callId;

  @override
  int get objectVersion => version;
}
