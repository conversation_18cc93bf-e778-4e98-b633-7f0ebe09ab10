import 'package:json_annotation/json_annotation.dart';
import 'package:objectbox/objectbox.dart';

import '../../../interface/base_private_data.dart';
import 'private_data.dart';

part 'user_private_data.g.dart';

@Entity()
@JsonSerializable(explicitToJson: true)
class UserPrivateData implements BasePrivateData {
  UserPrivateData({
    required this.sessionKey,
    required this.userId,
    this.aliasName,
    this.version = 0,
    this.source,
    this.dmId,
    this.blocked,
  });

  @Id(assignable: true)
  @Json<PERSON>ey(defaultValue: 0, includeFromJson: false, includeToJson: false)
  int id = 0;

  @Property(uid: 11001)
  @JsonKey(includeFromJson: true, includeToJson: false)
  String sessionKey;

  @Property(uid: 11002)
  @JsonKey(name: 'id')
  String userId;

  @Property(uid: 11003)
  String? aliasName;

  @Property(uid: 11004)
  int version;

  @Property(uid: 11005)
  String? source;

  @Property(uid: 11006)
  String? dmId;

  @Property(uid: 11007)
  bool? blocked;

  final ToOne<PrivateData> privateData = ToOne<PrivateData>();

  factory UserPrivateData.fromJson(Map<String, dynamic> json) =>
      _$UserPrivateDataFromJson(json);

  Map<String, dynamic> toJson() => _$UserPrivateDataToJson(this);

  @override
  String get objectId => userId;

  @override
  int get objectVersion => version;
}
