import 'package:json_annotation/json_annotation.dart';

import '../enums/presence_state.dart';

part 'presence_data.g.dart';

@JsonSerializable(explicitToJson: true)
class PresenceData {
  PresenceData({
    this.presenceState,
    this.lastUpdateInSeconds,
    this.lastUpdateTime,
  });

  PresenceStateEnum? presenceState;
  int? lastUpdateInSeconds;
  String? lastUpdateTime;

  @JsonKey(includeFromJson: false, includeToJson: false)
  bool get isOnline => presenceState == PresenceStateEnum.ONLINE;

  factory PresenceData.fromJson(Map<String, dynamic> json) =>
      _$PresenceDataFromJson(json);

  Map<String, dynamic> toJson() => _$PresenceDataToJson(this);
}
