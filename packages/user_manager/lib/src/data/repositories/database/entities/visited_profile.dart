import 'package:json_annotation/json_annotation.dart';

import '../../../../../user_manager.dart';

part 'visited_profile.g.dart';

@Entity()
@JsonSerializable(explicitToJson: true)
class VisitedProfile {
  VisitedProfile({
    required this.sessionKey,
    required this.userId,
    this.isRead,
    this.createTime,
    this.updateTime,
  });

  factory VisitedProfile.fromJson(Map<String, dynamic> json) {
    final visitedProfile = _$VisitedProfileFromJson(json);
    return visitedProfile;
  }

  Map<String, dynamic> toJson() {
    final json = _$VisitedProfileToJson(this);
    return json;
  }

  @Id(assignable: true)
  @JsonKey(defaultValue: 0)
  int id = 0;

  @Index()
  @Property(uid: 10020)
  String sessionKey;

  @Property(uid: 10021)
  String userId;

  @Property(uid: 10022)
  bool? isRead;

  @Property(uid: 10023)
  String? createTime;

  @Property(uid: 10024)
  String? updateTime;
}
