import 'dart:async';

import 'database/entities/user.dart';
import 'database/entities/user_status.dart';

abstract class UserRepository {
  int insert(User user);

  Future<List<int>> insertAll(List<User> users);

  Stream<List<User>> getUsersStream();

  List<User> getUsers();

  List<User> getUsersMySessionKeys(List<String> sessionKeys);

  User? getUser(String userId);

  Stream<User?> getUserStream(String userId);

  User? getUserByUsername(String username);

  bool deleteUser(String userId);

  bool deleteSession(String sessionKey);

  int updateUserConnectLink(String userId, String link);

  int updateAvatar(String userId, String avatarPath);

  int updateCover(String userId, String coverPath);

  StreamSubscription observerUser({
    required void Function(User? user) listener,
  });

  int forceInsert(User user);

  List<int> forceInserts(List<User> user);

  void updateMyStatus(UserStatus? status);
}
