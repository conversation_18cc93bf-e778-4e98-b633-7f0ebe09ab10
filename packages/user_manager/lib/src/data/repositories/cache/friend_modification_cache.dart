import 'package:cached_annotation/cached_annotation.dart';

part 'friend_modification_cache.cached.dart';

@WithCache(useStaticCache: true)
abstract mixin class FriendModificationCache
    implements _$FriendModificationCache {
  factory FriendModificationCache() = _FriendModificationCache;

  @Cached(
    syncWrite: true,
    ttl: 180,
    limit: 100,
  )
  Future<String> setCache({
    required String sessionKey,
    required String friendId,
    @ignore required String updateTime,
  }) async {
    return Future.value(updateTime);
  }

  @CachePeek("setCache")
  String? peekCached({
    required String sessionKey,
    required String friendId,
  });

  @ClearCached("setCache")
  void cleanCache();
}
