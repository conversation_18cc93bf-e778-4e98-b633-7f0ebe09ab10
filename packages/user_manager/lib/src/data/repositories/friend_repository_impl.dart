import 'package:app_core/core.dart';
import 'package:injectable/injectable.dart';

import 'cache/friend_modification_cache.dart';
import 'database/entities/friend.dart';
import 'database/generated/objectbox.g.dart';
import 'database/user_database.dart';
import 'friend_repository.dart';

@LazySingleton(as: FriendRepository)
class FriendRepositoryImpl extends FriendRepository {
  FriendRepositoryImpl(this._store, this._cache);

  final UserStore _store;
  final FriendModificationCache _cache;

  Box<Friend> get _friendBox => _store.box<Friend>();

  @override
  int insert(Friend friend) {
    final friendsToUpdate = filterFriendsToUpdate([friend]);

    if (friendsToUpdate.isNotEmpty) {
      _existsAndUpdate(friendsToUpdate);
      updateCacheForFriend(friendsToUpdate.first);
      return _friendBox.put(friendsToUpdate.first);
    }
    return friend.id;
  }

  @override
  Future<List<int>> insertAll(List<Friend> friends) async {
    final friendsToUpdate = filterFriendsToUpdate(friends);

    if (friendsToUpdate.isNotEmpty) {
      _existsAndUpdate(friendsToUpdate);
      friendsToUpdate.forEach(updateCacheForFriend);
      return _friendBox.putMany(friendsToUpdate);
    }
    return friends.map((friend) => friend.id).toList();
  }

  List<Friend> filterFriendsToUpdate(List<Friend> friends) {
    return friends.where(shouldInsertFriend).toList();
  }

  bool shouldInsertFriend(Friend friend) {
    final cachedTime = _cache.peekCached(
      sessionKey: Config.getInstance().activeSessionKey ?? '',
      friendId: friend.friendId.toString(),
    );
    return cachedTime != friend.updateTime;
  }

  void updateCacheForFriend(Friend friend) {
    _cache.setCache(
      sessionKey: Config.getInstance().activeSessionKey ?? '',
      friendId: friend.friendId.toString(),
      updateTime: friend.updateTime!,
    );
  }

  @override
  Stream<List<Friend>> getFriendsStream() {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    return _friendBox
        .query(Friend_.sessionKey.equals(sessionKey))
        .watch(triggerImmediately: true)
        .map((query) => query.find());
  }

  @override
  List<Friend> getFriends() {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    return _friendBox
        .query(Friend_.sessionKey.equals(sessionKey))
        .build()
        .find();
  }

  @override
  Friend? getFriend(String friendId) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final query = _friendBox
        .query(
          Friend_.sessionKey.equals(sessionKey) &
              Friend_.friendId.equals(friendId),
        )
        .build();
    final session = query.findFirst();
    query.close();
    return session;
  }

  @override
  bool deleteFriend(String friendId) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final query = _friendBox
        .query(
          Friend_.sessionKey.equals(sessionKey) &
              Friend_.friendId.equals(friendId),
        )
        .build();
    final session = query.findFirst();
    if (session != null) {
      return _friendBox.remove(session.id);
    }
    return false;
  }

  void _existsAndUpdate(List<Friend> friends) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final existingQuery = _friendBox
        .query(
          Friend_.sessionKey.equals(sessionKey).and(
                Friend_.friendId
                    .oneOf(friends.map((f) => f.friendId).toSet().toList()),
              ),
        )
        .build();

    final existingFriends = existingQuery.find();
    existingQuery.close();

    for (final existingFriend in existingFriends) {
      final friend = friends.firstWhere(
        (f) => f.friendId == existingFriend.friendId,
        orElse: null,
      );
      friend.id = existingFriend.id;
    }
  }

  @override
  bool deleteSession(String sessionKey) {
    final query =
        _friendBox.query(Friend_.sessionKey.equals(sessionKey)).build();

    final removedCount = query.remove();

    query.close();
    return removedCount > 0;
  }
}
