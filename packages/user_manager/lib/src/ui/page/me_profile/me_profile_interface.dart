import '../../../data/repositories/database/entities/user.dart';

abstract class MeProfileInterface {
  Future<void> onLogoutSuccess();

  void onClickAppearance();

  void onClickLanguage();

  void onClickPrivacyAndSecurity(User me);

  void onClickEditProfile(User me);

  void onClickNotification();

  void onClickShareProfile(User me);

  void onClickTakePhoto(User me);

  void onClickTapOpenGallery(User me);

  void onClickTakeAvatarPhoto(User me);

  void onOpenGalleryAvatar(User me);

  void onClickListBlockedUser();

  void onTapFullScreenImage(String imagePath);

  void onAddStatus({
    void Function(
      String content,
      String emoji,
      int statusDuration,
    )? onAddStatusSuccessful,
  });

  void onUpdateStatus({
    String content = '',
    String emoji = '',
    int keepStatusDuration = 0,
    void Function(
      String content,
      String emoji,
      int statusDuration,
    )? onUpdateStatusSuccessful,
  });

  void onDeleteStatus({
    void Function()? onDeleteStatusSuccessful,
  });
}
