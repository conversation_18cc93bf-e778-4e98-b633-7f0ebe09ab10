import 'dart:async';
import 'dart:io';
import 'dart:typed_data';

import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../../user_manager.dart';
import '../../../common/di/di.dart';
import '../../../data/repositories/database/enums/user_badge_enum.dart';
import '../../bloc/edit_profile/edit_profile_bloc.dart';

class EditProfilePage extends StatefulWidget {
  const EditProfilePage({
    required this.user,
    super.key,
    required this.interface,
  });

  final User user;
  final EditProfileInterface interface;

  @override
  State<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState
    extends BasePageState<EditProfilePage, EditProfileBloc>
    implements ui.EditProfilePageInterface {
  User? _user;
  ValueNotifier<String> _name = ValueNotifier('');

  late final EditProfileBloc _editProfileBloc;
  StreamSubscription? _changeAvatarScubscription;

  int badgeEnum = 0;
  ui.UserBadgeType? userBadgeType;
  ValueNotifier<bool> _isLoadingNewAvatar = ValueNotifier(false);

  @override
  void initState() {
    bloc.add(InitiateEditProfileEvent());
    _editProfileBloc = getIt<EditProfileBloc>();

    _changeAvatarScubscription = GetIt.instance
        .get<AppEventBus>()
        .on<ChooseAvatarEvent>()
        .listen(_onUpdateLoadingNewAvatar);

    super.initState();
  }

  void _onUpdateLoadingNewAvatar(ChooseAvatarEvent event) {
    _isLoadingNewAvatar.value = true;
  }

  @override
  void dispose() {
    _changeAvatarScubscription?.cancel();
    _editProfileBloc.close();
    super.dispose();
  }

  Widget buildPage(BuildContext context) {
    return BlocBuilder<EditProfileBloc, EditProfileState>(
      builder: (BuildContext context, EditProfileState state) {
        return state.maybeWhen(
          initial: () {
            return Center(child: ui.AppCircularProgressIndicator());
          },
          loaded: (user) {
            badgeEnum = user.profile?.userBadgeType ?? 0;
            userBadgeType = UserBadgeEnumExtension.getEnumByValue(badgeEnum)
                .toUserBadgeType();
            _user = user;
            _name.value = _user?.profile?.displayName ?? '';
            _isLoadingNewAvatar.value = false;

            return ui.EditProfilePage(
              interface: this,
              badgeType: userBadgeType,
              name: _name,
            );
          },
          setDisplayName: (displayName) {
            _name.value = displayName;
            return ui.EditProfilePage(
              interface: this,
              badgeType: userBadgeType,
              name: _name,
            );
          },
          orElse: () {
            return ui.EditProfilePage(
              interface: this,
              badgeType: userBadgeType,
              name: _name,
            );
          },
        );
      },
    );
  }

  @override
  String username() {
    return _user?.username ?? '';
  }

  @override
  String avatarPath() {
    return UrlUtils.parseAvatar(_user?.profile?.avatar);
  }

  @override
  void onClickDeleteAccount() {
    widget.interface.onClickDeleteAccount();
  }

  @override
  void onClickSetAvatar() {
    ui.ActionSheetUtil.showSetAvatarDmChannelActionSheet(
      context,
      hasAvatar: false,
      onTapOpenGallery: _onTapOpenGalleryAvatar,
      onTapTakePhoto: _onTapTakePhotoAvatar,
      onTapTakeVideo: () {
        //TODO: onTapTakeVideo
      },
      onTapCancel: () {
        Navigator.of(context).pop();
      },
      onTapViewAvatar: _onTapViewAvatar,
      onTapRemove: _onTapRemoveAvatar,
      allowAddFrame: false,
      onTapAvatarFrame: () {},
    );
  }

  @override
  void onClickSetDisplayName() {
    ui.BottomSheetUtil.showSetDisplayNameBottomSheet(
      context: context,
      onPressedCancel: () {
        Navigator.of(context).pop();
      },
      onPressedDone: (name) async {
        bloc.add(EditProfileSetDisplayNameEvent(displayName: name));
        Navigator.of(context).pop();
      },
      displayName: displayName,
    );
  }

  @override
  void onClickSetVideoAvatar() {
    Log.d('onClickSetVideoAvatar');
  }

  String displayName() {
    return _name.value;
  }

  @override
  Uint8List? avatarData() {
    return null;
  }

  @override
  void onClickBack() {
    Navigator.of(context).pop();
  }

  void _showSetProfileAvatarActionSheet() {
    ui.ActionSheetUtil.showSetAvatarDmChannelActionSheet(
      context,
      hasAvatar: isHasAvatar(),
      onTapOpenGallery: _onTapOpenGalleryAvatar,
      onTapTakePhoto: _onTapTakePhotoAvatar,
      onTapTakeVideo: () {
        //TODO: onTapTakeVideo
      },
      onTapCancel: () {
        Navigator.of(context).pop();
      },
      onTapViewAvatar: _onTapViewAvatar,
      onTapRemove: _onTapRemoveAvatar,
      allowAddFrame: false,
      onTapAvatarFrame: () {},
    );
  }

  bool isHasAvatar() {
    return (_user?.profile?.avatar ?? '').isNotEmpty;
  }

  Future<void> _onTapOpenGalleryAvatar() async {
    final isGranted = Platform.isIOS
        ? await PermissionUtils.requestImagePermission(context)
        : true;
    if (isGranted) {
      Navigator.of(context).pop();
      widget.interface.onOpenGalleryAvatar(_user!);
    }
  }

  Future<void> _onTapTakePhotoAvatar() async {
    // Check if the user is in a meeting room
    if (getIt<MeetingHandler>().hasJoinedMeetingRoom(context: context)) return;

    final isGranted = await PermissionUtils.requestCameraPermission(context);
    if (isGranted) {
      Navigator.of(context).pop();
      widget.interface.onClickTakeAvatarPhoto(_user!);
    }
  }

  void _onTapViewAvatar() {
    if (!isHasAvatar()) return;
    Navigator.of(context).pop();
    widget.interface.onGoToViewImagePage(_user?.profile?.avatar);
  }

  void _onTapRemoveAvatar() {
    if (!isHasAvatar()) return;
    Navigator.of(context).pop();
    bloc.add(DeleteAvatarEditProfileEvent());
  }

  @override
  void onAvatarClicked() {
    _showSetProfileAvatarActionSheet();
  }

  @override
  ValueNotifier<bool> isLoadingNewAvatar() {
    return _isLoadingNewAvatar;
  }
}
