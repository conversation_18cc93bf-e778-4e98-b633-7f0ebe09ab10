import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:localization_client/localization_client.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../bloc/my_qr/my_qr_bloc.dart';

class MyQrBottomSheet extends StatefulWidget {
  const MyQrBottomSheet({super.key});

  @override
  State<MyQrBottomSheet> createState() => _MyQrBottomSheetState();
}

class _MyQrBottomSheetState extends BasePageState<MyQrBottomSheet, MyQrBloc> {
  GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  @override
  void initState() {
    bloc.add(InitiateMyQrEvent());
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocBuilder<MyQrBloc, MyQrState>(
      builder: (BuildContext context, state) {
        return state.maybeWhen(
          initial: () {
            return ui.AppCircularProgressIndicator();
          },
          loaded: (user) {
            return ui.MyQRBottomSheet(
              key: navigatorKey,
              parentContext: context,
              userName: user?.username ?? '',
              connectedLink: user?.userConnectLink ?? '',
              genNewQR: genNewQR,
              onShared: (status) {
                Log.d(status.status);
              },
              avatarImageUrl: UrlUtils.parseAvatar(user?.profile?.avatar),
              qrImage: ui.AppAssets.iconZiichatQR,
            );
          },
          orElse: () {
            return ui.AppCircularProgressIndicator();
          },
        );
      },
    );
  }

  void genNewQR() {
    ui.ActionSheetUtil.showGenerateNewQRActionSheet(
      context,
      onClickGenerate: () {
        Navigator.of(context).pop();
        bloc.add(
          GenerateUserConnectEvent(
            onSuccess: _onGenQRSuccess,
            onFail: _onGenQRFail,
          ),
        );
      },
      onClickCancel: () {
        Navigator.of(context).pop();
      },
    );
  }

  void _onGenQRSuccess() {
    SnackBarOverlayHelper().showSnackBar(
      widgetBuilder: (T) {
        return ui.SnackBarUtilV2.showQrCodeIsCreated(
          context,
          appLocalizations: AppLocalizations.of(context)!,
        );
      },
    );
  }

  void _onGenQRFail() {
    SnackBarOverlayHelper().showSnackBar(
      widgetBuilder: (T) {
        return ui.SnackBarUtilV2.showFailedToGenerateQrCode(
          context,
          appLocalizations: AppLocalizations.of(context)!,
        );
      },
    );
  }
}
