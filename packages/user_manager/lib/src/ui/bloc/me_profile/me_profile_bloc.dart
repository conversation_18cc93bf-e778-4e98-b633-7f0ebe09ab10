import 'dart:async';

import 'package:app_core/core.dart';
import 'package:bloc/bloc.dart';
import 'package:filestore_sdk/filestore_sdk.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:upload_manager/upload_manager.dart' hide Config;

import '../../../../user_manager.dart';
import '../../../common/di/di.dart';
import '../../../domain/usecase/add_cover_photo_use_case.dart';
import '../../../domain/usecase/delete_cover_photo_use_case.dart';
import '../../../domain/usecase/delete_user_avatar_use_case.dart';
import '../../../domain/usecase/set_display_name_use_case.dart';
import '../../../domain/usecase/update_cover_photo_use_case.dart';
import '../../page/me_profile/me_profile_handler.dart';

part 'me_profile_bloc.freezed.dart';
part 'me_profile_event.dart';
part 'me_profile_state.dart';

@injectable
class MeProfileBloc extends BaseBloc<MeProfileEvent, MeProfileState> {
  MeProfileBloc(
    this._loadMeUseCase,
    this._forceUpsertUserUseCase,
    this._userRepository,
    this._addCoverPhotoUseCase,
    this._updateCoverPhotoUseCase,
    this._deleteCoverPhotoUseCase,
    this._updateUserAvatarUseCase,
    this._deleteUserAvatarUseCase,
    this._setDisplayNameUseCase,
    this._uploadImageHandler,
    this._visitedProfileRepository,
    this._loadVisitedProfileUseCase,
    this._getListVisitedProfileUseCase,
    this._localDeleteVisitedProfileUseCase,
  ) : super(MeProfileState.initial()) {
    on<InitiateMeProfileEvent>(_onInit);
    on<AddCoverMeProfileEvent>(_onAddCover);
    on<ChangeCoverMeProfileEvent>(_onUpdateCover);
    on<DeleteCoverMeProfileEvent>(_onDeleteCover);
    on<OnCoverUpdatedEvent>(_onCoverChanged);
    on<ChangeAvatarMeProfileEvent>(_onUpdateAvatar);
    on<OnAvatarUpdatedEvent>(_onAvatarChanged);
    on<OnAvatarUpdatedErrorEvent>(_onAvatarChangedError);
    on<OnCoverUpdatedErrorEvent>(_onCoverChangedError);
    on<DeleteAvatarMeProfileEvent>(_onDeleteAvatar);
    on<SetDisplayNameEvent>(_onSetDisplayName);
    on<OnUserUpdatedEvent>(_onUserUpdateEvent);
    on<ListenVisitedProfileEvent>(_subscribeToVisitedProfile);
    on<HasVisitedProfileEvent>(_onHasVisitedProfile);
    on<CheckDifferentVisitedProfileEvent>(_checkDifferentVisitedProfile);
    on<RefreshEvent>(_onRefresh);
    on<_DisposeEvent>(_onDispose);
  }

  final LoadMeUseCase _loadMeUseCase;
  final ForceUpsertUserUseCase _forceUpsertUserUseCase;

  final UserRepository _userRepository;
  StreamSubscription? _userSubscription;

  final AddCoverPhotoUseCase _addCoverPhotoUseCase;
  final UpdateCoverPhotoUseCase _updateCoverPhotoUseCase;
  final DeleteCoverPhotoUseCase _deleteCoverPhotoUseCase;

  final UpdateUserAvatarUseCase _updateUserAvatarUseCase;
  final DeleteUserAvatarUseCase _deleteUserAvatarUseCase;
  final SetDisplayNameUseCase _setDisplayNameUseCase;

  final UploadImageHandler _uploadImageHandler;

  StreamSubscription? _visitedProfileSubscription;
  final VisitedProfileRepository _visitedProfileRepository;
  final LoadVisitedProfileUseCase _loadVisitedProfileUseCase;
  final GetListVisitedProfileUseCase _getListVisitedProfileUseCase;
  final LocalDeleteVisitedProfileUseCase _localDeleteVisitedProfileUseCase;

  Future<void> _onInit(
    InitiateMeProfileEvent event,
    Emitter<MeProfileState> emit,
  ) async {
    await _userSubscription?.cancel();

    await _subToMe(emit);

    await _MeFromApi();
  }

  FutureOr<void> _subToMe(Emitter<MeProfileState> emit) async {
    _userSubscription?.cancel();
    _userSubscription = _userRepository.observerUser(
      listener: (user) {
        if (isClosed) return;
        if (user != null) {
          add(OnUserUpdatedEvent(user));
          return;
        }
        if (user == null) return;
        emit(
          MeProfileState.meProfileShowError(
            errorMessage: "User not found or removed.",
          ),
        );
      },
    );
  }

  FutureOr<void> _MeFromApi() async {
    final meOutput = await _loadMeUseCase.execute(LoadMeInput());

    if (meOutput.user != null) {
      await _forceUpsertUserUseCase
          .execute(ForceUpsertUserInput(users: [meOutput.user!]));
      getIt<UserStatusHandler>().saveUserStatus(
        content: meOutput.user?.statusData?.content,
        emoji: meOutput.user?.statusData?.status,
        keepStatusDuration: meOutput.user?.statusData?.expireAfterTime?.value,
        createTime: meOutput.user?.statusData?.createTime,
        updateTime: meOutput.user?.statusData?.updateTime,
        endTime: meOutput.user?.statusData?.endTime,
      );
      add(OnUserUpdatedEvent(meOutput.user));
    }
  }

  Future<void> _onUpdateCover(
    ChangeCoverMeProfileEvent event,
    Emitter<MeProfileState> emit,
  ) async {
    await _handleUpload(
      event.cover,
      _updateCoverPhotoPath,
    );
  }

  Future<void> _onAddCover(
    AddCoverMeProfileEvent event,
    Emitter<MeProfileState> emit,
  ) async {
    await _handleUpload(
      event.cover,
      _addCoverPhotoPath,
    );
  }

  Future<void> _onUpdateAvatar(
    ChangeAvatarMeProfileEvent event,
    Emitter<MeProfileState> emit,
  ) async {
    await _handleUpload(
      event.avatar,
      _updateAvatarPath,
    );
  }

  Future<void> _handleUpload(
    UploadFile file,
    Future<void> Function(String fileUrl) onSuccessHandler,
  ) async {
    try {
      final result = await _uploadImageHandler.handleUpload(
        file: file,
        onSuccess: (UpFile file, String fileUrl) async {
          await onSuccessHandler(fileUrl);
        },
        onError: _error,
      );

      if (!result.success) {
        add(
          OnCoverUpdatedErrorEvent(
            errorMessage: " Upload failed: ${result.errorMessage}",
          ),
        );
      }
    } catch (e) {
      add(
        OnCoverUpdatedErrorEvent(
          errorMessage: " Exception while uploading file: $e",
        ),
      );
    }
  }

  Future<void> _updateAvatarPath(String avatarPath) async {
    final UpdateUserAvatarOutput output =
        await _updateUserAvatarUseCase.execute(
      UpdateUserAvatarInput(
        avatarPath: avatarPath,
      ),
    );

    if (output.success) {
      final avatarPath = output.avatarPath!;
      add(OnAvatarUpdatedEvent(avatarPath));
    } else {
      final error = output.error!;
      add(OnAvatarUpdatedErrorEvent(errorMessage: error.message!));
    }
  }

  Future<void> _addCoverPhotoPath(String coverPath) async {
    final output = await _addCoverPhotoUseCase.execute(
      AddCoverPhotoInput(
        coverPath: coverPath,
      ),
    );

    if (output.success) {
      final coverPath = output.coverPath!;
      add(OnCoverUpdatedEvent(coverPath));
    }
  }

  Future<void> _updateCoverPhotoPath(String coverPath) async {
    final output = await _updateCoverPhotoUseCase.execute(
      UpdateCoverPhotoInput(
        coverPath: coverPath,
      ),
    );

    if (output.success) {
      final coverPath = output.coverPath!;
      add(OnCoverUpdatedEvent(coverPath));
    }
  }

  void _error(UpFile file, ErrorCode errorCode, String message) {
    switch (errorCode) {
      case ErrorCode.noInternet:
        add(
          OnCoverUpdatedErrorEvent(
            errorMessage: "Update noInternet error! $message",
          ),
        );
        break;
      case ErrorCode.uploadError:
        add(OnCoverUpdatedErrorEvent(errorMessage: " Update error! $message"));
        break;
      default:
        add(OnCoverUpdatedErrorEvent(errorMessage: message));
    }
  }

  Future<void> _onDeleteCover(
    DeleteCoverMeProfileEvent event,
    Emitter<MeProfileState> emit,
  ) async {
    final output = await _deleteCoverPhotoUseCase.execute(
      DeleteCoverPhotoInput(),
    );

    if (output.success) {
      add(OnCoverUpdatedEvent(""));
    }
  }

  Future<void> _onCoverChanged(
    OnCoverUpdatedEvent event,
    Emitter<MeProfileState> emit,
  ) async {
    emit(MeProfileState.meProfileCoverChanged(coverPath: event.path));
    await Future.delayed(DurationUtils.ms300);
    _userRepository.updateCover(MeProfileHandler.sessionKey, event.path!);
  }

  Future<void> _onAvatarChanged(
    OnAvatarUpdatedEvent event,
    Emitter<MeProfileState> emit,
  ) async {
    emit(MeProfileState.meProfileAvatarChanged(avatarPath: event.path));
    await Future.delayed(DurationUtils.ms300);
    _userRepository.updateAvatar(MeProfileHandler.sessionKey, event.path!);
  }

  void _onUserUpdateEvent(
    OnUserUpdatedEvent event,
    Emitter<MeProfileState> emit,
  ) {
    emit(MeProfileState.loaded(me: event.me!));
  }

  void _onAvatarChangedError(
    OnAvatarUpdatedErrorEvent event,
    Emitter<MeProfileState> emit,
  ) {
    emit(MeProfileState.meProfileShowError(errorMessage: event.errorMessage));
  }

  void _onCoverChangedError(
    OnCoverUpdatedErrorEvent event,
    Emitter<MeProfileState> emit,
  ) {
    emit(MeProfileState.meProfileShowError(errorMessage: event.errorMessage));
  }

  Future<void> _onDeleteAvatar(
    DeleteAvatarMeProfileEvent event,
    Emitter<MeProfileState> emit,
  ) async {
    final output = await _deleteUserAvatarUseCase.execute(
      DeleteUserAvatarInput(),
    );

    if (output.success) {
      add(OnAvatarUpdatedEvent(""));
    }
  }

  Future<void> _onSetDisplayName(
    SetDisplayNameEvent event,
    Emitter<MeProfileState> emit,
  ) async {
    final output = await _setDisplayNameUseCase.execute(
      SetDisplayNameInput(displayName: event.displayName),
    );

    if (output.success) {
      emit(MeProfileState.setDisplayName(displayName: event.displayName));
    }
  }

  Future<void> _subscribeToVisitedProfile(
    ListenVisitedProfileEvent event,
    Emitter<MeProfileState> emit,
  ) async {
    await _visitedProfileSubscription?.cancel();
    _visitedProfileSubscription =
        _visitedProfileRepository.observerVisitedProfile(
      listener: (List<VisitedProfile>? listVisitedProfile) async {
        bool hasVisited = false;
        for (var item in listVisitedProfile!) {
          if (item.isRead == false) {
            hasVisited = true;
            break;
          }
        }
        add(HasVisitedProfileEvent(hasVisitedProfile: hasVisited));
      },
    );
  }

  Future<void> _onHasVisitedProfile(
    HasVisitedProfileEvent event,
    Emitter<MeProfileState> emit,
  ) async {
    emit(
      MeProfileState.hasVisitedProfileEvent(
        hasVisited: event.hasVisitedProfile,
      ),
    );
  }

  Future<void> _checkDifferentVisitedProfile(
    CheckDifferentVisitedProfileEvent event,
    Emitter<MeProfileState> emit,
  ) async {
    var getOutput = await _loadVisitedProfileUseCase.execute(
      LoadVisitedProfileUseCaseInput(),
    );
    var listVisitedProfileApi = getOutput.listVisitedProfile;
    var local = await _getListVisitedProfileUseCase
        .execute(GetListVisitedProfileUseCaseInput());
    var listVisitedProfileLocal = local.listVisitedProfile;
    if (listVisitedProfileApi!.length > local.listVisitedProfile!.length) {
      AppEventBus.publish(HasNotificationEvent(hasNotification: true));
      return emit(MeProfileState.hasVisitedProfileEvent(hasVisited: true));
    }
    if (local.listVisitedProfile!.length > listVisitedProfileApi.length) {
      List<VisitedProfile> difference =
          local.listVisitedProfile!.where((localVisited) {
        return !listVisitedProfileApi.contains(localVisited);
      }).toList();
      difference.forEach((item) async {
        await _localDeleteVisitedProfileUseCase
            .execute(LocalDeleteVisitedProfileUseCaseInput(item.userId));
      });
    }

    var listDifferent = listVisitedProfileApi.where(
      (api) => listVisitedProfileLocal!.any(
        (local) =>
            api.userId == local.userId &&
            (api.updateTime != local.updateTime || api.isRead != local.isRead),
      ),
    );
    if (listDifferent.length > 0) {
      AppEventBus.publish(HasNotificationEvent(hasNotification: true));
      return emit(MeProfileState.hasVisitedProfileEvent(hasVisited: true));
    }
    AppEventBus.publish(HasNotificationEvent(hasNotification: false));
    return emit(MeProfileState.hasVisitedProfileEvent(hasVisited: false));
  }

  Future<void> _onRefresh(
    RefreshEvent event,
    Emitter<MeProfileState> emit,
  ) async {
    emit(
      MeProfileState.refresh(),
    );
  }

  @override
  Future<void> close() {
    _userSubscription?.cancel();
    _visitedProfileSubscription?.cancel();
    return super.close();
  }

  FutureOr<void> _onDispose(
    _DisposeEvent event,
    Emitter<MeProfileState> emit,
  ) {
    if (_visitedProfileSubscription != null) {
      _visitedProfileSubscription?.cancel();
      _visitedProfileSubscription = null;
    }
  }
}
