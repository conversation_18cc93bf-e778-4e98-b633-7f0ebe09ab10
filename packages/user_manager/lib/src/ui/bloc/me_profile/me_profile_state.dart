part of 'me_profile_bloc.dart';

@freezed
sealed class MeProfileState extends BaseBlocState with _$MeProfileState {
  const MeProfileState._();

  factory MeProfileState.initial() = MeProfileStateInitial;

  factory MeProfileState.loaded({
    required User me,
  }) = MeProfileStateLoaded;

  factory MeProfileState.meProfileCoverChanged({
    @Default('') String? coverPath,
  }) = MeProfileStateMeProfileCoverChanged;

  factory MeProfileState.meProfileAvatarChanged({
    @Default('') String? avatarPath,
  }) = MeProfileStateMeProfileAvatarChanged;

  factory MeProfileState.setDisplayName({
    required String displayName,
  }) = MeProfileStateSetDisplayName;

  factory MeProfileState.meProfileShowError({
    @Default('') String? errorMessage,
  }) = MeProfileStateMeProfileCoverChangedError;

  factory MeProfileState.hasVisitedProfileEvent({
    @Default(false) bool? hasVisited,
  }) = MeProfileStateHasVisitedProfileEvent;

  factory MeProfileState.refresh() = MeProfileStateRefresh;
}

extension MeProfileStateX on MeProfileState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function(User me)? loaded,
    T Function(String? coverPath)? meProfileCoverChanged,
    T Function(String? avatarPath)? meProfileAvatarChanged,
    T Function(String displayName)? setDisplayName,
    T Function(String? errorMessage)? meProfileShowError,
    T Function(bool? hasVisited)? hasVisitedProfileEvent,
    T Function()? refresh,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is MeProfileStateInitial && initial != null) return initial();
    if (state is MeProfileStateLoaded && loaded != null) {
      return loaded(state.me);
    }
    if (state is MeProfileStateMeProfileCoverChanged &&
        meProfileCoverChanged != null) {
      return meProfileCoverChanged(state.coverPath);
    }
    if (state is MeProfileStateMeProfileAvatarChanged &&
        meProfileAvatarChanged != null) {
      return meProfileAvatarChanged(state.avatarPath);
    }
    if (state is MeProfileStateSetDisplayName && setDisplayName != null) {
      return setDisplayName(state.displayName);
    }
    if (state is MeProfileStateMeProfileCoverChangedError &&
        meProfileShowError != null) {
      return meProfileShowError(state.errorMessage);
    }
    if (state is MeProfileStateHasVisitedProfileEvent &&
        hasVisitedProfileEvent != null) {
      return hasVisitedProfileEvent(state.hasVisited);
    }
    if (state is MeProfileStateRefresh && refresh != null) {
      return refresh();
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function(User me) loaded,
    required T Function(String? coverPath) meProfileCoverChanged,
    required T Function(String? avatarPath) meProfileAvatarChanged,
    required T Function(String displayName) setDisplayName,
    required T Function(String? errorMessage) meProfileShowError,
    required T Function(bool? hasVisited) hasVisitedProfileEvent,
    required T Function() refresh,
  }) {
    final state = this;

    if (state is MeProfileStateInitial) return initial();
    if (state is MeProfileStateLoaded) return loaded(state.me);
    if (state is MeProfileStateMeProfileCoverChanged) {
      return meProfileCoverChanged(state.coverPath);
    }
    if (state is MeProfileStateMeProfileAvatarChanged) {
      return meProfileAvatarChanged(state.avatarPath);
    }
    if (state is MeProfileStateSetDisplayName) {
      return setDisplayName(state.displayName);
    }
    if (state is MeProfileStateMeProfileCoverChangedError) {
      return meProfileShowError(state.errorMessage);
    }
    if (state is MeProfileStateHasVisitedProfileEvent) {
      return hasVisitedProfileEvent(state.hasVisited);
    }
    if (state is MeProfileStateRefresh) return refresh();

    throw StateError('Unhandled state: $state');
  }
}
