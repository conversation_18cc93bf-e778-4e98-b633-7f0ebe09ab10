// load_user_cache_bloc.dart
import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../../user_manager.dart';
import '../../../../domain/usecase/load_user_from_cache_use_case.dart';

part 'load_user_cache_bloc.freezed.dart';

part 'load_user_cache_event.dart';

part 'load_user_cache_state.dart';

@LazySingleton()
class LoadUserCacheBloc extends Bloc<LoadUserCacheEvent, LoadUserCacheState> {
  LoadUserCacheBloc(
    this._loadUserFromCacheUseCase,
  ) : super(LoadUserCacheState.initial()) {
    on<LoadUserCacheRequested>(_onLoadUserCacheRequested);
  }

  final LoadUserFromCacheUseCase _loadUserFromCacheUseCase;

  FutureOr<void> _onLoadUserCacheRequested(
    LoadUserCacheRequested event,
    Emitter<LoadUserCacheState> emit,
  ) async {
    emit(LoadUserCacheState.loading(event.userId));

    try {
      final userOutput = await _loadUserFromCacheUseCase.execute(
        LoadUserFromCacheInput(userId: event.userId),
      );

      if (userOutput.user != null) {
        emit(LoadUserCacheState.loaded(event.userId, userOutput.user!));
      } else {
        emit(LoadUserCacheState.error(event.userId, "User not found"));
      }
    } catch (error) {
      emit(LoadUserCacheState.error(event.userId, error.toString()));
    }
  }
}
