// load_user_cache_state.dart
part of 'load_user_cache_bloc.dart';

@freezed
sealed class LoadUserCacheState extends BaseBlocState
    with _$LoadUserCacheState {
  const LoadUserCacheState._();
  factory LoadUserCacheState.initial() = Initial;

  factory LoadUserCacheState.loading(String userId) = Loading;

  factory LoadUserCacheState.loaded(String userId, User user) = Loaded;

  factory LoadUserCacheState.error(String userId, String message) = Error;
}
