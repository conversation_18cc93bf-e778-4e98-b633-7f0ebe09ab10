import 'dart:async';
import 'dart:convert';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_view_api/user_view_api.dart';

import '../../../data/repositories/database/entities/user.dart';
import '../../../data/repositories/source/api/client/clients.dart';
import '../../../data/repositories/user_repository.dart';
import '../../../domain/usecase/load_user_use_case.dart';

part 'initial_user_bloc.freezed.dart';
part 'initial_user_event.dart';
part 'initial_user_state.dart';

@LazySingleton()
class InitialUserBloc extends BaseBloc<InitialUserEvent, InitialUserState> {
  InitialUserBloc(this._loadUserUseCase, this._userRepository)
      : super(InitialUserState.initial()) {
    on<InitialUser>(_onLoadUser);
  }

  final LoadUserUseCase _loadUserUseCase;
  final UserRepository _userRepository;

  FutureOr<void> _onLoadUser(
    InitialUser event,
    Emitter<InitialUserState> emit,
  ) async {
    final userOutput = _loadUserUseCase.execute(
      LoadUserInput(userId: event.userId),
    );

    if (userOutput.user != null) {
      emit(InitialUserState.onLoaded(userOutput.user!));
      return;
    }

    emit(InitialUserState.onLoading(event.userId));

    final meResponse = await UserViewClient().instance.getMe();

    final serializedMe =
        standardSerializers.toJson(V3Me.serializer, meResponse.data?.data);

    final meJson = jsonDecode(serializedMe);
    meJson['sessionKey'] = event.token;

    final user = User.fromJson(meJson);

    emit(InitialUserState.onLoaded(user));

    _userRepository.insert(user);
    
    // Publish MeInfoUpdatedEvent after successfully inserting user data
    print('Publishing MeInfoUpdatedEvent');
    AppEventBus.publish(MeInfoUpdatedEvent());
  }
}
