part of 'initial_user_bloc.dart';

@freezed
sealed class InitialUserState extends BaseBlocState with _$InitialUserState {
  const InitialUserState._();

  const factory InitialUserState.initial() = InitialUserInitialState;

  const factory InitialUserState.onLoading(String userId) =
      InitialUserOnLoadingState;

  const factory InitialUserState.onLoaded(User user) = InitialUserOnLoadedState;

  const factory InitialUserState.onError(String userId, String error) =
      InitialUserOnErrorState;
}
