import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../user_manager.dart';

@Injectable()
class UpsertUserUseCase
    extends BaseFutureUseCase<UpsertUserInput, UpsertUserOutput> {
  const UpsertUserUseCase(this._userRepository);

  final UserRepository _userRepository;

  @protected
  @override
  Future<UpsertUserOutput> buildUseCase(UpsertUserInput input) async {
    final users = input.users
        .map(
          (user) =>
              user..sessionKey = Config.getInstance().activeSessionKey ?? '',
        )
        .toList();

    List<int> ids = await _userRepository.insertAll(users);

    return UpsertUserOutput(total: ids.length);
  }
}

class UpsertUserInput extends BaseInput {
  final List<User> users;

  UpsertUserInput({required this.users});
}

class UpsertUserOutput extends BaseOutput {
  final int total;

  UpsertUserOutput({required this.total});
}
