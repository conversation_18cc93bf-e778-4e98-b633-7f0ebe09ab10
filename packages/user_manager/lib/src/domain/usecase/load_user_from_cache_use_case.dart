import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../data/repositories/cache/load_user_cache.dart';
import '../../data/repositories/database/entities/user.dart';
import '../../data/repositories/user_repository.dart';
import 'get_user_use_case.dart';

part 'load_user_from_cache_use_case.freezed.dart';

@Injectable()
class LoadUserFromCacheUseCase
    extends BaseFutureUseCase<LoadUserFromCacheInput, LoadUserFromCacheOutput> {
  const LoadUserFromCacheUseCase(
    this._repository,
    this._userCache,
    this._getUserUseCase,
  );

  final UserRepository _repository;
  final LoadUserCache _userCache;
  final GetUserUseCase _getUserUseCase;

  @protected
  @override
  Future<LoadUserFromCacheOutput> buildUseCase(
    LoadUserFromCacheInput input,
  ) async {
    final cachedUser = _userCache.peekCached(
      sessionKey: input.sessionKey,
      userId: input.userId,
    );

    if (cachedUser != null) {
      return LoadUserFromCacheOutput(user: cachedUser);
    }

    GetUserOutput output =
        await _getUserUseCase.execute(GetUserInput(userId: input.userId));

    if (output.user != null) {
      _userCache.setCache(
        sessionKey: input.sessionKey,
        userId: input.userId,
        user: output.user!,
      );

      _repository.insert(output.user!);
    }

    return LoadUserFromCacheOutput(user: output.user);
  }
}

@freezed
sealed class LoadUserFromCacheInput extends BaseInput
    with _$LoadUserFromCacheInput {
  factory LoadUserFromCacheInput({
    @Default('') String sessionKey,
    @Default('') String userId,
  }) = _LoadUserFromCacheInput;

  const LoadUserFromCacheInput._();
}

@freezed
sealed class LoadUserFromCacheOutput extends BaseOutput
    with _$LoadUserFromCacheOutput {
  factory LoadUserFromCacheOutput({
    @Default(null) User? user,
  }) = _LoadUserFromCacheOutput;

  const LoadUserFromCacheOutput._();
}
