import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_profile_api/user_profile_api.dart' as user_profile_api;

import '../../../user_manager.dart';

part 'add_cover_photo_use_case.freezed.dart';

@Injectable()
class AddCoverPhotoUseCase
    extends BaseFutureUseCase<AddCoverPhotoInput, AddCoverPhotoOutput> {
  AddCoverPhotoUseCase();

  @override
  Future<AddCoverPhotoOutput> buildUseCase(AddCoverPhotoInput input) async {
    final body = user_profile_api.V3AddCoverPhotoRequestBuilder()
      ..coverPath = input.coverPath!;

    final response = await UserProfileClient().instance.addCoverPhoto(
          body: body.build(),
        );

    if (response.data?.ok ?? false) {
      final coverData = response.data!.data!;

      return AddCoverPhotoOutput(
        success: true,
        coverPath: coverData.cover,
      );
    }
    return AddCoverPhotoOutput(
      success: false,
      error: response.data?.error as user_profile_api.V3Error,
    );
  }
}

@freezed
sealed class AddCoverPhotoInput extends BaseInput with _$AddCoverPhotoInput {
  const AddCoverPhotoInput._();
  factory AddCoverPhotoInput({
    required String? coverPath,
  }) = _AddCoverPhotoInput;
}

@freezed
sealed class AddCoverPhotoOutput extends BaseOutput with _$AddCoverPhotoOutput {
  const AddCoverPhotoOutput._();
  factory AddCoverPhotoOutput({
    required bool success,
    String? coverPath,
    final user_profile_api.V3Error? error,
  }) = _AddCoverPhotoOutput;
}
