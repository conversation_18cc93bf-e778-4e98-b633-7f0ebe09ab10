import 'package:app_core/core.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../data/repositories/database/entities/user.dart';
import '../../data/repositories/user_repository.dart';

@Injectable()
class GetMeStreamUseCase
    extends BaseSyncUseCase<GetMeStreamInput, GetMeStreamOutput> {
  const GetMeStreamUseCase(this._repository);

  final UserRepository _repository;

  @protected
  @override
  GetMeStreamOutput buildUseCase(GetMeStreamInput input) {
    final sessionKey = Config.getInstance().activeSessionKey ?? '';
    final userStream = _repository.getUserStream(sessionKey);

    return GetMeStreamOutput(userStream: userStream);
  }
}

class GetMeStreamInput extends BaseInput {
  const GetMeStreamInput();
}

class GetMeStreamOutput extends BaseOutput {
  const GetMeStreamOutput({required this.userStream});

  final Stream<User?> userStream;
}
