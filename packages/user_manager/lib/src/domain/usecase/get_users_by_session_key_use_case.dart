import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../user_manager.dart';

@Injectable()
class GetUserBySessionKeyUseCase extends BaseSyncUseCase<
    GetUserBySessionKeyInput, GetUserBySessionKeyOutput> {
  const GetUserBySessionKeyUseCase(this._userRepository);

  final UserRepository _userRepository;

  @protected
  @override
  GetUserBySessionKeyOutput buildUseCase(
    GetUserBySessionKeyInput input,
  ) {
    List<User> users = _userRepository.getUsersMySessionKeys(input.sessionKeys);

    return GetUserBySessionKeyOutput(users: users);
  }
}

class GetUserBySessionKeyInput extends BaseInput {
  final List<String> sessionKeys;

  GetUserBySessionKeyInput({required this.sessionKeys});
}

class GetUserBySessionKeyOutput extends BaseOutput {
  final List<User> users;

  GetUserBySessionKeyOutput({required this.users});
}
