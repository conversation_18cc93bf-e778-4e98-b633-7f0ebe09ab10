name: user_manager
description: Include core structure elements
version: 0.1.0+1
publish_to: none
environment:
  sdk: ^3.4.0
  flutter: ^3.22.0
dependencies:
  bloc: ^9.0.0
  cached_annotation:
    git:
      url: **************:ziichatlabs/cached.git
      ref: v0.1.0
      path: packages/cached_annotation
  equatable: ^2.0.7
  flutter:
    sdk: flutter
  flutter_bloc: ^9.1.0
  get_it: ^8.0.3
  injectable: ^2.5.0
  app_core:
    path: ../app_core
  shared:
    path: ../shared
  upload_manager:
    path: ../upload_manager
  objectbox:
    git:
      url: **************:ziichatlabs/objectbox-dart.git
      path: objectbox
      ref: v4.1.0
  objectbox_flutter_libs:
    git:
      url: **************:ziichatlabs/objectbox-dart.git
      path: flutter_libs
      ref: v4.1.0
  path_provider: ^2.1.5
  freezed_annotation: ^3.0.0
  json_annotation: ^4.9.0
  localization_client:
    git:
      url: **************:ziichatlabs/ziichat-flutter-i18n.git
      ref: main
  user_profile_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/user_profile_api
  user_view_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.29
      path: apis/user_view_api
  user_connect_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/user_connect_api
  friend_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/friend_api
  ziichat_ui:
    git:
      url: **************:ziichatlabs/ziichat-flutter-ui.git
      ref: release/0.56.0
  diffutil_dart: ^4.0.1
  filestore_sdk:
    git:
      url: **************:ziichatlabs/filestore-sdk.git
      ref: v0.1.0
  download_manager:
    git:
      url: **************:ziichatlabs/ziichat-flutter-sdks.git
      ref: v0.55.0
      path: packages/download_manager
dev_dependencies:
  build_runner: ^2.4.15
  cached:
    git:
      url: **************:ziichatlabs/cached.git
      ref: v0.1.0
      path: packages/cached
  flutter_test:
    sdk: flutter
  freezed: ^3.0.6
  injectable_generator: ^2.7.0
  mocktail: ^1.0.4
  very_good_analysis: ^7.0.0
  objectbox_generator:
    git:
      url: **************:ziichatlabs/objectbox-dart.git
      path: generator
      ref: v4.1.0
  json_serializable: ^6.9.1
objectbox:
  output_dir: src/data/repositories/database/generated
dependency_overrides:
  analyzer: 7.3.0
