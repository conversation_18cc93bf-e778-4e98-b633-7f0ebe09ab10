name: search
description: ZiiChat search package
version: 0.1.0+1
publish_to: none
environment:
  sdk: ^3.5.0
  flutter: ^3.24.0
dependencies:
  bloc: ^9.0.0
  equatable: ^2.0.7
  flutter:
    sdk: flutter
  flutter_bloc: ^9.1.0
  get_it: ^8.0.3
  injectable: ^2.5.0
  app_core:
    path: ../app_core
  shared:
    path: ../shared
  search_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.25
      path: apis/search_api
  suggestion_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/suggestion_api
  ziichat_ui:
    git:
      url: **************:ziichatlabs/ziichat-flutter-ui.git
      ref: release/0.56.0
  localization_client:
    git:
      url: **************:ziichatlabs/ziichat-flutter-i18n.git
      ref: main
  objectbox:
    git:
      url: **************:ziichatlabs/objectbox-dart.git
      path: objectbox
      ref: v4.1.0
  objectbox_flutter_libs:
    git:
      url: **************:ziichatlabs/objectbox-dart.git
      path: flutter_libs
      ref: v4.1.0
  path_provider: ^2.1.5
  freezed_annotation: ^3.0.0
  json_annotation: ^4.9.0
  infinite_scroll_pagination: ^4.0.0
  flutter_screenutil: ^5.9.3
dev_dependencies:
  build_runner: ^2.4.15
  flutter_test:
    sdk: flutter
  freezed: ^3.0.6
  injectable_generator: ^2.7.0
  mocktail: ^1.0.4
  very_good_analysis: ^7.0.0
  objectbox_generator:
    git:
      url: **************:ziichatlabs/objectbox-dart.git
      path: generator
      ref: v4.1.0
  json_serializable: ^6.9.1
objectbox:
  output_dir: src/data/repositories/database/generated
dependency_overrides:
  analyzer: 7.3.0
