part of 'search_bloc.dart';

class SearchState extends BaseBlocState {
  final bool isSearching;
  final bool isFirstLoadingSuggestedFriends;
  final SearchResults? sameChannelUsers;
  final SearchResults? usersYouMayKnow;
  final List<Search> recentSearches;
  final SearchResults? allSearchResults;
  final SearchResults? userSearchResults;
  final SearchResults? channelSearchResults;
  final bool isNewResults;
  final bool isNewSameChannelUsers;
  final bool isNewUsersYouMayKnow;
  final int currentSearchSegmentIndex;
  final List<History> history;

  const SearchState({
    this.isSearching = false,
    this.isFirstLoadingSuggestedFriends = true,
    this.sameChannelUsers,
    this.usersYouMayKnow,
    this.recentSearches = const [],
    this.allSearchResults,
    this.userSearchResults,
    this.channelSearchResults,
    this.isNewResults = false,
    this.isNewSameChannelUsers = false,
    this.isNewUsersYouMayKnow = false,
    this.currentSearchSegmentIndex = 0,
    this.history = const [],
  });

  SearchState copyWith({
    bool? isSearching,
    bool? isFirstLoadingSuggestedFriends,
    SearchResults? sameChannelUsers,
    SearchResults? usersYouMayKnow,
    List<Search>? recentSearches,
    SearchResults? allSearchResults,
    SearchResults? userSearchResults,
    SearchResults? channelSearchResults,
    bool? isNewResults,
    bool? isNewSameChannelUsers,
    bool? isNewUsersYouMayKnow,
    int? currentSearchSegmentIndex,
    List<History>? history,
  }) {
    return SearchState(
      isSearching: isSearching ?? this.isSearching,
      isFirstLoadingSuggestedFriends:
          isFirstLoadingSuggestedFriends ?? this.isFirstLoadingSuggestedFriends,
      sameChannelUsers: sameChannelUsers ?? this.sameChannelUsers,
      usersYouMayKnow: usersYouMayKnow ?? this.usersYouMayKnow,
      recentSearches: recentSearches ?? this.recentSearches,
      allSearchResults: allSearchResults ?? this.allSearchResults,
      userSearchResults: userSearchResults ?? this.userSearchResults,
      channelSearchResults: channelSearchResults ?? this.channelSearchResults,
      isNewResults: isNewResults ?? this.isNewResults,
      isNewSameChannelUsers:
          isNewSameChannelUsers ?? this.isNewSameChannelUsers,
      isNewUsersYouMayKnow: isNewUsersYouMayKnow ?? this.isNewUsersYouMayKnow,
      currentSearchSegmentIndex:
          currentSearchSegmentIndex ?? this.currentSearchSegmentIndex,
      history: history ?? this.history,
    );
  }
}

class SearchResults {
  final List<Search> results;
  final String? nextPageToken;
  final bool hasNext;

  const SearchResults({
    this.results = const [],
    this.nextPageToken,
    this.hasNext = false,
  });

  SearchResults copyWith({
    List<Search>? results,
    String? nextPageToken,
    bool? hasNext,
  }) {
    return SearchResults(
      results: results ?? this.results,
      nextPageToken: nextPageToken ?? this.nextPageToken,
      hasNext: hasNext ?? this.hasNext,
    );
  }
}
