import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../common/di/di.dart';
import '../../data/repositories/database/entities/search.dart';
import '../bloc/search_bloc.dart';
import 'partials/search_ready_partial.dart';
import 'search_page_interface.dart';
import 'widgets/all_list_view.dart';
import 'widgets/channels_list_view.dart';
import 'widgets/users_list_view.dart';
import 'package:auto_route/auto_route.dart';

class SearchPage extends StatefulWidget {
  const SearchPage({
    required this.interface,
    super.key,
  });

  final SearchPageInterface interface;

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage>
    with AutoRouteAwareStateMixin<SearchPage>
    implements ui.SearchPageInterface {
  ui.SearchSegmentEnum _searchSegment = ui.SearchSegmentEnum.all;
  String _keyword = '';

  GlobalKey<UsersListViewState> _usersListKey = GlobalKey();
  GlobalKey<ChannelsListViewState> _channelsListKey = GlobalKey();
  late final UserPrivateDataBloc _userPrivateDataBloc;
  late final SearchBloc _searchBloc;
  List<UserPrivateData> _listUserPrivateData = [];

  @override
  void initState() {
    super.initState();
    _userPrivateDataBloc = getIt<UserPrivateDataBloc>();
    _userPrivateDataBloc.add(InitUserPrivateDataEvent());
    _searchBloc = getIt<SearchBloc>();
    _searchBloc.add(InitiateSearchEvent());
    _searchBloc.add(OnInitUserPrivateDataEvent());
  }

  @override
  void dispose() {
    super.dispose();
    getIt<UserPrivateDataBloc>().add(GetPrivateDataUnSubscriptionEvent());
  }

  @override
  void didPopNext() {
    super.didPopNext();
    _searchBloc.add(OnInitUserPrivateDataEvent());
  }

  void _blocUserPrivateListener(
    BuildContext context,
    UserPrivateDataState state,
  ) {
    state.when(
      initial: () {},
      listUserPrivateData: (List<UserPrivateData> listUserPrivateData) {
        _listUserPrivateData = listUserPrivateData;
      },
    );
  }

  String? getAliasName(String? userId) {
    if (userId == null) return null;
    try {
      UserPrivateData userPrivateData =
          _listUserPrivateData.firstWhere((user) => user.userId == userId);
      return userPrivateData.aliasName != null &&
              userPrivateData.aliasName!.isNotEmpty
          ? userPrivateData.aliasName
          : null;
    } catch (error) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<SearchBloc>.value(value: _searchBloc),
        BlocProvider<UserPrivateDataBloc>.value(value: _userPrivateDataBloc),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<UserPrivateDataBloc, UserPrivateDataState>(
            listenWhen: (prev, state) => prev != state,
            listener: _blocUserPrivateListener,
          ),
        ],
        child: BlocBuilder<SearchBloc, SearchState>(
          builder: (context, state) {
            return GestureDetector(
              onTap: FocusScope.of(context).unfocus,
              child: ui.SearchPage(
                interface: this,
                buildReadyPartial: _buildReadyPartial,
              ),
            );
          },
        ),
      ),
    );
  }

  @override
  Widget buildAllSegment(BuildContext context) {
    final state = _searchBloc.state;

    Widget widget;
    if ((state.isSearching && state.currentSearchSegmentIndex == 0) ||
        state.currentSearchSegmentIndex != 0) {
      widget = ui.SearchSkeletonPartial();
    } else {
      final listUsers =
          state.allSearchResults?.results.where((e) => e.isUser).toList();
      final listChannels =
          state.allSearchResults?.results.where((e) => !e.isUser).toList();
      if ((listUsers?.isEmpty ?? true) && (listChannels?.isEmpty ?? true)) {
        widget = Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).padding.bottom,
          ),
          child: ui.SearchNoResultsFoundWidget(),
        );
      } else {
        widget = AllListView(
          listUsers: listUsers!,
          listChannels: listChannels!,
          listUserPrivateData: _listUserPrivateData,
          onTapSearchItem: _onTapSearchResult,
        );
      }
    }
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 250),
      transitionBuilder: (Widget child, Animation<double> animation) {
        return FadeTransition(opacity: animation, child: child);
      },
      child: widget,
    );
  }

  @override
  Widget buildUserSegment(BuildContext context) {
    return UsersListView(
      key: _usersListKey,
      keyword: _keyword,
      listUserPrivateData: _listUserPrivateData,
      onTapSearchItem: _onTapSearchResult,
    );
  }

  @override
  Widget buildChannelSegment(BuildContext context) {
    return ChannelsListView(
      key: _channelsListKey,
      keyword: _keyword,
      onTapSearchItem: _onTapSearchResult,
    );
  }

  @override
  void onSearchStateChanged(ui.SearchStateEnum status) {}

  @override
  void onSearchTextChanged(String value) {
    final valueTrimmed = value.trim();
    if (valueTrimmed != _keyword.trim()) {
      switch (_searchSegment) {
        case ui.SearchSegmentEnum.all:
          _searchBloc.add(SearchAllEvent(keyword: valueTrimmed));
          break;
        case ui.SearchSegmentEnum.user:
          _usersListKey.currentState?.onRefresh(valueTrimmed);
          break;
        case ui.SearchSegmentEnum.channel:
          _channelsListKey.currentState?.onRefresh(valueTrimmed);
          break;
      }
    }
    _keyword = value;
  }

  @override
  void onSegmentChanged(ui.SearchSegmentEnum segment) {
    FocusScope.of(context).requestFocus(FocusNode());
    _searchSegment = segment;
    switch (segment) {
      case ui.SearchSegmentEnum.all:
        _searchBloc.add(ChangeCurrentRecentSegmentEvent(index: 0));
        _searchBloc.add(SearchAllEvent(keyword: _keyword.trim()));
        break;
      case ui.SearchSegmentEnum.user:
        _searchBloc.add(ChangeCurrentRecentSegmentEvent(index: 1));
        _searchBloc.add(
          SearchUserEvent(
            keyword: _keyword.trim(),
            nextPageToken: '1',
          ),
        );
        _usersListKey.currentState
            ?.onRefresh(_keyword.trim(), needLoadData: false);
        break;
      case ui.SearchSegmentEnum.channel:
        _searchBloc.add(ChangeCurrentRecentSegmentEvent(index: 2));
        _searchBloc.add(
          SearchChannelEvent(
            keyword: _keyword.trim(),
            nextPageToken: '1',
          ),
        );
        _channelsListKey.currentState
            ?.onRefresh(_keyword.trim(), needLoadData: false);
        break;
    }
  }

  @override
  List<ui.UserSearchModel> usersYouMayKnow() {
    return [];
  }

  @override
  List<ui.UserSearchModel> sameChannelUsers() {
    return [];
  }

  @override
  List<ui.SearchModel> recentSearch() {
    return _searchBloc.state.recentSearches.map((item) {
      if (item.isUser) {
        return ui.UserSearchModel(
          userId: item.userId!,
          userName: item.username,
          name: getAliasName(item.userId!) ?? item.userDisplayName,
          avatarUrl: UrlUtils.parseAvatar(item.avatar),
        );
      } else {
        return ui.ChannelSearchModel(
          channelId: item.channelId!,
          workspaceId: item.workspaceId!,
          name: item.searchResultName,
          avatarUrl: UrlUtils.parseAvatar(item.avatar),
        );
      }
    }).toList();
  }

  @override
  List<String> searchHistories() {
    return _searchBloc.state.history.map((e) => e.keyword).toList();
  }

  @override
  String searchText() {
    return _keyword;
  }

  @override
  void onRemoveHistory(String keyword) {
    _searchBloc.add(RemoveHistoryEvent(keyword: keyword));
  }

  @override
  void onTapHistory(String keyword) {
    onSearchTextChanged(keyword);
    setState(() {
      _keyword = keyword;
    });
  }

  void _onTapSearchResult(Search item) {
    FocusScope.of(context).requestFocus(FocusNode());
    LoadingOverlayHelper.showLoading(context);
    _searchBloc.add(SaveRecentSearchEvent(model: item));
    if (item.isUser) {
      _searchBloc.add(
        CheckUserEvent(
          context: context,
          userId: item.userId!,
          onUserExists: () {
            LoadingOverlayHelper.hideLoading(context);
            widget.interface.onTapUser(
              item.userId!,
              UrlUtils.parseAvatar(item.avatar),
              item.searchResultName,
            );
          },
        ),
      );
    } else {
      _searchBloc.add(
        CheckChannelEvent(
          context: context,
          channelId: item.channelId!,
          workspaceId: item.workspaceId!,
          onChannelExists: () {
            LoadingOverlayHelper.hideLoading(context);
            widget.interface
                .onTapChannel(item.channelId!, item.workspaceId!, item.avatar);
          },
        ),
      );
    }
  }

  @override
  void onTapSearchItem(ui.SearchModel model) {
    FocusScope.of(context).requestFocus(FocusNode());
    LoadingOverlayHelper.showLoading(context);
    if (model is ui.UserSearchModel) {
      _searchBloc.add(
        CheckUserEvent(
          context: context,
          userId: model.userId,
          onUserExists: () {
            LoadingOverlayHelper.hideLoading(context);
            widget.interface.onTapUser(
              model.userId,
              model.avatarUrl,
              model.name,
            );
          },
        ),
      );
    }
    if (model is ui.ChannelSearchModel) {
      _searchBloc.add(
        CheckChannelEvent(
          context: context,
          channelId: model.channelId,
          workspaceId: model.workspaceId,
          onChannelExists: () {
            LoadingOverlayHelper.hideLoading(context);
            widget.interface.onTapChannel(
              model.channelId,
              model.workspaceId,
              model.avatarUrl,
            );
          },
        ),
      );
    }
  }

  @override
  void onSearchTextSubmitted(String value) {
    _searchBloc.add(SaveHistoryEvent(keyword: value.trim()));
  }

  Widget _buildReadyPartial(BuildContext context) {
    return SearchReadyPartial(onTapSearchItem: _onTapSearchResult);
  }

  @override
  void onScanQrIconClicked() {
    widget.interface.onTapScanQR();
  }
}
