import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../data/repositories/database/entities/search.dart';
import '../../bloc/search_bloc.dart';

class UsersListView extends StatefulWidget {
  const UsersListView({
    required this.keyword,
    required this.onTapSearchItem,
    required this.listUserPrivateData,
    super.key,
  });

  final String keyword;
  final void Function(Search item) onTapSearchItem;
  final List<UserPrivateData> listUserPrivateData;

  @override
  State<UsersListView> createState() => UsersListViewState();
}

const _firstPageKey = 0;

class UsersListViewState extends State<UsersListView> {
  PagingController<int, Search> _pagingController =
      PagingController(firstPageKey: _firstPageKey);
  String _nextPageToken = '1';
  String _lastNextPageToken = '';
  int _pageKey = _firstPageKey;
  String _keyword = '';

  @override
  void initState() {
    super.initState();
    _keyword = widget.keyword;
    _getData();
    _pagingController.addPageRequestListener(
      (pageKey) {
        if (pageKey != _firstPageKey) {
          _getData();
        }
      },
    );
  }

  Future<void> onRefresh(String keyword, {bool needLoadData = true}) async {
    _keyword = keyword;
    _nextPageToken = '1';
    _lastNextPageToken = '';
    _pageKey = _firstPageKey;
    _pagingController.refresh();
    if (needLoadData) {
      _getData();
    }
  }

  void _getData() {
    context.read<SearchBloc>().add(
          SearchUserEvent(
            keyword: _keyword,
            nextPageToken: '$_nextPageToken',
          ),
        );
  }

  String? getAliasName(String? userId) {
    if (userId == null) return null;
    try {
      UserPrivateData userPrivateData = widget.listUserPrivateData
          .firstWhere((user) => user.userId == userId);
      return userPrivateData.aliasName != null &&
              userPrivateData.aliasName!.isNotEmpty
          ? userPrivateData.aliasName
          : null;
    } catch (error) {
      return null;
    }
  }

  @override
  void dispose() {
    _pagingController.dispose();
    super.dispose();
  }

  void _blocChannelListener(BuildContext context, SearchState state) {
    if (state.currentSearchSegmentIndex == 1 &&
        !state.isSearching &&
        state.isNewResults) {
      final searchResults = state.userSearchResults?.results ?? [];
      final hasNext = state.userSearchResults?.hasNext ?? false;
      _nextPageToken = state.userSearchResults?.nextPageToken ?? '0';
      if (_lastNextPageToken != _nextPageToken) {
        _lastNextPageToken = _nextPageToken;
        _pageKey += searchResults.length;
        if (hasNext) {
          _pagingController.appendPage(searchResults, _pageKey);
        } else {
          _pagingController.appendLastPage(searchResults);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final paddingBottom = MediaQuery.of(context).padding.bottom;
    final state = context.watch<SearchBloc>().state;
    Widget child;
    if ((state.isSearching &&
            state.currentSearchSegmentIndex == 1 &&
            _pageKey == _firstPageKey) ||
        state.currentSearchSegmentIndex != 1) {
      child = ui.SearchSkeletonPartial();
    } else {
      if ((state.userSearchResults?.results.isEmpty ?? true) &&
          (_pageKey == _firstPageKey)) {
        child = Padding(
          padding: EdgeInsets.only(bottom: paddingBottom),
          child: ui.SearchNoResultsFoundWidget(),
        );
      } else {
        if (_pageKey == _firstPageKey) {
          final searchResults = state.userSearchResults?.results ?? [];
          final hasNext = state.userSearchResults?.hasNext ?? false;
          _nextPageToken = state.userSearchResults?.nextPageToken ?? '0';
          if (_lastNextPageToken != _nextPageToken) {
            _lastNextPageToken = _nextPageToken;
            _pageKey += searchResults.length;
            if (hasNext) {
              _pagingController.appendPage(searchResults, _pageKey);
            } else {
              _pagingController.appendLastPage(searchResults);
            }
          }
        }
        child = BlocListener<SearchBloc, SearchState>(
          listener: _blocChannelListener,
          child: PagedListView<int, Search>(
            keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
            pagingController: _pagingController,
            padding: EdgeInsets.only(
              bottom: paddingBottom,
            ),
            builderDelegate: PagedChildBuilderDelegate<Search>(
              itemBuilder: (context, item, index) {
                final model = ui.UserSearchModel(
                  userId: item.userId!,
                  userName: item.username,
                  name: getAliasName(item.userId!) ?? item.searchResultName,
                  avatarUrl: UrlUtils.parseAvatar(item.avatar),
                );
                return ui.SearchResultWidget(
                  model: model,
                  onTap: () {
                    widget.onTapSearchItem(item);
                  },
                  isShowStatus: false,
                );
              },
              noItemsFoundIndicatorBuilder: (_) {
                return Container();
              },
              newPageProgressIndicatorBuilder: (_) {
                return Center(child: ui.AppCircularProgressIndicator());
              },
              firstPageProgressIndicatorBuilder: (_) {
                return Container();
              },
            ),
          ),
        );
      }
    }

    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 250),
      transitionBuilder: (Widget child, Animation<double> animation) {
        return FadeTransition(opacity: animation, child: child);
      },
      child: child,
    );
  }
}
