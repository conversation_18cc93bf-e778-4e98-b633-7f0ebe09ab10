import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:search_api/search_api.dart';
import 'package:shared/shared.dart';

import '../../common/config/config.dart';
import '../../data/repositories/database/entities/search.dart';
import '../../data/repositories/source/api/client/clients.dart';

part 'search_user_use_case.freezed.dart';

@Injectable()
class SearchUserUseCase
    extends BaseFutureUseCase<SearchUserInput, SearchUserOutput> {
  const SearchUserUseCase();

  @override
  Future<SearchUserOutput> buildUseCase(
    SearchUserInput input,
  ) async {
    final sessionKey = Config.getInstance().activeSessionKey;
    try {
      V3SearchUsersRequestBuilder request = V3SearchUsersRequestBuilder()
        ..keyword = input.keyword
        ..nextPageToken = input.nextPageToken;
      final result = await SearchClient().instance.searchUsers(
            body: request.build(),
          );
      if (result.data?.ok ?? false) {
        List<Search> searchResults = [];
        final apiUsers = result.data!.data!.toList();
        final hasNext = result.data!.paging?.hasNext ?? false;
        final nextPageToken = result.data!.paging?.nextPageToken ?? '0';
        for (final apiUser in apiUsers) {
          final result = Search.user(
            sessionKey: sessionKey!,
            userId: apiUser.userId!,
            embed: jsonEncode(
              {
                'avatar': apiUser.avatar,
                'aliasName': null,
                'displayName': apiUser.displayName,
                'username': apiUser.username,
                'userBadgeType': apiUser.userBadgeType,
              },
            ),
          );
          searchResults.add(result);
        }
        return SearchUserOutput(
          searchResults: searchResults,
          hasNext: hasNext,
          nextPageToken: nextPageToken,
        );
      }

      return SearchUserOutput(searchResults: []);
    } on Exception catch (_) {
      return SearchUserOutput(searchResults: []);
    }
  }
}

@freezed
sealed class SearchUserInput extends BaseInput with _$SearchUserInput {
  const SearchUserInput._();
  factory SearchUserInput({
    required String keyword,
    @Default('1') String nextPageToken,
  }) = _SearchUserInput;
}

@freezed
sealed class SearchUserOutput extends BaseOutput with _$SearchUserOutput {
  const SearchUserOutput._();
  factory SearchUserOutput({
    @Default([]) List<Search> searchResults,
    @Default('0') String nextPageToken,
    @Default(false) bool hasNext,
  }) = _SearchUserOutput;
}
