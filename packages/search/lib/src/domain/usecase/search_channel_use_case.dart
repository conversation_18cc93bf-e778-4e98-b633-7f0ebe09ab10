import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:search_api/search_api.dart';
import 'package:shared/shared.dart';

import '../../common/config/config.dart';
import '../../data/repositories/database/entities/search.dart';
import '../../data/repositories/source/api/client/clients.dart';

part 'search_channel_use_case.freezed.dart';

@Injectable()
class SearchChannelUseCase
    extends BaseFutureUseCase<SearchChannelInput, SearchChannelOutput> {
  const SearchChannelUseCase();

  @override
  Future<SearchChannelOutput> buildUseCase(
    SearchChannelInput input,
  ) async {
    final sessionKey = Config.getInstance().activeSessionKey;
    try {
      V3SearchChannelsRequestBuilder request = V3SearchChannelsRequestBuilder()
        ..keyword = input.keyword
        ..nextPageToken = input.nextPageToken;
      final result = await SearchClient().instance.searchChannels(
            body: request.build(),
          );
      if (result.data?.ok ?? false) {
        List<Search> searchResults = [];
        final apiChannels = result.data!.data!.toList();
        final hasNext = result.data!.paging?.hasNext ?? false;
        final nextPageToken = result.data!.paging?.nextPageToken ?? '0';
        for (final channel in apiChannels) {
          final result = Search.channel(
            sessionKey: sessionKey!,
            channelId: channel.channelId!,
            workspaceId: channel.workspaceId!,
            embed: jsonEncode(
              {
                'avatar': channel.avatar,
                'channelName': channel.name,
              },
            ),
          );
          searchResults.add(result);
        }
        return SearchChannelOutput(
          searchResults: searchResults,
          hasNext: hasNext,
          nextPageToken: nextPageToken,
        );
      }
      return SearchChannelOutput(searchResults: []);
    } on Exception catch (_) {
      return SearchChannelOutput(searchResults: []);
    }
  }
}

@freezed
sealed class SearchChannelInput extends BaseInput with _$SearchChannelInput {
  const SearchChannelInput._();
  factory SearchChannelInput({
    required String keyword,
    @Default('1') String nextPageToken,
  }) = _SearchChannelInput;
}

@freezed
sealed class SearchChannelOutput extends BaseOutput with _$SearchChannelOutput {
  const SearchChannelOutput._();
  factory SearchChannelOutput({
    @Default([]) List<Search> searchResults,
    @Default('0') String nextPageToken,
    @Default(false) bool hasNext,
  }) = _SearchChannelOutput;
}
