import 'package:injectable/injectable.dart' hide Order;

import '../../../common/config/config.dart';
import '../database/database.dart';
import '../database/entities/search.dart';
import '../database/generated/objectbox.g.dart';
import '../interfaces/search_repository.dart';

@LazySingleton(as: SearchRepository)
class SearchRepositoryImpl implements SearchRepository {
  SearchRepositoryImpl(this._searchStore);

  final SearchStore _searchStore;

  Box<Search> get _searchBox => _searchStore.box<Search>();

  String get _sessionKey => Config.getInstance().activeSessionKey ?? '';

  @override
  int insert(Search search) {
    var id = 0;
    if (search.isUser) {
      final query = _searchBox
          .query(
            Search_.sessionKey
                .equals(_sessionKey)
                .and(Search_.userId.equals(search.userId!)),
          )
          .build();

      final oldUser = query.findFirst();
      query.close();
      if (oldUser != null) {
        id = oldUser.id;
      }
    } else {
      final query = _searchBox
          .query(
            Search_.sessionKey
                .equals(_sessionKey)
                .and(Search_.workspaceId.equals(search.workspaceId!))
                .and(Search_.channelId.equals(search.channelId!)),
          )
          .build();

      final oldChannel = query.findFirst();
      query.close();
      if (oldChannel != null) {
        id = oldChannel.id;
      }
    }
    search.id = id;
    return _searchBox.put(search);
  }

  @override
  List<Search> getAll() {
    final query = _searchBox
        .query(Search_.sessionKey.equals(_sessionKey))
        .order(Search_.timestamp, flags: Order.descending)
        .build();
    final results = query.find();
    query.close();
    return results;
  }

  @override
  bool delete(int id) {
    return _searchBox.remove(id);
  }

  @override
  Search? getUserSearchByUserId(String userId) {
    final query = _searchBox
        .query(
          Search_.sessionKey
              .equals(_sessionKey)
              .and(Search_.userId.equals(userId)),
        )
        .build();
    final user = query.findFirst();
    query.close();
    return user;
  }

  @override
  bool deleteSession(String sessionKey) {
    final query =
        _searchBox.query(Search_.sessionKey.equals(sessionKey)).build();

    final removedCount = query.remove();

    query.close();
    return removedCount > 0;
  }

  @override
  Search? getChannelSearch(String channelId, String workspaceId) {
    final query = _searchBox
        .query(
          Search_.sessionKey.equals(_sessionKey).andAll(
            [
              Search_.channelId.equals(channelId),
              Search_.workspaceId.equals(workspaceId),
            ],
          ),
        )
        .build();
    final user = query.findFirst();
    query.close();
    return user;
  }
}
