import 'package:injectable/injectable.dart' hide Order;

import '../../../common/config/config.dart';
import '../database/database.dart';
import '../database/entities/history.dart';
import '../database/generated/objectbox.g.dart';
import '../interfaces/history_repository.dart';

@LazySingleton(as: HistoryRepository)
class HistoryRepositoryImpl implements HistoryRepository {
  HistoryRepositoryImpl(this._searchStore);

  final SearchStore _searchStore;

  Box<History> get _historyBox => _searchStore.box<History>();

  String get _sessionKey => Config.getInstance().activeSessionKey ?? '';

  @override
  int insert(String keyword) {
    final query = _historyBox
        .query(
          History_.sessionKey
              .equals(_sessionKey)
              .and(History_.keyword.equals(keyword)),
        )
        .build();
    var history = query.findFirst();
    query.close();
    if (history == null) {
      history = History(
        keyword: keyword,
        sessionKey: _sessionKey,
        timestamp: DateTime.now().millisecondsSinceEpoch,
      );
    } else {
      history.timestamp = DateTime.now().millisecondsSinceEpoch;
    }

    return _historyBox.put(history);
  }

  @override
  List<History> getAll() {
    final query = _historyBox
        .query(History_.sessionKey.equals(_sessionKey))
        .order(History_.timestamp, flags: Order.descending)
        .build();
    final histories = query.find();
    query.close();
    return histories;
  }

  @override
  bool delete(String keyword) {
    final query = _historyBox
        .query(
          History_.sessionKey
              .equals(_sessionKey)
              .and(History_.keyword.equals(keyword)),
        )
        .build();
    final history = query.findFirst();
    query.close();
    if (history != null) {
      return _historyBox.remove(history.id);
    }
    return false;
  }

  @override
  bool deleteSession(String sessionKey) {
    final query =
        _historyBox.query(History_.sessionKey.equals(sessionKey)).build();

    final removedCount = query.remove();

    query.close();
    return removedCount > 0;
  }
}
