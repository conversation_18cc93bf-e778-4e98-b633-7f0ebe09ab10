import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:sticker_api/sticker_api.dart';

import '../../data/repositories/database/entities/sticker.dart';
import '../../data/repositories/source/api/client/clients.dart';
import '../../serializers/sticker_serializer.dart';

@Injectable()
class GetListStickersUseCase
    extends BaseFutureUseCase<GetListStickersInput, GetListStickersOutput> {
  const GetListStickersUseCase();

  @protected
  @override
  Future<GetListStickersOutput> buildUseCase(
    GetListStickersInput input,
  ) async {
    final response = await StickerClient()
        .instance
        .listStickers(collectionId: input.collectionId);
    if (response.data?.ok ?? false) {
      List<Sticker> results = [];
      final apiStickers = response.data?.data?.toList() ?? [];
      for (final item in apiStickers) {
        final json = jsonDecode(
          standardSerializers.toJson(
            V3Sticker.serializer,
            item,
          ),
        );
        final jsonData = json as Map<String, dynamic>;
        final sticker = StickerSerializer.serializeFromJson(data: jsonData);
        if (sticker != null) {
          results.add(sticker);
        }
      }
      return GetListStickersOutput(stickers: results);
    }
    return GetListStickersOutput(stickers: null);
  }
}

class GetListStickersInput extends BaseInput {
  final String collectionId;

  const GetListStickersInput({required this.collectionId});
}

class GetListStickersOutput extends BaseOutput {
  final List<Sticker>? stickers;

  const GetListStickersOutput({this.stickers});
}
