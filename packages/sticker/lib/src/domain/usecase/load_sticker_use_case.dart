import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:sticker_api/sticker_api.dart';

import '../../common/config/config.dart';
import '../../data/repositories/database/entities/sticker.dart';
import '../../data/repositories/source/api/client/clients.dart';

@Injectable()
class LoadStickerUseCase
    extends BaseFutureUseCase<LoadStickerInput, LoadStickerOutput> {
  const LoadStickerUseCase();

  @protected
  @override
  Future<LoadStickerOutput> buildUseCase(
    LoadStickerInput input,
  ) async {
    try {
      final response =
          await StickerClient().instance.getSticker(stickerId: input.stickerId);
      if (response.data?.ok ?? false) {
        final json = jsonDecode(
          standardSerializers.toJson(
            V3Sticker.serializer,
            response.data!.data,
          ),
        );
        json["sessionKey"] = Config.getInstance().activeSessionKey;
        final sticker = Sticker.fromJson(json);
        return LoadStickerOutput(sticker: sticker);
      }
      return LoadStickerOutput(sticker: null);
    } on Exception catch (ex) {
      Log.e(ex);
      return LoadStickerOutput(sticker: null);
    }
  }
}

class LoadStickerInput extends BaseInput {
  final String stickerId;

  LoadStickerInput({
    required this.stickerId,
  });
}

class LoadStickerOutput extends BaseOutput {
  final Sticker? sticker;

  LoadStickerOutput({
    required this.sticker,
  });
}
