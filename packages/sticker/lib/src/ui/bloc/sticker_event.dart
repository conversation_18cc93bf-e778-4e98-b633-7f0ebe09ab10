part of 'sticker_bloc.dart';

class StickerEvent extends BaseBlocEvent {
  const StickerEvent();
}

class GetListStickerCollectionsEvent extends StickerEvent {
  const GetListStickerCollectionsEvent();
}

class GetListStickersEvent extends StickerEvent {
  final String collectionId;

  const GetListStickersEvent({required this.collectionId});
}

class ProcessStickerEvent extends StickerEvent {
  final StickerResource resource;

  const ProcessStickerEvent({required this.resource});
}

class AddRecentStickerEvent extends StickerEvent {
  final Sticker sticker;

  const AddRecentStickerEvent({required this.sticker});
}

class LoadRecentStickersEvent extends StickerEvent {
  final bool mustUpdateUi;

  const LoadRecentStickersEvent({this.mustUpdateUi = true});
}

class SpriteCreatedEvent extends StickerEvent {
  final String spritePath;
  final String stickerUriPath;

  const SpriteCreatedEvent({
    required this.spritePath,
    required this.stickerUriPath,
  });
}

class ChangeStickerOnPreviewEvent extends StickerEvent {
  final String? stickerOnPreview;

  const ChangeStickerOnPreviewEvent({
    this.stickerOnPreview,
  });
}

class GenerateThumbnailEvent extends StickerEvent {
  final NetworkSticker nSticker;

  const GenerateThumbnailEvent({required this.nSticker});
}

class ThumbnailCreatedEvent extends StickerEvent {
  final String thumbnailPath;
  final String stickerUriPath;

  const ThumbnailCreatedEvent({
    required this.thumbnailPath,
    required this.stickerUriPath,
  });
}
