import 'package:flutter/material.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

class StickerListSkeletonWidget extends StatelessWidget {
  const StickerListSkeletonWidget({super.key, this.showTitle = true});

  final bool showTitle;

  @override
  Widget build(BuildContext context) {
    final skeletonColor = Theme.of(context).brightness == Brightness.dark
        ? ui.AppColors.gray0
        : ui.AppColors.gray50;
    return ui.AppShimmerEffect(
      child: MediaQuery.removePadding(
        removeTop: true,
        removeBottom: true,
        context: context,
        child: CustomScrollView(
          physics: NeverScrollableScrollPhysics(),
          slivers: [
            if (showTitle)
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16.0,
                    vertical: 8.0,
                  ),
                  child: Row(
                    children: [
                      ui.StickerCollectionNameSkeletonWidget(),
                    ],
                  ),
                ),
              ),
            SliverPadding(
              padding: const EdgeInsets.symmetric(
                horizontal: 8.0,
                vertical: 0.0,
              ),
              sliver: SliverGrid(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    return Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: AspectRatio(
                        aspectRatio: 1,
                        child: DecoratedBox(
                          decoration: BoxDecoration(
                            color: skeletonColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    );
                  },
                  childCount: 12,
                ),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 5,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
