import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../sticker.dart';
import '../constants.dart';

class CollectionsListView extends StatefulWidget {
  const CollectionsListView({
    required this.collections,
    this.onTapCollection,
    this.pauseAllAnimation = false,
    super.key,
  });

  final List<Collection> collections;
  final void Function(String colelctionId)? onTapCollection;
  final bool pauseAllAnimation;

  @override
  State<CollectionsListView> createState() => CollectionsListViewState();
}

class CollectionsListViewState extends State<CollectionsListView> {
  ValueNotifier<String?> _currentCollectId = ValueNotifier(null);
  Map<String, GlobalKey> _collectionKeys = {};
  final PagingController<int, Collection> _pagingController =
      PagingController(firstPageKey: 0);

  @override
  void initState() {
    super.initState();
    for (final collection in widget.collections) {
      _collectionKeys[collection.collectionId] = GlobalKey();
    }
    _pagingController.addPageRequestListener((pageKey) {
      _pagingController.appendLastPage(widget.collections);
    });
    _currentCollectId.value = widget.collections.first.collectionId;
    if (widget.collections.first.stickers.isEmpty &&
        widget.collections.length >= 2) {
      _currentCollectId.value = widget.collections[1].collectionId;
    }
  }

  void changeCurrentCollection(String collectionId, {bool mustScroll = true}) {
    if (_currentCollectId.value != collectionId) {
      _currentCollectId.value = collectionId;
      if (_collectionKeys[collectionId]?.currentContext != null && mustScroll) {
        Scrollable.ensureVisible(
          _collectionKeys[collectionId]!.currentContext!,
        );
      }
    }
  }

  @override
  void didUpdateWidget(covariant CollectionsListView oldWidget) {
    if (oldWidget.collections != widget.collections) {
      if (_pagingController.itemList?.isEmpty ?? true) {
        _pagingController.appendLastPage(widget.collections);
      } else {
        _pagingController.itemList = widget.collections;
      }
      for (final collection in widget.collections) {
        _collectionKeys[collection.collectionId] = GlobalKey();
      }
    }
    // Update current collection after loading recent stickers or loading sticker list for the first time
    if ((oldWidget.collections.isNotEmpty &&
            widget.collections.isNotEmpty &&
            oldWidget.collections.first.stickers.length !=
                widget.collections.first.stickers.length) ||
        (oldWidget.collections.isEmpty && widget.collections.isNotEmpty)) {
      _currentCollectId.value = widget.collections.first.collectionId;
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return PagedListView<int, Collection>(
      pagingController: _pagingController,
      scrollDirection: Axis.horizontal,
      padding: EdgeInsets.symmetric(horizontal: 8.0),
      builderDelegate: PagedChildBuilderDelegate<Collection>(
        itemBuilder: (context, collection, index) {
          if (collection.collectionId == recentCollectionId) {
            if (collection.stickers.isNotEmpty) {
              return _buildItemContainer(
                collectionId: collection.collectionId,
                child: Padding(
                  padding: const EdgeInsets.all(2.0),
                  child: AspectRatio(
                    aspectRatio: 1,
                    child: Padding(
                      padding: const EdgeInsets.all(6.0),
                      child: ui.AppAssets.pngIconAsset(
                        ui.AppAssets.icClock24,
                        boxFit: BoxFit.contain,
                      ),
                    ),
                  ),
                ),
              );
            } else {
              return SizedBox.shrink();
            }
          } else {
            return _buildItemContainer(
              collectionId: collection.collectionId,
              child: Padding(
                padding: const EdgeInsets.all(3.0),
                child: StickerWidget.fromUrl(
                  lottieUrl: UrlUtils.parseSticker(collection.avatar),
                  filterQuality: FilterQuality.low,
                  size: StickerSize.x72,
                  placeholder:
                      StickerPlaceholder(stickerUrl: collection.avatar),
                  pauseAnimation: widget.pauseAllAnimation,
                ),
              ),
            );
          }
        },
      ),
    );
  }

  Widget _buildItemContainer({
    required String collectionId,
    required Widget child,
  }) {
    return Padding(
      padding: const EdgeInsets.all(4.0),
      child: GestureDetector(
        onTap: () {
          widget.onTapCollection?.call(collectionId);
          changeCurrentCollection(
            collectionId,
            mustScroll: false,
          );
        },
        child: ValueListenableBuilder(
          valueListenable: _currentCollectId,
          builder: (context, currentCollectId, _) {
            return Container(
              key: _collectionKeys[collectionId],
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.0),
                color: collectionId == currentCollectId
                    ? Theme.of(context).brightness == Brightness.light
                        ? ui.AppColors.bgStickerColorLight
                        : ui.AppColors.gray0
                    : Colors.transparent,
              ),
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: child,
              ),
            );
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    _pagingController.dispose();
    super.dispose();
  }
}
