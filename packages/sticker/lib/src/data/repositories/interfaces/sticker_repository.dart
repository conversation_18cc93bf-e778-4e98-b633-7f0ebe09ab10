import '../database/entities/sticker.dart';

abstract class StickerRepository {
  int insert(Sticker sticker);

  List<int> insertAll(List<Sticker> stickers);

  bool delete(int id);

  bool deleteByStickerId(String stickerId);

  List<Sticker> getListStickersByCollectionId(String collectionId);

  List<Sticker> getRecentStickers({int limit = 20});

  Sticker? getByStickerId(String stickerId);

  Sticker? getWaveSticker();
}
