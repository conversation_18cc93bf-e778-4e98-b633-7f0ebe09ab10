import 'package:flutter/foundation.dart';
import 'package:objectbox/internal.dart';
import 'package:objectbox/objectbox.dart';
import 'package:shared/shared.dart';

class StickerDatabase {
  StickerDatabase(this.store) {
    if (Admin.isAvailable() &&
        kDebugMode &&
        GlobalConfig.enableStickerBoxAdmin) {
      admin = StickerAdmin(store, bindUri: 'http://127.0.0.1:8095');
    }
  }

  StickerAdmin? admin;

  final StickerStore store;
}

class StickerAdmin extends Admin {
  StickerAdmin(this.store, {required this.bindUri})
      : super(store, bindUri: bindUri) {}

  final String bindUri;

  final Store store;
}

class StickerStore extends Store {
  StickerStore(ModelDefinition modelDefinition, {required String directory})
      : super(modelDefinition, directory: directory);
}
