{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:8518680075972636624", "lastPropertyId": "8:2007", "name": "Collection", "properties": [{"id": "1:3593630995312867326", "name": "id", "type": 6, "flags": 129}, {"id": "2:2001", "name": "<PERSON><PERSON><PERSON>", "type": 9}, {"id": "3:2002", "name": "collectionId", "type": 9}, {"id": "4:2003", "name": "description", "type": 9}, {"id": "5:2004", "name": "name", "type": 9}, {"id": "6:2005", "name": "avatar", "type": 9}, {"id": "7:2006", "name": "updateTime", "type": 9}, {"id": "8:2007", "name": "dbCacheData", "type": 9}], "relations": []}, {"id": "2:3335378469941666192", "lastPropertyId": "13:3011", "name": "<PERSON>er", "properties": [{"id": "1:2280837550696613301", "name": "id", "type": 6, "flags": 129}, {"id": "2:3001", "name": "<PERSON><PERSON><PERSON>", "type": 9}, {"id": "3:3002", "name": "collectionId", "type": 9}, {"id": "4:3003", "name": "stickerId", "type": 9}, {"id": "5:3004", "name": "name", "type": 9}, {"id": "6:3005", "name": "tags", "type": 30}, {"id": "7:3006", "name": "defaultEmoji", "type": 9}, {"id": "8:3007", "name": "basePath", "type": 9}, {"id": "9:3008", "name": "fileName", "type": 9}, {"id": "10:3009", "name": "stickerUrl", "type": 9}, {"id": "11:3010", "name": "updateTime", "type": 9}, {"id": "12:3012", "name": "sentTime", "type": 10}, {"id": "13:3011", "name": "dbCacheData", "type": 9}], "relations": []}, {"id": "3:352024053423921207", "lastPropertyId": "4:5004", "name": "StickerFrameCount", "properties": [{"id": "1:4001", "name": "id", "type": 6, "flags": 129}, {"id": "2:4002", "name": "stickerUrl", "type": 9}, {"id": "3:5003", "name": "frameCount", "type": 6}, {"id": "4:5004", "name": "firstFrameData", "type": 9}], "relations": []}], "lastEntityId": "3:352024053423921207", "lastIndexId": "0:0", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [], "retiredIndexUids": [], "retiredPropertyUids": [], "retiredRelationUids": [], "version": 1}