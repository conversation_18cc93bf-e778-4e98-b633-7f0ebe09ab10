import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
import 'package:objectbox/objectbox.dart';

import 'cache_data.dart';
import 'sticker.dart';

part 'collection.g.dart';

@Entity()
@JsonSerializable(explicitToJson: true)
class Collection {
  Collection({
    required this.sessionKey,
    required this.collectionId,
    required this.description,
    required this.name,
    required this.avatar,
    this.id = 0,
    this.updateTime,
    this.cacheData,
  });

  factory Collection.fromJson(Map<String, dynamic> json) =>
      _$CollectionFromJson(json);

  Map<String, dynamic> toJson() => _$CollectionToJson(this);

  @Id(assignable: true)
  int id = 0;

  @Property(uid: 2001)
  String sessionKey;

  @Property(uid: 2002)
  String collectionId;

  @Property(uid: 2003)
  String description;

  @Property(uid: 2004)
  String name;

  @Property(uid: 2005)
  String avatar;

  @Property(uid: 2006)
  String? updateTime;

  @Transient()
  CacheData? cacheData;

  @Property(uid: 2007)
  @JsonKey(includeFromJson: false, includeToJson: false)
  String? get dbCacheData {
    if (cacheData == null) {
      return null;
    }
    return jsonEncode(cacheData!.toJson());
  }

  set dbCacheData(String? value) {
    if (value != null) {
      cacheData = CacheData.fromJson(jsonDecode(value));
    }
  }

  @Transient()
  @JsonKey(includeFromJson: false, includeToJson: false)
  List<Sticker> stickers = [];
}
