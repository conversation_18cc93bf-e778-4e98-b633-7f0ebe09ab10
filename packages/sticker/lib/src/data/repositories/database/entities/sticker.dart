import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
import 'package:objectbox/objectbox.dart';
import 'package:shared/shared.dart';

import 'cache_data.dart';

part 'sticker.g.dart';

@Entity()
@JsonSerializable(explicitToJson: true)
class Sticker {
  Sticker({
    required this.sessionKey,
    required this.collectionId,
    required this.stickerId,
    required this.name,
    required this.tags,
    required this.defaultEmoji,
    required this.basePath,
    required this.fileName,
    required this.stickerUrl,
    this.updateTime,
    this.cacheData,
    this.sentTime,
    this.id = 0,
  });

  factory Sticker.quickSticker() {
    return Sticker(
      sessionKey: '',
      collectionId: '',
      stickerId: GlobalConfig.quickStickerId,
      name: '',
      tags: [],
      defaultEmoji: '🫰',
      basePath: '',
      fileName: '',
      stickerUrl: GlobalConfig.quickStickerUrl,
    );
  }

  factory Sticker.defaultWaveSticker() {
    return Sticker(
      sessionKey: '',
      collectionId: '01HXTX9THSW5KJ0C253Z0H7CAY',
      stickerId: GlobalConfig.quickStickerId,
      name: '<PERSON>',
      tags: [
        "hello",
        "wave",
        "waving hand",
        "👋",
        "hands",
        "gesture",
        "solong",
        "farewell",
        "hi",
        "palm",
      ],
      defaultEmoji: '👋',
      basePath: "01HXTX9THSW5KJ0C253Z0H7CAY/01HXTX9THPXEKDWX8WZZDDBZTQ/",
      fileName: "Hello.tgs",
      stickerUrl: GlobalConfig.defaultWaveStickerUrl,
    );
  }

  factory Sticker.fromJson(Map<String, dynamic> json) =>
      _$StickerFromJson(json);

  Map<String, dynamic> toJson() => _$StickerToJson(this);

  @Id(assignable: true)
  int id = 0;

  @Property(uid: 3001)
  String sessionKey;

  @Property(uid: 3002)
  String collectionId;

  @Property(uid: 3003)
  String stickerId;

  @Property(uid: 3004)
  String name;

  @Property(uid: 3005)
  List<String> tags;

  @Property(uid: 3006)
  String defaultEmoji;

  @Property(uid: 3007)
  String basePath;

  @Property(uid: 3008)
  String fileName;

  @Property(uid: 3009)
  String stickerUrl;

  @Property(uid: 3010)
  String? updateTime;

  @Transient()
  CacheData? cacheData;

  @Property(uid: 3011)
  @JsonKey(includeFromJson: false, includeToJson: false)
  String? get dbCacheData {
    if (cacheData == null) {
      return null;
    }
    return jsonEncode(cacheData!.toJson());
  }

  set dbCacheData(String? value) {
    if (value != null) {
      cacheData = CacheData.fromJson(jsonDecode(value));
    }
  }

  @Property(uid: 3012)
  DateTime? sentTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Sticker &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          collectionId == other.collectionId &&
          stickerId == other.stickerId &&
          defaultEmoji == other.defaultEmoji &&
          stickerUrl == other.stickerUrl &&
          updateTime == other.updateTime;

  @override
  int get hashCode =>
      id.hashCode ^
      collectionId.hashCode ^
      stickerId.hashCode ^
      defaultEmoji.hashCode ^
      stickerUrl.hashCode ^
      updateTime.hashCode;
}
