import 'dart:math';

import 'package:injectable/injectable.dart' hide Order;

import '../../../../sticker.dart';
import '../database/database.dart';
import '../database/generated/objectbox.g.dart';
import '../interfaces/sticker_repository.dart';

@LazySingleton(as: StickerRepository)
class StickerRepositoryImplement implements StickerRepository {
  StickerRepositoryImplement(this._stickerStore);

  final StickerStore _stickerStore;

  Box<Sticker> get _stickerBox => _stickerStore.box<Sticker>();

  String get _sessionKey => Config.getInstance().activeSessionKey ?? '';

  @override
  bool delete(int id) {
    return _stickerBox.remove(id);
  }

  @override
  bool deleteByStickerId(String stickerId) {
    var sticker = getByStickerId(stickerId);
    if (sticker == null) {
      return false;
    }
    return delete(sticker.id);
  }

  @override
  Sticker? getByStickerId(String stickerId) {
    final query = _stickerBox
        .query(
          Sticker_.stickerId
              .equals(stickerId)
              .and(Sticker_.sessionKey.equals(_sessionKey)),
        )
        .build();
    var sticker = query.findFirst();
    query.close();
    return sticker;
  }

  @override
  List<Sticker> getListStickersByCollectionId(String collectionId) {
    final query = _stickerBox
        .query(
          Sticker_.collectionId
              .equals(collectionId)
              .and(Sticker_.sessionKey.equals(_sessionKey)),
        )
        .build();
    var stickers = query.find();
    query.close();
    return stickers;
  }

  @override
  int insert(Sticker sticker) {
    if (sticker.id == 0) {
      var oldSticker = getByStickerId(sticker.stickerId);
      if (oldSticker != null) {
        sticker.id = oldSticker.id;
      }
    }
    return _stickerBox.put(sticker);
  }

  @override
  List<Sticker> getRecentStickers({int limit = 20}) {
    final query = _stickerBox
        .query(
          Sticker_.sentTime
              .notNull()
              .and(Sticker_.sessionKey.equals(_sessionKey)),
        )
        .order(Sticker_.sentTime, flags: Order.descending)
        .build()
      ..limit = limit;
    var stickers = query.find();
    query.close();
    return stickers;
  }

  @override
  List<int> insertAll(List<Sticker> stickers) {
    for (final sticker in stickers) {
      if (sticker.id == 0) {
        var oldSticker = getByStickerId(sticker.stickerId);
        if (oldSticker != null) {
          sticker.id = oldSticker.id;
        }
      }
    }
    return _stickerBox.putMany(stickers);
  }

  @override
  Sticker? getWaveSticker() {
    final query = _stickerBox
        .query(
          Sticker_.sessionKey
              .equals(_sessionKey)
              .and(Sticker_.name.equals('Hello')),
        )
        .build();
    var stickers = query.find();
    query.close();
    if (stickers.isEmpty) {
      return Sticker.defaultWaveSticker();
    }
    return stickers[Random().nextInt(stickers.length)];
  }
}
