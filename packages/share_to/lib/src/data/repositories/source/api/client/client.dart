import 'package:channel_view_api/channel_view_api.dart' as channel_view_api;
import 'package:friend_view_api/friend_view_api.dart' as friend_view_api;
import 'package:injectable/injectable.dart';
import 'package:search_api/search_api.dart' as search;
import '../../../../../../share_to.dart';
import 'package:shared/shared.dart';

@LazySingleton()
class ChannelViewClient {
  ChannelViewClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = channel_view_api.ChannelViewApi(
      dio: BaseClient.dio,
      serializers: channel_view_api.standardSerializers,
    ).getChannelViewServiceApi();
  }
  late final channel_view_api.ChannelViewServiceApi _instance;

  channel_view_api.ChannelViewServiceApi get instance => _instance;
}

@LazySingleton()
class FriendViewClient {
  FriendViewClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = friend_view_api.FriendViewApi(
      dio: BaseClient.dio,
      serializers: friend_view_api.standardSerializers,
    ).getFriendViewServiceApi();
  }
  late final friend_view_api.FriendViewServiceApi _instance;

  friend_view_api.FriendViewServiceApi get instance => _instance;
}

@LazySingleton()
class SearchClient {
  SearchClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = search.SearchApi(
      dio: BaseClient.dio,
      serializers: search.standardSerializers,
    ).getSearchServiceApi();
  }
  late final search.SearchServiceApi _instance;

  search.SearchServiceApi get instance => _instance;
}
