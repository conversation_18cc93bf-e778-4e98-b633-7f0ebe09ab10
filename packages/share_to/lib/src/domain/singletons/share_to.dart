import 'dart:async';

import 'package:injectable/injectable.dart';
import 'package:share_to_sdk/share_to/share_to_plugin.dart';

import '../../../share_to.dart';

@LazySingleton()
class ShareTo {
  static StreamSubscription? _streamSubscription;

  Future<void> listen(Function(ShareToFile) callback) async {
    _streamSubscription?.cancel();
    _streamSubscription =
        ShareToPlugin.instance.receiveSharingIntent.getMediaStream().listen(
      (value) {
        callback(value);
      },
      onError: (err) {},
    );

    await ShareToPlugin.instance.receiveSharingIntent.getInitialMedia().then(
      (value) {
        callback(value);
        ShareToPlugin.instance.receiveSharingIntent.reset();
      },
      onError: (err) {},
    );
  }

  Future<void> close() async {
    await _streamSubscription?.cancel();
  }
}
