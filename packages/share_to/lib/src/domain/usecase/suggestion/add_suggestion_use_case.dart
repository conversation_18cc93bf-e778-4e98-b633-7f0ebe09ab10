import 'package:app_core/core.dart';
import 'package:injectable/injectable.dart';
import 'package:search_api/search_api.dart';
import 'package:share_to_sdk/share_to/share_to_plugin.dart';
import 'package:shared/shared.dart';

import '../../../../share_to.dart';

@Injectable()
class AddSuggestionUseCase
    extends BaseFutureUseCase<AddSuggestionInput, AddSuggestionOutput> {
  AddSuggestionUseCase(this._listShareToIncomingUseCase);

  ListShareToIncomingUseCase _listShareToIncomingUseCase;

  @override
  Future<AddSuggestionOutput> buildUseCase(AddSuggestionInput input) async {
    final responseChannels = await _listShareToIncomingUseCase
        .execute(ListShareToIncomingInput(limit: 2));
    final listSuggestion = <SuggestionShareToType>[];
    final listShareToIncoming = responseChannels.listShareToIncoming ?? [];

    for (final item in listShareToIncoming) {
      var avatar = UrlUtils.parseAvatar(SearchUtils.getAvatar(item));
      final suggestion = SuggestionShareToType(
        conversationID: SearchUtils.getUserIdFromIncomingResult(item) ??
            item.channel?.channelId ??
            '',
        displayName: SearchUtils.getName(item) ?? '',
        avatarURL: avatar.isNotEmpty ? avatar : null,
        userId: SearchUtils.getUserIdFromIncomingResult(item),
        workspaceId: item.channel?.workspaceId,
        typeChannel: getType(item),
      );
      listSuggestion.add(suggestion);
    }
    await ShareToPlugin.instance.suggestionShareTo.deleteSuggestionShareTo();
    await ShareToPlugin.instance.suggestionShareTo
        .addSuggestionShareTo(listSuggestion);
    return AddSuggestionOutput(result: true);
  }

  String getType(V3ShareToIncomingResult item) {
    if (item.channel?.type?.name != null &&
        item.channel?.type?.name ==
            V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_CHANNEL.name) {
      return "channel";
    }
    return "dm";
  }
}

class AddSuggestionInput extends BaseInput {
  AddSuggestionInput();
}

class AddSuggestionOutput extends BaseOutput {
  AddSuggestionOutput({
    required this.result,
  });

  final bool result;
}
