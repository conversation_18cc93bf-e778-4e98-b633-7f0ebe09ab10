import 'package:app_core/core.dart';
import 'package:injectable/injectable.dart';
import 'package:search_api/search_api.dart';
import 'package:shared/shared.dart';

@Injectable()
class ListShareToUseCase
    extends BaseFutureUseCase<ListShareToInput, ListShareToOutput> {
  ListShareToUseCase(this._listShareToIncomingUseCase);

  ListShareToIncomingUseCase _listShareToIncomingUseCase;

  @override
  Future<ListShareToOutput> buildUseCase(ListShareToInput input) async {
    final responseChannels =
        await _listShareToIncomingUseCase.execute(ListShareToIncomingInput());
    return ListShareToOutput(
      listShareToIncoming: responseChannels.listShareToIncoming,
    );
  }
}

class ListShareToInput extends BaseInput {
  ListShareToInput();
}

class ListShareToOutput extends BaseOutput {
  ListShareToOutput({
    this.listShareToIncoming,
  });

  final List<V3ShareToIncomingResult>? listShareToIncoming;
}
