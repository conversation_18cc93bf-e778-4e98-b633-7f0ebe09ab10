import 'package:injectable/injectable.dart';
import 'package:search_api/search_api.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../data/repositories/source/api/client/client.dart';

@Injectable()
class SearchUserUseCase
    extends BaseFutureUseCase<SearchUserInput, SearchUserOutput> {
  SearchUserUseCase();

  @override
  Future<SearchUserOutput> buildUseCase(
    SearchUserInput input,
  ) async {
    try {
      V3SearchEverythingRequestBuilder request =
          V3SearchEverythingRequestBuilder()
            ..keyword = input.keyword
            ..nextPageToken = input.nextPageToken;

      final result = await SearchClient().instance.searchEverything(
            body: request.build(),
          );
      if (result.data?.ok ?? false) {
        final searchResults = <ui.UserItem>[];
        final apiUsers = result.data!.users!.toList();
        final channels = result.data!.channels!.toList();
        final hasNext = result.data!.paging?.hasNext ?? false;
        final nextPageToken = result.data!.paging?.nextPageToken ?? '0';
        for (final apiUser in apiUsers) {
          final result = ui.UserItem(
            id: apiUser.userId ?? '',
            name: apiUser.displayName != null && apiUser.displayName != ''
                ? '${apiUser.displayName}'
                : apiUser.username ?? '',
            url: UrlUtils.parseAvatar(apiUser.avatar),
            userId: apiUser.userId,
            workspaceId: null,
            type: V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_DM.name,
          );

          searchResults.add(result);
        }
        for (final channel in channels) {
          if (channel.type?.name !=
              V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_BROADCAST.name) {
            final result = ui.UserItem(
              id: channel.channelId ?? '',
              name: channel.name ?? '',
              url: UrlUtils.parseAvatar(channel.avatar),
              workspaceId: channel.workspaceId,
              channelId: channel.channelId,
              type: V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_CHANNEL.name,
            );
            searchResults.add(result);
          }
        }
        return SearchUserOutput(
          searchResults: searchResults,
          hasNext: hasNext,
          nextPageToken: nextPageToken,
        );
      }
      return SearchUserOutput(searchResults: []);
    } on Exception catch (_) {
      return SearchUserOutput(searchResults: []);
    }
  }
}

class SearchUserInput extends BaseInput {
  SearchUserInput({this.keyword, this.nextPageToken});

  final String? keyword;
  final String? nextPageToken;
}

class SearchUserOutput extends BaseOutput {
  SearchUserOutput({
    required this.searchResults,
    this.nextPageToken,
    this.hasNext,
  });

  final List<ui.UserItem> searchResults;
  final String? nextPageToken;
  final bool? hasNext;
}
