import 'dart:io';

import 'package:app_core/core.dart' as app_core;
import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:search_api/search_api.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../share_to.dart';
import '../../../common/di/di.dart';

class ShareToViewPage extends StatefulWidget {
  ShareToViewPage({
    required this.sharedFiles,
    super.key,
    this.onBack,
    this.title,
    this.action,
  });

  final ShareToFile sharedFiles;
  final VoidCallback? onBack;
  final String? title;
  final String? action;

  @override
  State<StatefulWidget> createState() {
    return _ShareToViewPageState();
  }
}

class _ShareToViewPageState extends State<ShareToViewPage> {
  String pathFile = '';
  double sizeFile = 100;
  late List<UserItem> allAccounts = [];
  late List<UserItem> selectedAccounts = [];

  late void Function(BuildContext, UserItem)
      onRemoveAccountSelectedButtonPressed;
  late void Function(BuildContext, String) onChangedTextField;
  late List<UserItem> filteredAccounts = [];
  late ShareToViewBloc bloc;
  late bool _bottomSheetShowed;
  List<app_core.UserPrivateData> _listUserPrivateData = [];

  @override
  void initState() {
    super.initState();
    _bottomSheetShowed = false;
    getIt<PendingIntentHandler>().shareToFile = null;
    getIt<PendingIntentHandler>().uri = null;
    checkLimitSizeFile();
  }

  @override
  void dispose() {
    super.dispose();
    getIt<PendingIntentHandler>().shareToFile = null;
    getIt<PendingIntentHandler>().uri = null;
    getIt<app_core.UserPrivateDataBloc>()
        .add(app_core.GetPrivateDataUnSubscriptionEvent());
  }

  void onBackButtonClicked() {
    widget.onBack?.call();
  }

  bool hasChannel(V3ShareToIncomingResult item) {
    return item.channel != null ? true : false;
  }

  int limitFile = 10;
  int valueMBIOS = 1000000;
  int valueMBAndroid = 1048576;

  checkLimitSizeFile() {
    int countFile = 0;
    int countByte = 0;
    bool hasGreaterThan2000 = false;
    bool noConnect = getIt<NetworkManager>().noConnection();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted || _bottomSheetShowed) return;

      _bottomSheetShowed = true;
      if (noConnect)
        return ui.DialogUtils.showLostConnectionShareToDialog(
          context,
          barrierDismissible: false,
          onOkClicked: (BuildContext dialogContext) {
            Navigator.pop(context);
            Navigator.pop(context);
          },
        );
      widget.sharedFiles.listMediaFile.forEach((item) {
        switch (item.type) {
          case SharedMediaType.image ||
                SharedMediaType.video ||
                SharedMediaType.audio ||
                SharedMediaType.file:
            countFile++;
            final file = File(item.path);
            countByte += file.lengthSync();

          case SharedMediaType.text || SharedMediaType.url:
            if (item.path.length > 2000) {
              hasGreaterThan2000 = true;
            }
        }
      });
      if (hasGreaterThan2000) {
        await ui.DialogUtils.showFutureCannotSendOnlySendUpTo2000(
          context,
          onOkClicked: (BuildContext dialogContext) {
            Navigator.pop(context, true);
          },
        );
        Navigator.pop(context);
      } else if (countFile > 10) {
        await ui.DialogUtils.showFutureOnly10FilesCanBeUploadedAtOneTime(
          context,
          onOkClicked: (BuildContext dialogContext) {
            Navigator.pop(context, true);
          },
        );
        Navigator.pop(context);
      } else if (countByte >
          (Platform.isAndroid ? valueMBAndroid * 100 : valueMBIOS * 100)) {
        await ui.DialogUtils.showFutureOnlyUploadLessThan100Mb(
          context,
          onOkClicked: (BuildContext dialogContext) {
            Navigator.pop(context, true);
          },
        );
        Navigator.pop(context);
      }
    });
  }

  void handleMapListUserItem(List<V3ShareToIncomingResult>? listChannel) {
    allAccounts.clear();
    selectedAccounts.clear();
    listChannel?.forEach((item) {
      final userItem = ui.UserItem(
        id: SearchUtils.getUserIdFromIncomingResult(item) ??
            item.channel?.channelId ??
            '',
        workspaceId: item.channel?.workspaceId,
        channelId: item.channel?.channelId,
        userId: SearchUtils.getUserIdFromIncomingResult(item),
        type: item.channel?.type?.name ??
            V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_DM.name,
        name: SearchUtils.getName(item, aliasName: getAliasName(item)) ?? '',
        url: UrlUtils.parseAvatar(
          SearchUtils.getAvatar(item),
        ),
      );

      allAccounts.add(userItem);
      if (widget.sharedFiles.channelID != null &&
          (widget.sharedFiles.channelID == item.channel?.channelId ||
              widget.sharedFiles.channelID == item.user?.userId ||
              widget.sharedFiles.channelID == item.friend?.userId)) {
        selectedAccounts.add(userItem);
      }
    });
  }

  void _blocUserPrivateListener(
    BuildContext context,
    app_core.UserPrivateDataState state,
  ) {
    state.when(
      initial: () {},
      listUserPrivateData:
          (List<app_core.UserPrivateData> listUserPrivateData) {
        _listUserPrivateData = listUserPrivateData;
      },
    );
  }

  String? getAliasName(V3ShareToIncomingResult item) {
    var userId = SearchUtils.getUserIdFromIncomingResult(item);
    if (userId == null) return null;
    try {
      app_core.UserPrivateData userPrivateData =
          _listUserPrivateData.firstWhere((user) => user.userId == userId);
      return userPrivateData.aliasName != null &&
              userPrivateData.aliasName!.isNotEmpty
          ? userPrivateData.aliasName
          : null;
    } catch (error) {
      return null;
    }
  }

  String? getAliasNameByUserId(String? userId) {
    if (userId == null) return null;
    try {
      app_core.UserPrivateData userPrivateData =
          _listUserPrivateData.firstWhere((user) => user.userId == userId);
      return userPrivateData.aliasName != null &&
              userPrivateData.aliasName!.isNotEmpty
          ? userPrivateData.aliasName
          : null;
    } catch (error) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    bloc = context.read<ShareToViewBloc>();
    return ui.AppScaffold(
      backgroundColor: Colors.transparent,
      body: BlocListener<app_core.UserPrivateDataBloc,
          app_core.UserPrivateDataState>(
        listener: _blocUserPrivateListener,
        child: BlocBuilder<ShareToViewBloc, ShareToState>(
          buildWhen: (prev, state) => prev != state,
          builder: (BuildContext context, ShareToState state) {
            return state.maybeWhen(
              initial: () {
                _bottomSheetShowed = false;
                return _buildShareToPage(true);
              },
              channelLoaded: (listChannel) {
                handleMapListUserItem(listChannel);
                return _buildShareToPage(false);
              },
              search: (listSearch) {
                listSearch.forEach((item) {
                  if (item.type ==
                      V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_DM.name) {
                    item.name = getAliasNameByUserId(item.id) ?? item.name;
                  }
                });
                filteredAccounts = listSearch;
                return _buildShareToPage(false);
              },
              waiting: (listChannel) {
                // allAccounts = [];
                listChannel?.forEach((item) {
                  allAccounts.add(item);
                });
                return _buildShareToPage(true);
              },
              orElse: () {
                return _buildShareToPage(false);
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildShareToPage(bool skeleton) {
    return ui.ShareToBottomSheet(
      allAccounts: allAccounts,
      selectedAccounts: selectedAccounts,
      onChangedTextField: (context, string) async {
        bloc.add(OnSearchEvent(keyword: string));
      },
      filteredAccounts: filteredAccounts,
      parentContext: context,
      skeleton: skeleton,
      onShareToSelectedUsers: (listUserItem) {
        bloc.add(OnShareEvent(selectedAccounts: selectedAccounts));
        Navigator.pop(context);
      },
      title: widget.title,
      submitTitle: widget.action,
    );
  }
}
