part of 'share_to_view_bloc.dart';

sealed class ShareTo<PERSON>vent extends BaseBloc<PERSON>vent {
  const ShareToEvent();
}

class OnInitShareToEvent extends ShareToEvent {
  const OnInitShareToEvent();
}

class OnSearchEvent extends ShareToEvent {
  const OnSearchEvent({this.keyword});

  final String? keyword;
}

class OnShareEvent extends ShareToEvent {
  const OnShareEvent({required this.selectedAccounts});

  final List<UserItem> selectedAccounts;
}

class OnLoadSearchEvent extends ShareToEvent {
  const OnLoadSearchEvent({required this.search});

  final List<UserItem> search;
}
