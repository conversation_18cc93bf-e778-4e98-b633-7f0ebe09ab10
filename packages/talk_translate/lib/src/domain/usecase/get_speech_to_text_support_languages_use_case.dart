import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:talk_and_translate/talk_and_translate.dart';

@injectable
class GetSpeechToTextSupportLanguagesUseCase extends BaseFutureUseCase<
    GetSpeechToTextSupportLanguagesUseCaseInput,
    GetSpeechToTextSupportLanguagesUseCaseOutput> {
  GetSpeechToTextSupportLanguagesUseCase();

  @override
  Future<GetSpeechToTextSupportLanguagesUseCaseOutput> buildUseCase(
    GetSpeechToTextSupportLanguagesUseCaseInput input,
  ) async {
    try {
      final locales =
          await input.talkAndTranslateProvider.getSttSupportedLanguages();
      return GetSpeechToTextSupportLanguagesUseCaseOutput(locales: locales);
    } catch (e) {
      throw Exception('Error while translate: $e');
    }
  }
}

class GetSpeechToTextSupportLanguagesUseCaseInput extends BaseInput {
  GetSpeechToTextSupportLanguagesUseCaseInput({
    required this.talkAndTranslateProvider,
  });
  final TalkAndTranslateProvider talkAndTranslateProvider;
}

class GetSpeechToTextSupportLanguagesUseCaseOutput extends BaseOutput {
  GetSpeechToTextSupportLanguagesUseCaseOutput({
    required this.locales,
  });
  final List<LocaleName> locales;
}
