import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:talk_and_translate/talk_and_translate.dart';

@injectable
class GetTextToSpeechSupportLanguagesUseCase extends BaseFutureUseCase<
    GetTextToSpeechSupportLanguagesUseCaseInput,
    GetTextToSpeechSupportLanguagesUseCaseOutput> {
  GetTextToSpeechSupportLanguagesUseCase();

  @override
  Future<GetTextToSpeechSupportLanguagesUseCaseOutput> buildUseCase(
    GetTextToSpeechSupportLanguagesUseCaseInput input,
  ) async {
    try {
      final locales =
          await input.talkAndTranslateProvider.getTtsSupportedLanguages();
      return GetTextToSpeechSupportLanguagesUseCaseOutput(locales: locales);
    } catch (e) {
      throw Exception('Error while translate: $e');
    }
  }
}

class GetTextToSpeechSupportLanguagesUseCaseInput extends BaseInput {
  GetTextToSpeechSupportLanguagesUseCaseInput({
    required this.talkAndTranslateProvider,
  });
  final TalkAndTranslateProvider talkAndTranslateProvider;
}

class GetTextToSpeechSupportLanguagesUseCaseOutput extends BaseOutput {
  GetTextToSpeechSupportLanguagesUseCaseOutput({
    required this.locales,
  });
  final List<dynamic> locales;
}
