import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:talk_and_translate/talk_and_translate.dart';

@injectable
class GetSpeechToTextResultUseCase extends BaseFutureUseCase<
    GetSpeechToTextResultUseCaseInput, GetSpeechToTextResultUseCaseOutput> {
  GetSpeechToTextResultUseCase();

  @override
  Future<GetSpeechToTextResultUseCaseOutput> buildUseCase(
    GetSpeechToTextResultUseCaseInput input,
  ) async {
    try {
      await input.talkAndTranslateProvider.startListening(
        localeId: input.localeId,
        onResult: (result) {
          input.onResult(result);
        },
      );
      return GetSpeechToTextResultUseCaseOutput(result: "Listening started");
    } catch (e) {
      throw Exception('Error while listening: $e');
    }
  }
}

class GetSpeechToTextResultUseCaseInput extends BaseInput {
  GetSpeechToTextResultUseCaseInput({
    required this.localeId,
    required this.onResult,
    required this.talkAndTranslateProvider,
  });

  final String localeId;
  final Function(String) onResult;
  final TalkAndTranslateProvider talkAndTranslateProvider;
}

class GetSpeechToTextResultUseCaseOutput extends BaseOutput {
  GetSpeechToTextResultUseCaseOutput({
    required this.result,
  });
  final String result;
}
