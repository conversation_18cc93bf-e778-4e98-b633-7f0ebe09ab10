import 'package:flutter/widgets.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:talk_and_translate/talk_and_translate.dart';

@injectable
class ActiveTextToSpeechUseCase extends BaseFutureUseCase<
    ActiveTextToSpeechUseCaseInput, ActiveTextToSpeechUseCaseOutput> {
  const ActiveTextToSpeechUseCase();

  @override
  Future<ActiveTextToSpeechUseCaseOutput> buildUseCase(
    ActiveTextToSpeechUseCaseInput input,
  ) async {
    try {
      await input.talkAndTranslateProvider.speak(
        text: input.text,
        completionHandler: () {
          input.completionHandler();
        },
      );
      return ActiveTextToSpeechUseCaseOutput(result: "Speak started");
    } catch (e) {
      throw Exception('Error while listening: $e');
    }
  }
}

class ActiveTextToSpeechUseCaseInput extends BaseInput {
  ActiveTextToSpeechUseCaseInput({
    required this.text,
    required this.completionHandler,
    required this.talkAndTranslateProvider,
  });

  final String text;
  final VoidCallback completionHandler;
  final TalkAndTranslateProvider talkAndTranslateProvider;
}

class ActiveTextToSpeechUseCaseOutput extends BaseOutput {
  ActiveTextToSpeechUseCaseOutput({
    required this.result,
  });
  final String result;
}
