import 'package:flutter/material.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

class TATMessage {
  final String messageId;
  final String content;
  final String translatedContent;
  final ui.TranslateStatusMessage translateStatus;
  final Locale currentLanguage;
  final Locale targetLanguage;
  final String translateSourceLanguage;
  final String translateTargetLanguage;
  final bool isSender;

  const TATMessage({
    required this.messageId,
    required this.content,
    required this.translatedContent,
    required this.translateStatus,
    required this.currentLanguage,
    required this.targetLanguage,
    required this.translateSourceLanguage,
    required this.translateTargetLanguage,
    required this.isSender,
  });

  TATMessage copyWith({
    String? messageId,
    String? content,
    String? translatedContent,
    ui.TranslateStatusMessage? translateStatus,
    Locale? currentLanguage,
    Locale? targetLanguage,
    String? translateSourceLanguage,
    String? translateTargetLanguage,
    bool? isSender,
  }) {
    return TATMessage(
      messageId: messageId ?? this.messageId,
      content: content ?? this.content,
      translatedContent: translatedContent ?? this.translatedContent,
      translateStatus: translateStatus ?? this.translateStatus,
      currentLanguage: currentLanguage ?? this.currentLanguage,
      targetLanguage: targetLanguage ?? this.targetLanguage,
      translateSourceLanguage:
          translateSourceLanguage ?? this.translateSourceLanguage,
      translateTargetLanguage:
          translateTargetLanguage ?? this.translateTargetLanguage,
      isSender: isSender ?? this.isSender,
    );
  }
}
