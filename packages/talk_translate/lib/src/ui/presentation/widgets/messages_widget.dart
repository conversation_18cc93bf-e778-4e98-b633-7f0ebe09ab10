import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../bloc/talk_translate_bloc.dart';
import 'translate_message_widget.dart';

class MessagesWidget extends StatefulWidget {
  const MessagesWidget({
    Key? key,
  }) : super(key: key);

  @override
  _MessagesWidgetState createState() => _MessagesWidgetState();
}

class _MessagesWidgetState extends State<MessagesWidget> {
  @override
  Widget build(BuildContext context) {
    final state = context.watch<TalkTranslateBloc>().state;
    return ListView.builder(
      itemCount: state.messages.length,
      itemBuilder: (context, index) {
        final message = state.messages[index];
        return Padding(
          padding: EdgeInsets.only(bottom: 12.h),
          child: TranslateMessageWidget(
            content: message.content,
            translatedContent: message.translatedContent,
            translateStatus: message.translateStatus,
            isSender: message.isSender,
            messageId: message.messageId,
            targetLanguage: message.targetLanguage,
            translateSourceLanguage: message.translateSourceLanguage,
            translateTargetLanguage: message.translateTargetLanguage,
          ),
        );
      },
    );
  }
}
