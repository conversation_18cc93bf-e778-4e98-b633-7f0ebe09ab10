import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../bloc/talk_translate_bloc.dart';

class TranslateMessageWidget extends StatefulWidget {
  final String messageId;
  final String content;
  final String translatedContent;
  final ui.TranslateStatusMessage translateStatus;
  final Locale targetLanguage;
  final String translateSourceLanguage;
  final String translateTargetLanguage;
  final bool isSender;

  const TranslateMessageWidget({
    required this.messageId,
    required this.content,
    required this.translatedContent,
    required this.translateStatus,
    required this.targetLanguage,
    required this.translateSourceLanguage,
    required this.translateTargetLanguage,
    required this.isSender,
  });

  @override
  State<TranslateMessageWidget> createState() => _TranslateMessageWidgetState();
}

class _TranslateMessageWidgetState extends State<TranslateMessageWidget> {
  @override
  Widget build(BuildContext context) {
    final bloc = context.watch<TalkTranslateBloc>();
    return widget.isSender
        ? ui.TranslateMessageSender(
            originalMessage: widget.content,
            translateMessage: widget.translatedContent,
            translateStatusMessage: widget.translateStatus,
            onPlayVoice: (context, messageId) {
              bloc.add(
                TalkTranslateEvent.speakText(
                  text: widget.translatedContent,
                  isSender: widget.isSender,
                  messageId: messageId,
                ),
              );
            },
            onPauseVoice: (context, messageId) {
              bloc.add(
                TalkTranslateEvent.stopSpeaking(
                  isSender: widget.isSender,
                  messageId: messageId,
                ),
              );
            },
            messageId: widget.messageId,
            language: widget.targetLanguage,
            onError: (context, messageId) {
              _showDialog(context, bloc);
            },
          )
        : ui.TranslateMessageReceiver(
            originalMessage: widget.content,
            translateMessage: widget.translatedContent,
            translateStatusMessage: widget.translateStatus,
            onPlayVoice: (context, messageId) {
              bloc.add(
                TalkTranslateEvent.speakText(
                  text: widget.translatedContent,
                  isSender: widget.isSender,
                  messageId: messageId,
                ),
              );
            },
            onPauseVoice: (context, messageId) {
              bloc.add(
                TalkTranslateEvent.stopSpeaking(
                  isSender: widget.isSender,
                  messageId: messageId,
                ),
              );
            },
            messageId: widget.messageId,
            language: widget.targetLanguage,
            onError: (parentContext, messageId) {
              _showDialog(parentContext, bloc);
            },
          );
  }

  void _showDialog(BuildContext parentcontext, TalkTranslateBloc bloc) {
    return ui.DialogUtils.showCanNotTranslateDialog(
      parentcontext,
      onLater: () {
        Navigator.of(parentcontext).pop();
      },
      onRetry: () {
        bloc.add(
          TalkTranslateEvent.translateText(
            messageId: widget.messageId,
            isSender: widget.isSender,
            isRetry: true,
          ),
        );
        Navigator.of(context).pop();
      },
    );
  }
}
