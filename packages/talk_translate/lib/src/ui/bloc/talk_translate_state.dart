part of 'talk_translate_bloc.dart';

@freezed
sealed class TalkTranslateState extends BaseBlocState
    with _$TalkTranslateState {
  const TalkTranslateState._();
  factory TalkTranslateState({
    @Default(false) bool ttsInitialized,
    @Default(false) bool isListening,
    @Default([]) List<Locale> allLocales,
    @Default({}) Map<String, String> receiverLanguage,
    @Default({}) Map<String, String> senderLanguage,
    @Default([]) List<String> generatingTexts,
    @Default('') String textRecognized,
    @Default(false) bool isFinalResult,
    @Default([]) List<String> resultsSearch,
    @Default([]) List<TATMessage> messages,
    @Default(false) bool errorListening,
    @Default(false) bool errorTranslate,
    @Default('') String error,
  }) = _TalkTranslateState;
}
