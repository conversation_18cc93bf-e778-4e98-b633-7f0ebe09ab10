import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:talk_and_translate/talk_and_translate.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../common/di/di.dart';
import '../../domain/models/tat_message.dart';
import '../../domain/usecase/active_text_to_speech_use_case.dart';
import '../../domain/usecase/get_speech_to_text_result_use_case.dart';
import '../../domain/usecase/get_speech_to_text_support_languages_use_case.dart';
import '../../domain/usecase/get_text_to_speech_support_languages_use_case.dart';
import '../../domain/usecase/translate_use_case.dart';

part 'talk_translate_bloc.freezed.dart';
part 'talk_translate_event.dart';
part 'talk_translate_state.dart';

@injectable
class TalkTranslateBloc
    extends BaseBloc<TalkTranslateEvent, TalkTranslateState> {
  final GetSpeechToTextResultUseCase _getSpeechToTextResultUseCase;
  final TranslateUseCase _translateUseCase;
  final ActiveTextToSpeechUseCase _activeTextToSpeechUseCase;
  final GetSpeechToTextSupportLanguagesUseCase
      _getSpeechToTextSupportLanguagesUseCase;
  final GetTextToSpeechSupportLanguagesUseCase
      _getTextToSpeechSupportLanguagesUseCase;

  String _previousTextRecognized = '';

  TalkAndTranslateProvider? _receiver;
  TalkAndTranslateProvider? _sender;

  final prefs = getIt<SharedPreferences>();

  TalkTranslateBloc(
    this._getSpeechToTextResultUseCase,
    this._translateUseCase,
    this._activeTextToSpeechUseCase,
    this._getSpeechToTextSupportLanguagesUseCase,
    this._getTextToSpeechSupportLanguagesUseCase,
  ) : super(TalkTranslateState()) {
    on<TalkTranslateInitializeEvent>(_onInitialize);
    on<TalkTranslateStartListeningEvent>(_onStartListening);
    on<TalkTranslateStopListeningEvent>(_onStopListening);
    on<TalkTranslateTextRecognizedEvent>(_onSpeechRecognized);
    on<TalkTranslateTranslateTextEvent>(_onTranslateText);
    on<TalkTranslateAddMessageEvent>(_onAddMessage);
    on<TalkTranslateStopSpeakingEvent>(_onStopSpeaking);
    on<TalkTranslateSpeakTextEvent>(_onSpeakText);
    on<TalkTranslateCompleteSpeakingEvent>(_onCompleteSpeaking);
    on<TalkTranslateSelectLanguageEvent>(_onSelectLanguage);
    on<TalkTranslateClearTextRegconizedEvent>(_onClearTextRegconized);
    on<TalkTranslateSwapLanguagesEvent>(_onSwapLanguages);
  }

  List<String> getMatchingLocales(
    List<LocaleName> locales,
    List<dynamic> dynamicList,
  ) {
    final dynamicLocaleIds = dynamicList
        .map((e) => e.toString().replaceAll(RegExp(r'[-_]'), ''))
        .toSet();

    return locales
        .where(
          (locale) => dynamicLocaleIds
              .contains(locale.localeId.replaceAll(RegExp(r'[-_]'), '')),
        )
        .map((locale) => locale.localeId)
        .toList();
  }

  List<String> getAllLocales(List<Locale> locales, Locale myLocale) {
    final allLocales = locales.map((language) {
      final locale =
          ui.AppLocaleUtil.handleDisplayLocaleText(language, myLocale);
      return locale;
    }).toList()
      ..sort((a, b) => a.compareTo(b));
    return allLocales;
  }

  Locale _handleLocale(String locale) {
    List<String> parts = locale.split(RegExp(r'[-_]'));
    return Locale(
      parts[0],
      parts[1],
    );
  }

  String? _handleSenderSpeakLanguage(String languageCode) {
    switch (languageCode) {
      case 'vi':
        return 'en-US';
      case 'en':
        return 'vi-VN';
      default:
        return 'en-US';
    }
  }

  String? _handleReceiverSpeakLanguage(String languageCode) {
    switch (languageCode) {
      case 'vi':
        return 'vi-VN';
      case 'en':
        return 'en-US';
      case 'hi':
        return 'hi-IN';
      case 'id':
        return 'id-ID';
      default:
        return 'en-US';
    }
  }

  String? _handleSenderListenLanguage(String languageCode) {
    switch (languageCode) {
      case 'vi':
        return 'vi_VN';
      case 'en':
        return 'en_US';
      case 'hi':
        return 'hi_IN';
      case 'id':
        return 'id_ID';
      default:
        return 'en_US';
    }
  }

  String? _handleReceiverListenLanguage(String languageCode) {
    switch (languageCode) {
      case 'vi':
        return 'en_US';
      case 'en':
        return 'vi_VN';

      default:
        return 'en_US';
    }
  }

  Future<void> _onInitialize(
    TalkTranslateInitializeEvent event,
    Emitter<TalkTranslateState> emit,
  ) async {
    String? storedSenderMap = prefs.getString('senderMap');
    String? storedReceiverMap = prefs.getString('receiverMap');

    Map<String, String> senderMap = storedSenderMap != null
        ? Map<String, String>.from(jsonDecode(storedSenderMap))
        : {
            'translateSourceLanguage': event.myLocale.languageCode,
            'translateTargetLanguage': event.myLocale.languageCode != 'vi'
                ? event.myLocale.languageCode != 'en'
                    ? 'en'
                    : 'vi'
                : 'en',
            'speakLanguage':
                _handleSenderSpeakLanguage(event.myLocale.languageCode)!,
            'listenLanguage':
                _handleSenderListenLanguage(event.myLocale.languageCode)!,
          };

    Map<String, String> receiverMap = storedReceiverMap != null
        ? Map<String, String>.from(jsonDecode(storedReceiverMap))
        : {
            'translateSourceLanguage': event.myLocale.languageCode != 'vi'
                ? event.myLocale.languageCode != 'en'
                    ? 'en'
                    : 'vi'
                : 'en',
            'translateTargetLanguage': event.myLocale.languageCode,
            'speakLanguage':
                _handleReceiverSpeakLanguage(event.myLocale.languageCode)!,
            'listenLanguage':
                _handleReceiverListenLanguage(event.myLocale.languageCode)!,
          };

    _receiver = TalkAndTranslateProvider(
      translateSourceLanguage: receiverMap['translateSourceLanguage']!,
      translateTargetLanguage: receiverMap['translateTargetLanguage']!,
      speakLanguage: receiverMap['speakLanguage']!,
      listenLanguage: receiverMap['listenLanguage']!,
    );
    _sender = TalkAndTranslateProvider(
      translateSourceLanguage: senderMap['translateSourceLanguage']!,
      translateTargetLanguage: senderMap['translateTargetLanguage']!,
      speakLanguage: senderMap['speakLanguage']!,
      listenLanguage: senderMap['listenLanguage']!,
    );

    await _receiver!.initialize();
    await _sender!.initialize();

    await prefs.setString('senderMap', jsonEncode(senderMap));
    await prefs.setString('receiverMap', jsonEncode(receiverMap));

    final speechToTextSupportLanguagesOutput =
        await _getSpeechToTextSupportLanguagesUseCase.execute(
      GetSpeechToTextSupportLanguagesUseCaseInput(
        talkAndTranslateProvider: _receiver!,
      ),
    );

    final textToSpeechSupportLanguagesOutput =
        await _getTextToSpeechSupportLanguagesUseCase.execute(
      GetTextToSpeechSupportLanguagesUseCaseInput(
        talkAndTranslateProvider: _receiver!,
      ),
    );

    List<String> matchingLocales = getMatchingLocales(
      speechToTextSupportLanguagesOutput.locales,
      textToSpeechSupportLanguagesOutput.locales,
    );

    List<Locale> allLocales = matchingLocales.map((localeId) {
      List<String> parts = localeId.split(RegExp(r'[-_]'));

      return Locale(parts[0], parts[1]);
    }).toList();

    emit(
      state.copyWith(
        allLocales: allLocales,
        receiverLanguage: receiverMap,
        senderLanguage: senderMap,
        ttsInitialized: true,
      ),
    );
  }

  Future<void> _onStartListening(
    TalkTranslateStartListeningEvent event,
    Emitter<TalkTranslateState> emit,
  ) async {
    emit(state.copyWith(isListening: true, errorListening: false));

    try {
      await _getSpeechToTextResultUseCase.execute(
        GetSpeechToTextResultUseCaseInput(
          localeId: event.localeId,
          onResult: (transcription) {
            if (transcription.isNotEmpty) {
              add(
                TalkTranslateEvent.textRecognized(
                  transcription,
                ),
              );
            }
          },
          talkAndTranslateProvider: event.isSender ? _sender! : _receiver!,
        ),
      );
    } catch (e) {
      emit(state.copyWith(error: e.toString(), errorListening: true));
    }
  }

  Future<void> _onStopListening(
    TalkTranslateStopListeningEvent event,
    Emitter<TalkTranslateState> emit,
  ) async {
    if (event.isSender) {
      await _sender!.stopListening();
    } else {
      await _receiver!.stopListening();
    }
    emit(state.copyWith(isListening: false));

    if (event.content.isNotEmpty) {
      final message = TATMessage(
        content: event.content,
        translatedContent: '',
        translateStatus: ui.TranslateStatusMessage.loading,
        isSender: event.isSender,
        messageId: (state.messages.length).toString(),
        currentLanguage: event.isSender
            ? _handleLocale(_sender!.listenLanguage)
            : _handleLocale(_receiver!.listenLanguage),
        targetLanguage: event.isSender
            ? _handleLocale(_receiver!.listenLanguage)
            : _handleLocale(_sender!.listenLanguage),
        translateSourceLanguage: event.isSender
            ? _sender!.translateSourceLanguage
            : _receiver!.translateSourceLanguage,
        translateTargetLanguage: event.isSender
            ? _sender!.translateTargetLanguage
            : _receiver!.translateTargetLanguage,
      );
      add(
        TalkTranslateEvent.addMessage(
          messages: List.from(state.messages)..add(message),
          messageId: (state.messages.length).toString(),
          isSender: event.isSender,
        ),
      );
    }
  }

  Future<void> _onAddMessage(
    TalkTranslateAddMessageEvent event,
    Emitter<TalkTranslateState> emit,
  ) async {
    emit(state.copyWith(messages: event.messages));

    add(
      TalkTranslateEvent.translateText(
        isRetry: false,
        isSender: event.isSender,
        messageId: event.messageId,
      ),
    );
  }

  Future<void> _onSpeechRecognized(
    TalkTranslateTextRecognizedEvent event,
    Emitter<TalkTranslateState> emit,
  ) async {
    final textRecognized = event.textRecognized;
    emit(
      state.copyWith(
        textRecognized: textRecognized,
      ),
    );

    final List<String> generatingTexts =
        List<String>.from(state.generatingTexts);
    String changedText = '';

    if (textRecognized != _previousTextRecognized) {
      if (_previousTextRecognized.isNotEmpty &&
          textRecognized.startsWith(_previousTextRecognized)) {
        changedText = textRecognized.substring(_previousTextRecognized.length);
      } else if (generatingTexts.length == 0) {
        changedText = textRecognized;
      }

      generatingTexts.add(changedText);
      _previousTextRecognized = textRecognized;
    }
    emit(
      state.copyWith(
        generatingTexts: generatingTexts,
      ),
    );
  }

  Future<void> _onTranslateText(
    TalkTranslateTranslateTextEvent event,
    Emitter<TalkTranslateState> emit,
  ) async {
    try {
      emit(state.copyWith(errorTranslate: false));
      final output = await _translateUseCase.execute(
        TranslateUseCaseInput(
          text: state.textRecognized,
          talkAndTranslateProvider: event.isSender ? _sender! : _receiver!,
        ),
      );
      if (!event.isRetry) {
        final messages = state.messages.map((message) {
          if (message.messageId == event.messageId) {
            return message.copyWith(
              translatedContent: output.translatedText,
              translateStatus: ui.TranslateStatusMessage.inActive,
            );
          }
          return message;
        }).toList();

        add(
          TalkTranslateEvent.speakText(
            messageId: event.messageId,
            text: output.translatedText,
            isSender: event.isSender,
          ),
        );

        emit(
          state.copyWith(
            messages: messages,
          ),
        );
      } else {
        final messages = state.messages.map((message) {
          if (message.messageId == event.messageId) {
            return message.copyWith(
              translatedContent: output.translatedText,
              translateStatus: ui.TranslateStatusMessage.inActive,
            );
          }
          return message;
        }).toList();
        add(
          TalkTranslateEvent.speakText(
            messageId: event.messageId,
            text: output.translatedText,
            isSender: event.isSender,
          ),
        );

        emit(
          state.copyWith(
            messages: messages,
          ),
        );
      }
    } catch (e) {
      final messages = state.messages.map((message) {
        if (message.messageId == event.messageId) {
          return message.copyWith(
            translatedContent: state.textRecognized,
            translateStatus: ui.TranslateStatusMessage.failed,
          );
        }
        return message;
      }).toList();
      emit(
        state.copyWith(
          error: e.toString(),
          messages: messages,
          errorTranslate: true,
        ),
      );
    }
  }

  Future<void> _updateMessageStatus(
    String messageId,
    ui.TranslateStatusMessage status,
    bool isSender,
    Emitter<TalkTranslateState> emit,
  ) async {
    final updatedMessages = state.messages.map((message) {
      if (message.messageId == messageId) {
        return message.copyWith(
          translateStatus: status,
        );
      } else if (message.translateStatus == ui.TranslateStatusMessage.active ||
          message.translateStatus == ui.TranslateStatusMessage.loading) {
        add(
          TalkTranslateEvent.stopSpeaking(
            isSender: isSender,
            messageId: message.messageId,
          ),
        );
        return message.copyWith(
          translateStatus: ui.TranslateStatusMessage.inActive,
        );
      }
      return message;
    }).toList();

    emit(state.copyWith(messages: updatedMessages));
  }

  Future<void> _onSpeakText(
    TalkTranslateSpeakTextEvent event,
    Emitter<TalkTranslateState> emit,
  ) async {
    if (event.isSender) {
      await _sender!.stopSpeaking();
    } else {
      await _receiver!.stopSpeaking();
    }

    await Future.delayed(const Duration(milliseconds: 1));

    await _updateMessageStatus(
      event.messageId,
      ui.TranslateStatusMessage.loading,
      event.isSender,
      emit,
    );

    try {
      await Future.delayed(const Duration(milliseconds: 100));

      await _activeTextToSpeechUseCase.execute(
        ActiveTextToSpeechUseCaseInput(
          text: event.text,
          completionHandler: () {
            add(
              TalkTranslateEvent.completeSpeaking(
                isSender: event.isSender,
                messageId: event.messageId,
              ),
            );
          },
          talkAndTranslateProvider: event.isSender ? _sender! : _receiver!,
        ),
      );
      await _updateMessageStatus(
        event.messageId,
        ui.TranslateStatusMessage.active,
        event.isSender,
        emit,
      );
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  void _onCompleteSpeaking(
    TalkTranslateCompleteSpeakingEvent event,
    Emitter<TalkTranslateState> emit,
  ) async {
    await _updateMessageStatus(
      event.messageId,
      ui.TranslateStatusMessage.inActive,
      event.isSender,
      emit,
    );
  }

  Future<void> _onStopSpeaking(
    TalkTranslateStopSpeakingEvent event,
    Emitter<TalkTranslateState> emit,
  ) async {
    if (event.isSender) {
      await _sender!.stopSpeaking();
    } else {
      await _receiver!.stopSpeaking();
    }

    await _updateMessageStatus(
      event.messageId,
      ui.TranslateStatusMessage.inActive,
      event.isSender,
      emit,
    );
  }

  Map<String, String> _createProvider({
    required String sourceLang,
    required String targetLang,
    required String speakLang,
    required String listenLang,
  }) {
    return {
      'translateSourceLanguage': sourceLang,
      'translateTargetLanguage': targetLang,
      'speakLanguage': speakLang,
      'listenLanguage': listenLang,
    };
  }

  Future<void> _onSelectLanguage(
    TalkTranslateSelectLanguageEvent event,
    Emitter<TalkTranslateState> emit,
  ) async {
    final isSender = event.isSender;
    final targetLanguage = event.language.languageCode;
    final countryCode = event.language.countryCode!;
    final speakLanguage = '$targetLanguage-$countryCode';
    final listenLanguage = '$targetLanguage\_$countryCode';
    final bool isSameLanguageAsTarget = _handleLocale(
          isSender ? _receiver!.listenLanguage : _sender!.listenLanguage,
        ) ==
        event.language;

    if (isSender) {
      final senderMap = _createProvider(
        sourceLang: targetLanguage,
        targetLang: isSameLanguageAsTarget
            ? _sender!.translateSourceLanguage
            : _receiver!.translateSourceLanguage,
        speakLang: isSameLanguageAsTarget
            ? _receiver!.speakLanguage
            : _sender!.speakLanguage,
        listenLang: listenLanguage,
      );

      final receiverMap = _createProvider(
        sourceLang: isSameLanguageAsTarget
            ? _sender!.translateSourceLanguage
            : _receiver!.translateSourceLanguage,
        targetLang: targetLanguage,
        speakLang: speakLanguage,
        listenLang: isSameLanguageAsTarget
            ? _sender!.listenLanguage
            : _receiver!.listenLanguage,
      );
      _sender = _sender!.copyWith(
        translateSourceLanguage: senderMap['translateSourceLanguage'],
        translateTargetLanguage: senderMap['translateTargetLanguage'],
        speakLanguage: senderMap['speakLanguage'],
        listenLanguage: senderMap['listenLanguage'],
      );
      _receiver = _receiver!.copyWith(
        translateSourceLanguage: receiverMap['translateSourceLanguage'],
        translateTargetLanguage: receiverMap['translateTargetLanguage'],
        speakLanguage: receiverMap['speakLanguage'],
        listenLanguage: receiverMap['listenLanguage'],
      );
      await prefs.setString('senderMap', jsonEncode(senderMap));
      await prefs.setString('receiverMap', jsonEncode(receiverMap));
      emit(
        state.copyWith(
          senderLanguage: senderMap,
          receiverLanguage: receiverMap,
        ),
      );
    } else {
      final senderMap = _createProvider(
        sourceLang: isSameLanguageAsTarget
            ? _receiver!.translateSourceLanguage
            : _sender!.translateSourceLanguage,
        targetLang: targetLanguage,
        speakLang: speakLanguage,
        listenLang: isSameLanguageAsTarget
            ? _receiver!.listenLanguage
            : _sender!.listenLanguage,
      );

      final receiverMap = _createProvider(
        sourceLang: targetLanguage,
        targetLang: isSameLanguageAsTarget
            ? _receiver!.translateSourceLanguage
            : _sender!.translateSourceLanguage,
        speakLang: isSameLanguageAsTarget
            ? _sender!.speakLanguage
            : _receiver!.speakLanguage,
        listenLang: listenLanguage,
      );

      _sender = _sender!.copyWith(
        translateSourceLanguage: senderMap['translateSourceLanguage'],
        translateTargetLanguage: senderMap['translateTargetLanguage'],
        speakLanguage: senderMap['speakLanguage'],
        listenLanguage: senderMap['listenLanguage'],
      );
      _receiver = _receiver!.copyWith(
        translateSourceLanguage: receiverMap['translateSourceLanguage'],
        translateTargetLanguage: receiverMap['translateTargetLanguage'],
        speakLanguage: receiverMap['speakLanguage'],
        listenLanguage: receiverMap['listenLanguage'],
      );
      await prefs.setString('senderMap', jsonEncode(senderMap));
      await prefs.setString('receiverMap', jsonEncode(receiverMap));
      emit(
        state.copyWith(
          senderLanguage: senderMap,
          receiverLanguage: receiverMap,
        ),
      );
    }
    await Future.wait([
      _receiver!.initialize(),
      _sender!.initialize(),
    ]);
  }

  void _onClearTextRegconized(
    TalkTranslateClearTextRegconizedEvent event,
    Emitter<TalkTranslateState> emit,
  ) {
    emit(
      state.copyWith(
        textRecognized: '',
        generatingTexts: [],
      ),
    );
  }

  void _onSwapLanguages(
    TalkTranslateSwapLanguagesEvent event,
    Emitter<TalkTranslateState> emit,
  ) async {
    final tempReceiverMap = state.receiverLanguage;
    final tempReceiverProvider = _receiver;
    _receiver = _sender;
    _sender = tempReceiverProvider;
    await prefs.setString('senderMap', jsonEncode(state.receiverLanguage));
    await prefs.setString('receiverMap', jsonEncode(state.senderLanguage));
    emit(
      state.copyWith(
        receiverLanguage: state.senderLanguage,
        senderLanguage: tempReceiverMap,
      ),
    );
  }
}
