import 'package:call_api/call_api.dart' as call;
import 'package:friend_view_api/friend_view_api.dart' as friend_view;
import 'package:injectable/injectable.dart';
import 'package:search_api/search_api.dart' as search;
import 'package:shared/shared.dart';

import '../../../../../common/config/config.dart';

@LazySingleton()
class CallClient {
  late final call.CallServiceApi _instance;

  CallClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = call.CallApi(
      dio: BaseClient.dio,
      serializers: call.standardSerializers,
    ).getCallServiceApi();
  }

  call.CallServiceApi get instance => _instance;
}

@LazySingleton()
class SearchClient {
  late final search.SearchServiceApi _instance;

  SearchClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = search.SearchApi(
      dio: BaseClient.dio,
      serializers: search.standardSerializers,
    ).getSearchServiceApi();
  }

  search.SearchServiceApi get instance => _instance;
}

@LazySingleton()
class FriendViewClient {
  late final friend_view.FriendViewServiceApi _instance;

  FriendViewClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = friend_view.FriendViewApi(
      dio: BaseClient.dio,
      serializers: friend_view.standardSerializers,
    ).getFriendViewServiceApi();
  }

  friend_view.FriendViewServiceApi get instance => _instance;
}
