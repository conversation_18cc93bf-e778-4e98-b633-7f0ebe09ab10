import 'package:flutter/foundation.dart';
import 'package:objectbox/internal.dart';
import 'package:objectbox/objectbox.dart';
import 'package:shared/shared.dart';

class CallDatabase {
  CallDatabase(this.store) {
    if (Admin.isAvailable() &&
        kDebugMode &&
        GlobalConfig.enableStickerBoxAdmin) {
      admin = CallAdmin(store, bindUri: 'http://127.0.0.1:8097');
    }
  }

  CallAdmin? admin;

  final CallStore store;
}

class CallAdmin extends Admin {
  CallAdmin(this.store, {required this.bindUri})
      : super(store, bindUri: bindUri) {}

  final String bindUri;

  final Store store;
}

class CallStore extends Store {
  CallStore(ModelDefinition modelDefinition, {required String directory})
      : super(modelDefinition, directory: directory);
}
