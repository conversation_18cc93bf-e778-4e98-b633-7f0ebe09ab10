{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "4:1798218016539289990", "lastPropertyId": "15:7014", "name": "CallLog", "properties": [{"id": "1:1386992090774543293", "name": "id", "type": 6, "flags": 129}, {"id": "2:7001", "name": "<PERSON><PERSON><PERSON>", "type": 9, "flags": 2048, "indexId": "1:2104078788508694510"}, {"id": "3:7002", "name": "callId", "type": 9, "flags": 2048, "indexId": "2:6761504558222418223"}, {"id": "4:7003", "name": "callerId", "type": 9}, {"id": "5:7004", "name": "calleeId", "type": 9}, {"id": "6:7005", "name": "callState", "type": 6}, {"id": "7:7006", "name": "endedReason", "type": 6}, {"id": "8:7007", "name": "callTimeInSeconds", "type": 6}, {"id": "9:7008", "name": "isMissedCall", "type": 1}, {"id": "10:7009", "name": "isInComingCall", "type": 1}, {"id": "11:7010", "name": "isOutgoing", "type": 1}, {"id": "12:7011", "name": "isVideoCall", "type": 1}, {"id": "13:7012", "name": "readTime", "type": 9}, {"id": "14:7013", "name": "createTime", "type": 9}, {"id": "15:7014", "name": "endedTime", "type": 9}], "relations": []}], "lastEntityId": "4:1798218016539289990", "lastIndexId": "2:6761504558222418223", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [8518680075972636624, 3335378469941666192, 352024053423921207], "retiredIndexUids": [], "retiredPropertyUids": [3593630995312867326, 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2280837550696613301, 3001, 3002, 3003, 3004, 3005, 3006, 3007, 3008, 3009, 3010, 3012, 3011, 4001, 4002, 5003, 5004], "retiredRelationUids": [], "version": 1}