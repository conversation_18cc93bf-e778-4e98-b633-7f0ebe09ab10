import 'package:flutter/material.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart' as rtc;
import 'package:livekit_client/livekit_client.dart';

class VideoTrackWidget extends StatefulWidget {
  const VideoTrackWidget({
    super.key,
    required this.videoTrack,
    required this.disposeRender,
    required this.cacheRenderer,
    required this.trackId,
  });

  final VideoTrack videoTrack;
  final String trackId;
  final void Function(String trackId) disposeRender;
  final rtc.RTCVideoRenderer cacheRenderer;

  @override
  State<VideoTrackWidget> createState() => _VideoTrackWidgetState();
}

class _VideoTrackWidgetState extends State<VideoTrackWidget> {
  @override
  Widget build(BuildContext context) {
    return AbsorbPointer(
      absorbing: true,
      child: VideoTrackRenderer(
        widget.videoTrack,
        fit: rtc.RTCVideoViewObjectFit.RTCVideoViewObjectFitCover,
        renderMode: VideoRenderMode.texture,
        autoDisposeRenderer: false,
        cachedRenderer: widget.cacheRenderer,
      ),
    );
  }

  @override
  void dispose() {
    widget.disposeRender(widget.trackId);
    super.dispose();
  }
}
