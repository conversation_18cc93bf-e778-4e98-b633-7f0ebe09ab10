import 'package:livekit_client/livekit_client.dart';

class ParticipantTrack {
  final String id;
  final Participant participant;
  final TrackPublication? videoTrackPublication;
  final String userId;
  final String? avatarUrl;
  final String displayName;
  final bool isMe;
  final bool isScreenShare;

  ParticipantTrack({
    required this.id,
    required this.participant,
    required this.userId,
    required this.displayName,
    this.videoTrackPublication,
    this.isScreenShare = false,
    this.isMe = false,
    this.avatarUrl,
  });
}
