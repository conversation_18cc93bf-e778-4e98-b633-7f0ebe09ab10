import 'package:injectable/injectable.dart';
import 'package:livekit_client/livekit_client.dart';
import 'package:shared/shared.dart';

const _defaultRoomOptions = RoomOptions(
  adaptiveStream: true,
  dynacast: true,
  defaultAudioOutputOptions: const AudioOutputOptions(speakerOn: true),
);

@Injectable()
class ConnectRoomUseCase
    extends BaseFutureUseCase<ConnectRoomInput, ConnectRoomOutput> {
  ConnectRoomUseCase();

  @override
  Future<ConnectRoomOutput> buildUseCase(ConnectRoomInput input) async {
    try {
      final room = Room(roomOptions: input.roomOptions);
      await room.connect(input.url, input.token);
      return ConnectRoomOutput(ok: true, room: room);
    } catch (error) {
      Log.e(name: 'ConnectRoomUseCase.buildUseCase', error);
      return ConnectRoomOutput(ok: false, error: error);
    }
  }
}

class ConnectRoomInput extends BaseInput {
  ConnectRoomInput({
    required this.url,
    required this.token,
    this.roomOptions = _defaultRoomOptions,
  });

  final String url;
  final String token;
  final RoomOptions roomOptions;
}

class ConnectRoomOutput extends BaseOutput {
  ConnectRoomOutput({
    required this.ok,
    this.room,
    this.error,
  }) : assert(ok ? room != null : error != null);

  final bool ok;
  final Object? error;
  final Room? room;
}
