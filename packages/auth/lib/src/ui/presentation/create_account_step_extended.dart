import 'package:ziichat_ui/ziichat_ui.dart';

class AuthenticationStepExtended extends AuthenticationStep {
  final AuthStepId id;
  final String? boldContent;

  AuthenticationStepExtended({
    required String stepContent,
    required StepStatus stepStatus,
    this.boldContent,
    required this.id,
  }) : super(
          stepContent: stepContent,
          stepStatus: stepStatus,
        );

  AuthenticationStepExtended copyWith({
    Duration? duration,
    String? stepContent,
    StepStatus? stepStatus,
    String? boldContent,
    AuthStepId? id,
    String? userName,
    AuthStepId? authStepId,
  }) {
    return AuthenticationStepExtended(
      id: authStepId ?? this.id,
      boldContent: boldContent ?? this.boldContent,
      stepContent: stepContent ?? this.stepContent,
      stepStatus: stepStatus ?? this.stepStatus,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'authStepId': id.toString(),
      'stepContent': stepContent,
      'stepStatus': stepStatus.toString(),
    };
  }
}

enum AuthStepId {
  usingUsername,
  loginAuthenticationYourAccount,
  loginGeneratingAnAuthenticationQRCode,
  registerSecuringYourAccountWithPhoneAuthentication,
  loginVerifyingFromAnotherDevice,
  registerProcessingRegisterRequest,
  loginProcessingLoginRequest,
  finnish,
}
