import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:auth_api/auth_api.dart';
import 'package:bloc/bloc.dart';
import 'package:crypto/crypto.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../auth.dart';
import '../../../domain/usecase/account_deletion_code/confirm_view_security_key_use_case.dart';
import '../../../domain/usecase/account_deletion_code/generate_security_key_use_case.dart';
import '../../../domain/usecase/account_deletion_code/initiate_generate_security_key_use_case.dart';
import '../../../domain/usecase/account_deletion_code/initiate_view_security_key_use_case.dart';
import '../../../domain/usecase/improving_passkey/check_migrate_passkey_status_use_case.dart';

part 'security_key_bloc.freezed.dart';
part 'security_key_event.dart';
part 'security_key_state.dart';

@injectable
class SecurityKeyBloc extends BaseBloc<SecurityKeyEvent, SecurityKeyState> {
  SecurityKeyBloc(
    this._initiateGenerateSecurityKeyUseCase,
    this._initiateViewSecurityKeyUseCase,
    this._generateSecurityKeyUseCase,
    this._confirmViewSecurityKeyUseCase,
    this._getCredentialUseCase,
    this._checkMigratePasskeyStatusUseCase,
  ) : super(SecurityKeyState.initial()) {
    on<GenerateSecurityKeyRequested>(_handleGenerateSecurityKeyFlow);
    on<ViewSecurityKeyRequested>(_handleViewSecurityKeyFlow);
    on<ResetSecurityKeyRequested>(_handleResetSecurityKeyState);
  }

  final InitiateGenerateSecurityKeyUseCase _initiateGenerateSecurityKeyUseCase;
  final InitiateViewSecurityKeyUseCase _initiateViewSecurityKeyUseCase;
  final GenerateSecurityKeyUseCase _generateSecurityKeyUseCase;
  final ConfirmViewSecurityKeyUseCase _confirmViewSecurityKeyUseCase;
  final GetCredentialUseCase _getCredentialUseCase;
  final CheckMigratePasskeyStatusUseCase _checkMigratePasskeyStatusUseCase;
  late String _reqChallenge = RandomUtils.generateRandomString(16);

  Future<void> _handleGenerateSecurityKeyFlow(
    GenerateSecurityKeyRequested event,
    Emitter<SecurityKeyState> emit,
  ) async {
    emit(SecurityKeyState.generateSecurityKeyLoading(true));

    final reqChallengeHash =
        sha256.convert(utf8.encode(_reqChallenge)).toString();

    final initOutput = await _initiateGenerateSecurityKeyUseCase.execute(
      InitiateGenerateSecurityKeyInput(reqChallengeHash: reqChallengeHash),
    );

    if (initOutput.error != null) {
      emit(SecurityKeyState.generateSecurityKeyFailure(initOutput.error!));
      return;
    }

    bool migrated = await _checkMigrationStatus();

    final credentialRequestOptions =
        initOutput.response?.data?.credentialRequestOptions;
    final reqId = initOutput.response?.data?.reqId;

    if (credentialRequestOptions == null || reqId == null) {
      emit(
        SecurityKeyState.generateSecurityKeyFailure(
          'Missing required data from initiate generate security key response.',
        ),
      );
      return;
    }

    final getCredentialOutput = await _getCredentialUseCase.execute(
      GetCredentialInput(
        credentialRequestOptions: credentialRequestOptions,
        reqId: reqId,
        reqVerifier: _reqChallenge,
        passkeyMigrated: migrated,
      ),
    );

    if (getCredentialOutput.exception != null) {
      if (getCredentialOutput.exception is AppPasskeyAuthCancelledException ||
          getCredentialOutput.exception is AppUnhandledAuthenticatorException) {
        emit(SecurityKeyState.generateSecurityKeyLoading(false));

        return;
      }
      emit(
        SecurityKeyState.generateSecurityKeyFailure(
          getCredentialOutput.exception.toString(),
        ),
      );
      return;
    }

    if (!getCredentialOutput.ok) {
      return;
    }

    final generateOutput = await _generateSecurityKeyUseCase.execute(
      GenerateSecurityKeyInput(
        request: (V3GenerateSecurityKeyRequestBuilder()
              ..reqId = getCredentialOutput.reqId
              ..reqVerifier = getCredentialOutput.reqVerifier
              ..assertion = getCredentialOutput.assertion!.toBuilder())
            .build(),
      ),
    );
    if (generateOutput.error != null) {
      emit(SecurityKeyState.generateSecurityKeyFailure(generateOutput.error!));
      return;
    }

    if (generateOutput.ok) {
      emit(
        SecurityKeyState.generateSecurityKeySuccess(
          generateOutput.securityKey,
        ),
      );
      return;
    }
    emit(SecurityKeyState.generateSecurityKeyFailure());
  }

  Future<void> _handleViewSecurityKeyFlow(
    ViewSecurityKeyRequested event,
    Emitter<SecurityKeyState> emit,
  ) async {
    final reqChallengeHash =
        sha256.convert(utf8.encode(_reqChallenge)).toString();

    final initOutput = await _initiateViewSecurityKeyUseCase.execute(
      InitiateViewSecurityKeyInput(reqChallengeHash: reqChallengeHash),
    );

    if (initOutput.error != null) {
      emit(SecurityKeyState.viewSecurityKeyFailure(initOutput.error!));
      return;
    }

    bool migrated = await _checkMigrationStatus();

    final credentialRequestOptions =
        initOutput.response?.data?.credentialRequestOptions;
    final reqId = initOutput.response?.data?.reqId;

    if (credentialRequestOptions == null || reqId == null) {
      emit(
        SecurityKeyState.generateSecurityKeyFailure(
          'Missing required data from initiate generate security key response.',
        ),
      );
      return;
    }

    final getCredentialOutput = await _getCredentialUseCase.execute(
      GetCredentialInput(
        credentialRequestOptions: credentialRequestOptions,
        reqId: reqId,
        reqVerifier: _reqChallenge,
        passkeyMigrated: migrated,
      ),
    );

    if (getCredentialOutput.exception != null) {
      if (getCredentialOutput.exception is AppPasskeyAuthCancelledException ||
          getCredentialOutput.exception is AppUnhandledAuthenticatorException) {
        return;
      }
      emit(
        SecurityKeyState.viewSecurityKeyFailure(
          getCredentialOutput.exception.toString(),
        ),
      );
      return;
    }

    if (!getCredentialOutput.ok) return;

    final confirmOutput = await _confirmViewSecurityKeyUseCase.execute(
      ConfirmViewSecurityKeyInput(
        request: (V3ConfirmViewSecurityKeyRequestBuilder()
              ..reqId = getCredentialOutput.reqId
              ..reqVerifier = getCredentialOutput.reqVerifier
              ..assertion = getCredentialOutput.assertion!.toBuilder())
            .build(),
      ),
    );

    if (confirmOutput.error != null) {
      emit(SecurityKeyState.viewSecurityKeyFailure(confirmOutput.error!));
      return;
    }

    if (confirmOutput.ok) {
      emit(
        SecurityKeyState.viewSecurityKeySuccess(
          confirmOutput.securityKey!,
        ),
      );
      return;
    }

    emit(SecurityKeyState.viewSecurityKeyFailure());
  }

  Future<bool> _checkMigrationStatus() async {
    if (Platform.isAndroid) return true;

    final checkMigratedOutput = await _checkMigratePasskeyStatusUseCase.execute(
      CheckMigratePasskeyStatusInput(),
    );
    return checkMigratedOutput.ok;
  }

  FutureOr<void> _handleResetSecurityKeyState(
    ResetSecurityKeyRequested event,
    Emitter<SecurityKeyState> emit,
  ) {
    emit(SecurityKeyState.initial());
  }
}
