part of 'auth_bloc.dart';

@freezed
sealed class AuthState extends BaseBlocState with _$AuthState {
  const AuthState._();
  factory AuthState.initial() = Initial;

  factory AuthState.loginQRRequest() = LoginQRRequest;

  factory AuthState.loginQRGenerated(String strQR) = LoginQRGenerated;

  factory AuthState.loginQRCanceled() = LoginQRCanceled;

  factory AuthState.reGenerateQRLogin() = ReGenerateQRLogin;

  factory AuthState.onProcessRegisterRequest() = ProcessRegisterRequest;

  factory AuthState.onProcessLoginRequest() = ProcessLoginRequest;

  factory AuthState.retryStep(AuthStepId stepId) = RetryStep;

  factory AuthState.error(String error) = Error;

  factory AuthState.success(String sessionKey, String sessionToken) = Success;

  factory AuthState.waiting() = Waiting;

  factory AuthState.usernameInvalid() = UsernameInvalid;

  factory AuthState.usernameRequestByOtherUser() = UsernameRequestByOtherUser;

  factory AuthState.usernameDeleted() = AuthStateUsernameDeleted;
}

extension AuthStateX on AuthState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function()? loginQRRequest,
    T Function(String strQR)? loginQRGenerated,
    T Function()? loginQRCanceled,
    T Function()? reGenerateQRLogin,
    T Function()? onProcessRegisterRequest,
    T Function()? onProcessLoginRequest,
    T Function(AuthStepId stepId)? retryStep,
    T Function(String error)? error,
    T Function(String sessionKey, String sessionToken)? success,
    T Function()? waiting,
    T Function()? usernameInvalid,
    T Function()? usernameRequestByOtherUser,
    T Function()? usernameDeleted,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is Initial && initial != null) return initial();
    if (state is LoginQRRequest && loginQRRequest != null)
      return loginQRRequest();
    if (state is LoginQRGenerated && loginQRGenerated != null) {
      return loginQRGenerated(state.strQR);
    }
    if (state is LoginQRCanceled && loginQRCanceled != null)
      return loginQRCanceled();
    if (state is ReGenerateQRLogin && reGenerateQRLogin != null)
      return reGenerateQRLogin();
    if (state is ProcessRegisterRequest && onProcessRegisterRequest != null) {
      return onProcessRegisterRequest();
    }
    if (state is ProcessLoginRequest && onProcessLoginRequest != null) {
      return onProcessLoginRequest();
    }
    if (state is RetryStep && retryStep != null) return retryStep(state.stepId);
    if (state is Error && error != null) return error(state.error);
    if (state is Success && success != null) {
      return success(state.sessionKey, state.sessionToken);
    }
    if (state is Waiting && waiting != null) return waiting();
    if (state is UsernameInvalid && usernameInvalid != null)
      return usernameInvalid();
    if (state is UsernameRequestByOtherUser &&
        usernameRequestByOtherUser != null) {
      return usernameRequestByOtherUser();
    }
    if (state is AuthStateUsernameDeleted && usernameDeleted != null)
      return usernameDeleted();

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function() loginQRRequest,
    required T Function(String strQR) loginQRGenerated,
    required T Function() loginQRCanceled,
    required T Function() reGenerateQRLogin,
    required T Function() onProcessRegisterRequest,
    required T Function() onProcessLoginRequest,
    required T Function(AuthStepId stepId) retryStep,
    required T Function(String error) error,
    required T Function(String sessionKey, String sessionToken) success,
    required T Function() waiting,
    required T Function() usernameInvalid,
    required T Function() usernameRequestByOtherUser,
    required T Function() usernameDeleted,
  }) {
    final state = this;

    if (state is Initial) return initial();
    if (state is LoginQRRequest) return loginQRRequest();
    if (state is LoginQRGenerated) return loginQRGenerated(state.strQR);
    if (state is LoginQRCanceled) return loginQRCanceled();
    if (state is ReGenerateQRLogin) return reGenerateQRLogin();
    if (state is ProcessRegisterRequest) return onProcessRegisterRequest();
    if (state is ProcessLoginRequest) return onProcessLoginRequest();
    if (state is RetryStep) return retryStep(state.stepId);
    if (state is Error) return error(state.error);
    if (state is Success) return success(state.sessionKey, state.sessionToken);
    if (state is Waiting) return waiting();
    if (state is UsernameInvalid) return usernameInvalid();
    if (state is UsernameRequestByOtherUser)
      return usernameRequestByOtherUser();
    if (state is AuthStateUsernameDeleted) return usernameDeleted();

    throw StateError('Unhandled state: $state');
  }
}
