part of 'auth_bloc.dart';

sealed class AuthEvent extends BaseBlocEvent {
  const AuthEvent();
}

@freezed
sealed class InitiateAuthPageEvent extends AuthEvent
    with _$InitiateAuthPageEvent {
  const InitiateAuthPageEvent._();

  factory InitiateAuthPageEvent() = _InitiateAuthPageEvent;
}

@freezed
sealed class InitAuthProgressPageEvent extends AuthEvent
    with _$InitAuthProgressPageEvent {
  const InitAuthProgressPageEvent._();

  factory InitAuthProgressPageEvent({
    required String username,
    required String reqChallenge,
    required Object request,
    required Object hashCash,
  }) = _InitAuthProgressPageEvent;
}

@freezed
sealed class InitiateUserKeyEvent extends AuthEvent
    with _$InitiateUserKeyEvent {
  const InitiateUserKeyEvent._();

  factory InitiateUserKeyEvent(String username) = _InitiateUserKey;
}

@freezed
sealed class LoginEvent extends AuthEvent with _$LoginEvent {
  const LoginEvent._();

  factory LoginEvent({
    required V3LoginRequestUserKey loginReq,
    required String username,
  }) = _LoginEvent;
}

@freezed
sealed class RegisterEvent extends AuthEvent with _$RegisterEvent {
  const RegisterEvent._();

  factory RegisterEvent({required V3RegisterRequestUserKey registerReq}) =
      _RegisterEvent;
}

@freezed
sealed class CancelQRLoginFlowEvent extends AuthEvent
    with _$CancelQRLoginFlowEvent {
  const CancelQRLoginFlowEvent._();

  factory CancelQRLoginFlowEvent() = _CancelQRLoginFlowEvent;
}

@freezed
sealed class RetryAuthEvent extends AuthEvent with _$RetryAuthEvent {
  const RetryAuthEvent._();

  factory RetryAuthEvent({required AuthStepId stepId}) = _RetryAuthEvent;
}

@freezed
sealed class QRLoginRequestVerifiedEvent extends AuthEvent
    with _$QRLoginRequestVerifiedEvent {
  const QRLoginRequestVerifiedEvent._();

  factory QRLoginRequestVerifiedEvent() = _QRLoginRequestVerifiedEvent;
}

@freezed
sealed class AuthOnUsernameInvalidEvent extends AuthEvent
    with _$AuthOnUsernameInvalidEvent {
  const AuthOnUsernameInvalidEvent._();

  factory AuthOnUsernameInvalidEvent({
    required int code,
    required String message,
  }) = _AuthOnUsernameInvalidEvent;
}

@freezed
sealed class OnAuthErrorEvent extends AuthEvent with _$OnAuthErrorEvent {
  const OnAuthErrorEvent._();

  factory OnAuthErrorEvent({required String errorMessage}) = _OnAuthErrorEvent;
}

@freezed
sealed class AuthEventConfirmCancelRegisterEvent extends AuthEvent
    with _$AuthEventConfirmCancelRegisterEvent {
  const AuthEventConfirmCancelRegisterEvent._();

  factory AuthEventConfirmCancelRegisterEvent() =
      _AuthEventConfirmCancelRegisterEvent;
}
