import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:auth_api/auth_api.dart';
import 'package:bloc/src/bloc.dart';
import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:passkeys/exceptions.dart';
import 'package:shared/shared.dart';

import '../../../../auth.dart';
import '../../../data/repositories/database/entities/session_local_metadata.dart';
import '../../../data/repositories/source/api/hash_cash/hash_cash.dart';
import '../../../domain/usecase/hash_cash_use_case.dart';
import '../../../domain/usecase/login/login_use_case.dart';
import '../../../domain/usecase/login_qr/get_qr_auth_state_use_case.dart';
import '../../../domain/usecase/login_qr/initiate_qr_auth_flow_use_case.dart';
import '../../../domain/usecase/login_qr/login_with_qr_auth_code_use_case.dart';
import '../../../domain/usecase/register/cancel_register_use_case.dart';
import '../../../domain/usecase/register/register_use_case.dart';
import '../../presentation/create_account_step_extended.dart';

part 'auth_bloc.freezed.dart';
part 'auth_event.dart';
part 'auth_state.dart';

@injectable
class AuthBloc extends BaseBloc<AuthEvent, AuthState> {
  AuthBloc(
    this._createCredentialUseCase,
    this._getCredentialUseCase,
    this._initiateQRAuthFlow,
    this._getQRAuthStateUseCase,
    this._loginWithQRAuthCodeUseCase,
    this._initiateUserKeyAuthFlowUseCase,
    this._hashCashUseCase,
    this._registerUseCase,
    this._loginUseCase,
    this._cancelRegisterUseCase,
    this._sessionRepository,
  ) : super(AuthState.initial()) {
    on<InitAuthProgressPageEvent>(_onInitAuthProgressPageEvent);

    on<RegisterEvent>(_onRegister);

    on<LoginEvent>(_onLogin);

    on<CancelQRLoginFlowEvent>(_onCancelQRLoginFlow);

    on<RetryAuthEvent>(_onRetryAuth);

    on<QRLoginRequestVerifiedEvent>(_loginWithQRAuthCode);

    on<AuthOnUsernameInvalidEvent>(_onUsernameInvalid);

    on<OnAuthErrorEvent>(_onAuthError);

    on<AuthEventConfirmCancelRegisterEvent>(_onConfirmCancelRegister);
  }

  final CreateCredentialUseCase _createCredentialUseCase;
  final GetCredentialUseCase _getCredentialUseCase;
  final InitiateQRAuthFlowUseCase _initiateQRAuthFlow;
  final GetQRAuthStateUseCase _getQRAuthStateUseCase;
  final LoginWithQRAuthCodeUseCase _loginWithQRAuthCodeUseCase;
  final InitiateUserKeyAuthFlowUseCase _initiateUserKeyAuthFlowUseCase;
  final HashCashUseCase _hashCashUseCase;
  final LoginUseCase _loginUseCase;
  final RegisterUseCase _registerUseCase;
  final CancelRegisterUseCase _cancelRegisterUseCase;
  final SessionRepository _sessionRepository;
  late HashCash? _hashCash;
  late V3RegisterRequestUserKey? _registerRequest = null;
  late V3LoginRequestUserKey? _loginRequest = null;
  late String _reqChallenge = RandomUtils.generateRandomString(16);
  late String _username;
  late String _qrRequestId = '';
  late String _qrRequestVerifier = '';
  late bool _verifyLoginQRCanceled = false;

  void _saveSession({
    required V3SessionData? sessionData,
    required bool isLogin,
    required bool isLoginQR,
  }) {
    Session session = Session(
      sessionKey: sessionData!.userId ?? '',
      sessionId: sessionData.sessionId ?? '',
      sessionToken: sessionData.sessionToken ?? '',
      active: true,
      isLogin: isLogin,
      isLoginQR: isLoginQR,
    );
    _sessionRepository.insert(session);

    _sessionRepository.insertMetadata(
      SessionLocalMetadata(
        sessionKey: session.sessionKey,
      ),
    );
  }

  FutureOr<void> _onRegister(
    RegisterEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      _registerRequest = event.registerReq;
      final createCredentialOutput = await _createCredentialUseCase.execute(
        CreateCredentialInput(
          registerRequest: _registerRequest!,
          reqChallenge: _reqChallenge,
          hashCash: _hashCash!,
        ),
      );

      if (createCredentialOutput.registerRequest == null) {
        return;
      }

      emit(AuthState.onProcessRegisterRequest());

      final registerOutput = await _registerUseCase.execute(
        RegisterInput(
          registerRequest: createCredentialOutput.registerRequest!,
          hashCash: _hashCash!,
        ),
      );

      if (!registerOutput.response!.ok!) {
        add(
          OnAuthErrorEvent(
            errorMessage: registerOutput.response!.error!.message!,
          ),
        );
        _logToFirebaseCrashlytics(registerOutput.response!.error, event: event);
        return;
      }

      final V3SessionData? sessionData = registerOutput.response?.data;

      if (sessionData == null ||
          sessionData.userId!.isEmpty ||
          sessionData.sessionToken!.isEmpty) {
        add(OnAuthErrorEvent(errorMessage: 'ErrorWhenProcessRegisterRequest'));
        return;
      }

      _saveSession(
        sessionData: sessionData,
        isLogin: true,
        isLoginQR: false,
      );

      emit(
        AuthState.success(
          sessionData.userId ?? '',
          sessionData.sessionToken ?? '',
        ),
      );
    } on PasskeyAuthCancelledException {
      add(OnAuthErrorEvent(errorMessage: 'PasskeyAuthCancelledException'));
    } on UnhandledAuthenticatorException catch (e) {
      add(OnAuthErrorEvent(errorMessage: 'UnhandledAuthenticatorException'));
      _logToFirebaseCrashlytics(e, event: event);
    } on SyncAccountNotAvailableException catch (e) {
      add(OnAuthErrorEvent(errorMessage: 'SyncAccountNotAvailableException'));
      _logToFirebaseCrashlytics(e, event: event);
    } on AppUncaughtException catch (e) {
      if (e.rootError is DioException) {
        final rootEx = e.rootError as DioException;
        if (rootEx.response?.statusCode == 400) {
          _handleOkFalse(rootEx);
          return;
        }
      }
      add(OnAuthErrorEvent(errorMessage: e.toString()));
      _logToFirebaseCrashlytics(e, event: event);
    } catch (e) {
      add(OnAuthErrorEvent(errorMessage: e.toString()));
      _logToFirebaseCrashlytics(e, event: event);
    }
  }

  FutureOr<void> _onLogin(LoginEvent event, Emitter<AuthState> emit) async {
    try {
      _loginRequest = event.loginReq;

      final getCredentialOutput = await _getCredentialUseCase.execute(
        GetCredentialInput(
          credentialRequestOptions: event.loginReq.credentialRequestOptions!,
          reqId: event.loginReq.reqId!,
          reqVerifier: _reqChallenge,
          passkeyMigrated: _loginRequest!.passkeyMigrated ?? true,
        ),
      );

      if (getCredentialOutput.exception != null) {
        final e = getCredentialOutput.exception;

        if (e is AppNoCredentialsAvailableException) {
          emit(AuthState.loginQRRequest());
          await _initLoginQRFlow(emit);
          return;
        }

        if (e is AppPasskeyAuthCancelledException && Platform.isIOS) {
          if (e.isNoKeyFound()) {
            emit(AuthState.loginQRRequest());
            await _initLoginQRFlow(emit);
            return;
          }

          add(OnAuthErrorEvent(errorMessage: e.toString()));
          return;
        }

        add(OnAuthErrorEvent(errorMessage: e.toString()));
      }

      if (!getCredentialOutput.ok) return;

      emit(AuthState.onProcessLoginRequest());

      final loginOutput = await _loginUseCase.execute(
        LoginInput(
          loginRequest: (V3LoginWithUserKeyRequestBuilder()
                ..reqId = getCredentialOutput.reqId
                ..reqVerifier = getCredentialOutput.reqVerifier
                ..assertion = getCredentialOutput.assertion!.toBuilder())
              .build(),
        ),
      );

      if (!loginOutput.response!.ok!) {
        add(
          OnAuthErrorEvent(
            errorMessage: loginOutput.response!.error!.message!,
          ),
        );
        _logToFirebaseCrashlytics(loginOutput.response!.error, event: event);
        return;
      }

      final sessionData = loginOutput.response?.data;

      _saveSession(
        sessionData: sessionData,
        isLogin: true,
        isLoginQR: false,
      );

      emit(
        AuthState.success(
          sessionData?.userId ?? '',
          sessionData?.sessionToken ?? '',
        ),
      );
    } on AppUncaughtException catch (e) {
      if (e.rootError is DioException) {
        final rootEx = e.rootError as DioException;
        if (rootEx.response?.statusCode == 400) {
          _handleOkFalse(rootEx);
          return;
        }
      }
      add(OnAuthErrorEvent(errorMessage: e.toString()));
      _logToFirebaseCrashlytics(e, event: event);
    } catch (e) {
      add(OnAuthErrorEvent(errorMessage: e.toString()));
      _logToFirebaseCrashlytics(e, event: event);
    }
  }

  void _logToFirebaseCrashlytics(dynamic exception, {Object? event}) {
    String? username;
    String? errorReason;
    if (event is LoginEvent) {
      errorReason = 'Login error';
      username = event.username;
    }
    if (event is RegisterEvent) {
      errorReason = 'Register error';
      username = event.registerReq.credentialCreationOptions?.user?.name;
    }
    FirebaseCrashlytics.instance.recordError(
      exception,
      null,
      reason: errorReason,
      information: [
        'Username: $username',
        'Event: $event',
      ],
    );
  }

  FutureOr<void> _onInitAuthProgressPageEvent(
    InitAuthProgressPageEvent event,
    Emitter<AuthState> emit,
  ) {
    _reqChallenge = event.reqChallenge;
    _hashCash = event.hashCash as HashCash;
    _username = event.username;

    if (event.request is V3LoginRequestUserKey) {
      add(
        LoginEvent(
          loginReq: event.request as V3LoginRequestUserKey,
          username: _username,
        ),
      );
    } else if (event.request is V3RegisterRequestUserKey) {
      add(
        RegisterEvent(registerReq: event.request as V3RegisterRequestUserKey),
      );
    }
  }

  FutureOr<void> _initLoginQRFlow(
    Emitter<AuthState> emit,
  ) async {
    try {
      _reqChallenge = RandomUtils.generateRandomString(16);

      final hashCashOutput = await _hashCashUseCase.execute(HashCashInput());

      _hashCash = hashCashOutput.hashCash;

      final initQROutput = await _initiateQRAuthFlow.execute(
        InitiateQRAuthFlowUseCaseInput(
          requestId: _loginRequest!.reqId!,
          hashCash: _hashCash!,
        ),
      );

      if (!initQROutput.response.ok!) {
        add(
          OnAuthErrorEvent(
            errorMessage: initQROutput.response.error!.message!,
          ),
        );
        return;
      }

      final data = initQROutput.response.data!;

      _qrRequestId = data.reqId!;
      _qrRequestVerifier = _hashCash!.hash;

      emit(
        AuthState.loginQRGenerated(
          initQROutput.response.data!.qrAuthCodeRequestOptions!.qrAuthCode!,
        ),
      );

      _verifyLoginQRCanceled = false;

      _verifyLoginQR(emit);
    } on AppUncaughtException catch (e) {
      if (e.rootError is DioException) {
        final rootEx = e.rootError as DioException;
        if (rootEx.response?.statusCode == 400) {
          _handleOkFalse(rootEx);
          return;
        }
      }
      add(OnAuthErrorEvent(errorMessage: e.toString()));
    } catch (e) {
      add(OnAuthErrorEvent(errorMessage: e.toString()));
    }
  }

  void _verifyLoginQR(Emitter<AuthState> emit) async {
    try {
      final stateOutput = await _getQRAuthStateUseCase.execute(
        GetQRAuthStateUseCaseInput(
          requestId: _qrRequestId,
        ),
      );

      Log.e(name: '_verifyLoginQR', stateOutput.response);

      if (stateOutput.response == null) {
        add(OnAuthErrorEvent(errorMessage: 'Error when verify qr code'));
        return;
      }

      if (_verifyLoginQRCanceled) return;

      if (stateOutput.response!.ok == false) {
        add(OnAuthErrorEvent(errorMessage: 'Error when verify qr code'));
        return;
      }

      if (stateOutput.response!.data == V3LoginRequestState.PENDING) {
        Future.delayed(DurationUtils.ms2000, () {
          if (_verifyLoginQRCanceled) return;

          _verifyLoginQR(emit);
        });
        return;
      }

      if (stateOutput.response!.data == V3LoginRequestState.FAILED) {
        add(OnAuthErrorEvent(errorMessage: 'Error when verify qr code'));
        return;
      }

      if (stateOutput.response!.data == V3LoginRequestState.COMPLETED) {
        add(OnAuthErrorEvent(errorMessage: 'Error when verify qr code'));
        return;
      }

      if (stateOutput.response!.data == V3LoginRequestState.VERIFIED) {
        add(QRLoginRequestVerifiedEvent());
        return;
      }
    } on AppUncaughtException catch (e) {
      if (e.rootError is DioException) {
        final rootEx = e.rootError as DioException;
        if (rootEx.response?.statusCode == 400) {
          _handleOkFalse(rootEx);
          return;
        }
      }
      add(OnAuthErrorEvent(errorMessage: e.toString()));
    } catch (e) {
      add(OnAuthErrorEvent(errorMessage: e.toString()));
    }
  }

  Future<void> _loginWithQRAuthCode(
    QRLoginRequestVerifiedEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthState.onProcessLoginRequest());

    final stateOutput = await _loginWithQRAuthCodeUseCase.execute(
      LoginWithQRAuthCodeUseCaseInput(
        requestId: _qrRequestId,
        reqVerifier: _qrRequestVerifier,
      ),
    );

    if (!stateOutput.response.ok!) {
      add(OnAuthErrorEvent(errorMessage: stateOutput.response.error!.message!));
      return;
    }

    final sessionData = stateOutput.response.data;

    if (sessionData == null ||
        sessionData.userId!.isEmpty ||
        sessionData.sessionToken!.isEmpty) return;

    _saveSession(
      sessionData: sessionData,
      isLogin: true,
      isLoginQR: true,
    );

    emit(
      AuthState.success(
        sessionData.userId ?? '',
        sessionData.sessionToken ?? '',
      ),
    );
  }

  FutureOr<void> _onCancelQRLoginFlow(
    CancelQRLoginFlowEvent event,
    Emitter<AuthState> emit,
  ) {
    _verifyLoginQRCanceled = true;
    emit(
      AuthState.loginQRCanceled(),
    );
  }

  FutureOr<void> _onRetryAuth(RetryAuthEvent event, Emitter<AuthState> emit) {
    emit(AuthState.retryStep(event.stepId));
    switch (event.stepId) {
      case AuthStepId.registerSecuringYourAccountWithPhoneAuthentication:
        _handleRetryRegisterStep(emit);
      case AuthStepId.registerProcessingRegisterRequest:
        _handleRetryRegisterStep(emit);
      case AuthStepId.loginAuthenticationYourAccount:
        _handleRetryLoginStep(emit);
      case AuthStepId.loginGeneratingAnAuthenticationQRCode:
        _handleRetryLoginGeneratingAnAuthenticationQRCodeStep(emit);
      case AuthStepId.loginVerifyingFromAnotherDevice:
        _handleRetryLoginVerifyingFromAnotherDeviceStep(emit);
      case AuthStepId.loginProcessingLoginRequest:
        _handleRetryLoginStep(emit);
      default:
        {
          Log.e(
            name: '_onRetryAuth',
            'No need to try again step ${event.stepId}',
          );
        }
    }
  }

  /// Handle retry when fail step register
  ///
  /// try hashCash -> initiateUserKeyAuthFlow -> register
  void _handleRetryRegisterStep(
    Emitter<AuthState> emit,
  ) async {
    try {
      final reqChallengeHash =
          sha256.convert(utf8.encode(_reqChallenge)).toString();

      final hashCashOutput = await _hashCashUseCase.execute(HashCashInput());

      _hashCash = hashCashOutput.hashCash;

      final initiateOutput = await _initiateUserKeyAuthFlowUseCase.execute(
        InitiateUserKeyAuthFlowInput(
          username: _username,
          reqChallengeHash: reqChallengeHash,
          hashCash: _hashCash!,
        ),
      );

      if (!initiateOutput.response.ok!) {
        add(
          AuthOnUsernameInvalidEvent(
            code: initiateOutput.response.error!.code!,
            message: initiateOutput.response.error!.message!,
          ),
        );
        return;
      }

      add(RegisterEvent(registerReq: initiateOutput.response.registerRequest!));
    } catch (e) {
      add(OnAuthErrorEvent(errorMessage: e.toString()));
    }
  }

  void _handleRetryLoginStep(
    Emitter<AuthState> emit,
  ) async {
    try {
      final reqChallengeHash =
          sha256.convert(utf8.encode(_reqChallenge)).toString();

      final hashCashOutput = await _hashCashUseCase.execute(HashCashInput());

      _hashCash = hashCashOutput.hashCash;

      final initiateOutput = await _initiateUserKeyAuthFlowUseCase.execute(
        InitiateUserKeyAuthFlowInput(
          username: _username,
          reqChallengeHash: reqChallengeHash,
          hashCash: _hashCash!,
        ),
      );

      add(
        LoginEvent(
          loginReq: initiateOutput.response.loginRequest!,
          username: _username,
        ),
      );
    } catch (e) {
      add(OnAuthErrorEvent(errorMessage: e.toString()));
    }
  }

  void _handleRetryLoginGeneratingAnAuthenticationQRCodeStep(
    Emitter<AuthState> emit,
  ) async {
    await _initLoginQRFlow(emit);
  }

  void _handleRetryLoginVerifyingFromAnotherDeviceStep(
    Emitter<AuthState> emit,
  ) async {
    _verifyLoginQRCanceled = false;
    _handleRetryLoginStep(emit);
  }

  FutureOr<void> _onUsernameInvalid(
    AuthOnUsernameInvalidEvent event,
    Emitter<AuthState> emit,
  ) {
    switch (event.code) {
      case 2046:
        emit(AuthState.usernameInvalid());
        break;
      case 2053:
        emit(AuthState.usernameRequestByOtherUser());
        break;
      case 7100:
        emit(AuthState.usernameDeleted());
        break;
      default:
        add(OnAuthErrorEvent(errorMessage: event.message));
    }
  }

  FutureOr<void> _onAuthError(OnAuthErrorEvent event, Emitter<AuthState> emit) {
    emit(AuthState.error(event.errorMessage));
  }

  FutureOr<void> _onConfirmCancelRegister(
    AuthEventConfirmCancelRegisterEvent event,
    Emitter<AuthState> emit,
  ) async {
    await _cancelRegisterUseCase.execute(
      CancelRegisterInput(
        requestId: _registerRequest!.reqId!,
        userKey: _username,
      ),
    );
  }

  void _handleOkFalse(DioException dioException) {
    final responseData = dioException.response?.data;
    if (responseData is Map<String, dynamic>) {
      final responseError = ResponseError.fromJson(responseData['error']);
      add(OnAuthErrorEvent(errorMessage: responseError.message ?? ''));
    }
  }
}
