part of 'initial_user_key_auth_bloc.dart';

@freezed
sealed class InitialUserKeyAuthState extends BaseBlocState
    with _$InitialUserKeyAuthState {
  const InitialUserKeyAuthState._();

  factory InitialUserKeyAuthState.initial() = InitialUserKeyAuthStateInitial;

  factory InitialUserKeyAuthState.loginRequest({
    required V3LoginRequestUserKey loginReq,
  }) = InitialUserKeyAuthStateLoginRequest;

  factory InitialUserKeyAuthState.loginRequestCanceled() =
      InitialUserKeyAuthStateLoginRequestCanceled;

  factory InitialUserKeyAuthState.registerRequest({
    required V3RegisterRequestUserKey registerReq,
  }) = InitialUserKeyAuthStateRegisterRequest;

  factory InitialUserKeyAuthState.registerRequestCanceled() =
      InitialUserKeyAuthStateRegisterRequestCanceled;

  factory InitialUserKeyAuthState.goToAuthProgressPage({
    required String username,
    required String reqChallenge,
    required Object request,
    required Object hashCash,
  }) = InitialUserKeyAuthStateGoToAuthProgressPage;

  factory InitialUserKeyAuthState.waiting() = InitialUserKeyAuthStateWaiting;

  factory InitialUserKeyAuthState.error(String error) =
      InitialUserKeyAuthStateError;

  factory InitialUserKeyAuthState.usernameInvalid() =
      InitialUserKeyAuthStateUsernameInvalid;

  factory InitialUserKeyAuthState.usernameRequestByOtherUser() =
      InitialUserKeyAuthStateUsernameRequestByOtherUser;

  factory InitialUserKeyAuthState.usernameDeleted() =
      InitialUserKeyAuthStateUsernameDeleted;

  factory InitialUserKeyAuthState.authSuccess({
    required String sessionKey,
    required String authToken,
  }) = InitialUserKeyAuthStateAuthSuccess;

  factory InitialUserKeyAuthState.authCanceled() =
      InitialUserKeyAuthStateAuthCanceled;

  factory InitialUserKeyAuthState.unknownError() =
      InitialUserKeyAuthStateUnknownError;
}

extension InitialUserKeyAuthStateX on InitialUserKeyAuthState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function(V3LoginRequestUserKey loginReq)? loginRequest,
    T Function()? loginRequestCanceled,
    T Function(V3RegisterRequestUserKey registerReq)? registerRequest,
    T Function()? registerRequestCanceled,
    T Function(
      String username,
      String reqChallenge,
      Object request,
      Object hashCash,
    )? goToAuthProgressPage,
    T Function()? waiting,
    T Function(String error)? error,
    T Function()? usernameInvalid,
    T Function()? usernameRequestByOtherUser,
    T Function()? usernameDeleted,
    T Function(String sessionKey, String authToken)? authSuccess,
    T Function()? authCanceled,
    T Function()? unknownError,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is InitialUserKeyAuthStateInitial && initial != null)
      return initial();
    if (state is InitialUserKeyAuthStateLoginRequest && loginRequest != null) {
      return loginRequest(state.loginReq);
    }
    if (state is InitialUserKeyAuthStateLoginRequestCanceled &&
        loginRequestCanceled != null) {
      return loginRequestCanceled();
    }
    if (state is InitialUserKeyAuthStateRegisterRequest &&
        registerRequest != null) {
      return registerRequest(state.registerReq);
    }
    if (state is InitialUserKeyAuthStateRegisterRequestCanceled &&
        registerRequestCanceled != null) {
      return registerRequestCanceled();
    }
    if (state is InitialUserKeyAuthStateGoToAuthProgressPage &&
        goToAuthProgressPage != null) {
      return goToAuthProgressPage(
        state.username,
        state.reqChallenge,
        state.request,
        state.hashCash,
      );
    }
    if (state is InitialUserKeyAuthStateWaiting && waiting != null)
      return waiting();
    if (state is InitialUserKeyAuthStateError && error != null)
      return error(state.error);
    if (state is InitialUserKeyAuthStateUsernameInvalid &&
        usernameInvalid != null) {
      return usernameInvalid();
    }
    if (state is InitialUserKeyAuthStateUsernameRequestByOtherUser &&
        usernameRequestByOtherUser != null) {
      return usernameRequestByOtherUser();
    }
    if (state is InitialUserKeyAuthStateUsernameDeleted &&
        usernameDeleted != null) {
      return usernameDeleted();
    }
    if (state is InitialUserKeyAuthStateAuthSuccess && authSuccess != null) {
      return authSuccess(
        state.sessionKey,
        state.authToken,
      );
    }
    if (state is InitialUserKeyAuthStateAuthCanceled && authCanceled != null) {
      return authCanceled();
    }
    if (state is InitialUserKeyAuthStateUnknownError && unknownError != null) {
      return unknownError();
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function(V3LoginRequestUserKey loginReq) loginRequest,
    required T Function() loginRequestCanceled,
    required T Function(V3RegisterRequestUserKey registerReq) registerRequest,
    required T Function() registerRequestCanceled,
    required T Function(
      String username,
      String reqChallenge,
      Object request,
      Object hashCash,
    ) goToAuthProgressPage,
    required T Function() waiting,
    required T Function(String error) error,
    required T Function() usernameInvalid,
    required T Function() usernameRequestByOtherUser,
    required T Function() usernameDeleted,
    required T Function(String sessionKey, String authToken) authSuccess,
    required T Function() authCanceled,
    required T Function() unknownError,
  }) {
    final state = this;

    if (state is InitialUserKeyAuthStateInitial) return initial();
    if (state is InitialUserKeyAuthStateLoginRequest)
      return loginRequest(state.loginReq);
    if (state is InitialUserKeyAuthStateLoginRequestCanceled)
      return loginRequestCanceled();
    if (state is InitialUserKeyAuthStateRegisterRequest)
      return registerRequest(state.registerReq);
    if (state is InitialUserKeyAuthStateRegisterRequestCanceled)
      return registerRequestCanceled();
    if (state is InitialUserKeyAuthStateGoToAuthProgressPage) {
      return goToAuthProgressPage(
        state.username,
        state.reqChallenge,
        state.request,
        state.hashCash,
      );
    }
    if (state is InitialUserKeyAuthStateWaiting) return waiting();
    if (state is InitialUserKeyAuthStateError) return error(state.error);
    if (state is InitialUserKeyAuthStateUsernameInvalid)
      return usernameInvalid();
    if (state is InitialUserKeyAuthStateUsernameRequestByOtherUser) {
      return usernameRequestByOtherUser();
    }
    if (state is InitialUserKeyAuthStateUsernameDeleted)
      return usernameDeleted();
    if (state is InitialUserKeyAuthStateAuthSuccess) {
      return authSuccess(
        state.sessionKey,
        state.authToken,
      );
    }
    if (state is InitialUserKeyAuthStateAuthCanceled) {
      return authCanceled();
    }
    if (state is InitialUserKeyAuthStateUnknownError) {
      return unknownError();
    }
    throw StateError('Unhandled state: $state');
  }
}
