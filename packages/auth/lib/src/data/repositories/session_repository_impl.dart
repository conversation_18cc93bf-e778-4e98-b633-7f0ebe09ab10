import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../auth.dart';
import 'database/auth_database.dart';
import 'database/entities/session_local_metadata.dart';
import 'database/generated/objectbox.g.dart';

@LazySingleton(as: SessionRepository)
class SessionRepositoryImpl extends SessionRepository {
  SessionRepositoryImpl(this._store) {
    _listenUpdateResumeId();
  }

  void _listenUpdateResumeId() {
    GetIt.instance.get<AppEventBus>().on<ResumeIdUpdated>().listen((event) {
      updateResumeId(event.id);
    });

    GetIt.instance
        .get<AppEventBus>()
        .on<OnWebsocketConnected>()
        .listen((event) {
      _handleWebsocketConnected();
    });
  }

  final AuthStore _store;

  late SessionLocalMetadata? _metadata = null;

  Box<Session> get _sessionBox => _store.box<Session>();

  Box<SessionLocalMetadata> get _metaBox => _store.box<SessionLocalMetadata>();

  @override
  int insert(Session session) {
    final query = _sessionBox
        .query(Session_.sessionKey.equals(session.sessionKey))
        .build();
    final oldSession = query.findFirst();
    if (oldSession != null) {
      session.id = oldSession.id;
    }
    return _sessionBox.put(session);
  }

  @override
  Stream<List<Session>> getSessionsStream() {
    return _sessionBox
        .query()
        .watch(triggerImmediately: true)
        .map((query) => query.find());
  }

  @override
  List<Session> getSessions() {
    return _sessionBox.getAll();
  }

  @override
  List<Session> getSessionsSkipQRLogin() {
    final query = _sessionBox.query(Session_.isLoginQR.equals(false)).build();
    final session = query.find();
    return session;
  }

  @override
  Session? getSession(String sessionKey) {
    final query =
        _sessionBox.query(Session_.sessionKey.equals(sessionKey)).build();
    final session = query.findFirst();
    query.close();
    return session;
  }

  @override
  bool deleteSession(String sessionKey) {
    final query =
        _sessionBox.query(Session_.sessionKey.equals(sessionKey)).build();
    final session = query.findFirst();
    if (session != null) {
      _deleteMeta(sessionKey);
      return _sessionBox.remove(session.id);
    }
    return false;
  }

  bool _deleteMeta(String sessionKey) {
    final query = _metaBox
        .query(SessionLocalMetadata_.sessionKey.equals(sessionKey))
        .build();
    query.remove();
    return true;
  }

  @override
  int deleteAllSessions() {
    return _sessionBox.removeAll();
  }

  @override
  Session? getActiveSession() {
    final query = _sessionBox.query(Session_.active.equals(true)).build();
    final session = query.findFirst();
    return session;
  }

  @override
  int setSessionInactive(Session session) {
    session.active = false;
    return _sessionBox.put(session);
  }

  @override
  SessionLocalMetadata? getSessionMetadata() {
    final query = _metaBox
        .query(
          SessionLocalMetadata_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? ''),
        )
        .build();
    final metadata = query.findFirst();
    query.close();
    return metadata;
  }

  @override
  int insertMetadata(SessionLocalMetadata metadata) {
    Session? session = getSession(metadata.sessionKey);

    if (session == null) return 0;
    var newMetadata = metadata;

    if (_metadata == null || _metadata?.sessionKey != metadata.sessionKey) {
      final query = _metaBox
          .query(SessionLocalMetadata_.sessionKey.equals(metadata.sessionKey))
          .build();
      final oldMeta = query.findFirst();

      if (oldMeta != null) {
        newMetadata = oldMeta;
      }
    } else {
      newMetadata = _metadata!;
    }

    _metadata = newMetadata;

    return _metaBox.put(metadata);
  }

  @override
  void updateResumeId(String resumeId) {
    if (_metadata == null) {
      final query = _metaBox
          .query(
            SessionLocalMetadata_.sessionKey
                .equals(Config.getInstance().activeSessionKey ?? ''),
          )
          .build();
      final oldMeta = query.findFirst();

      if (oldMeta == null) return;

      _metadata = oldMeta;
    }

    _metadata!.resumeId = resumeId;

    _metaBox.put(_metadata!);
  }

  void _handleWebsocketConnected() {
    var metadata = _metadata;

    if (metadata == null) {
      metadata = getSessionMetadata();
    }

    if (metadata == null) return;

    _metadata = metadata;
    AppEventBus.publish(ResumeWebsocket(data: metadata.resumeId));
  }

  @override
  Future<Session?> getActiveSessionAsync() async {
    return await _sessionBox
        .query(Session_.active.equals(true))
        .build()
        .findFirstAsync();
  }

  @override
  Future<void> setPasskeyMigrated() async {
    Session? session = await getActiveSessionAsync();

    if (session == null) return;

    session.passkeyMigrated = true;

    await _sessionBox.putAsync(session);
  }

  @override
  Future<void> updateUserUpdateTimeAfter(String updateTimeAfter) async {
    var metadata = getSessionMetadata();

    if (metadata == null) return;

    _metaBox.put(metadata.copyWith(userUpdateTimeAfter: updateTimeAfter));
  }
}
