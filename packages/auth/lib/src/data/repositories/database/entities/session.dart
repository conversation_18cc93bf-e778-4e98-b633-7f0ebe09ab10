import 'package:objectbox/objectbox.dart';

import '../enums/status.dart';

@Entity()
class Session {
  Session({
    required this.sessionKey,
    required this.sessionId,
    required this.sessionToken,
    required this.active,
    required this.isLogin,
    this.status,
    this.logoutTime,
    this.loginTime,
    this.passkeyMigrated = false,
    this.isLoginQR = false,
  });

  Session.mock()
      : id = 1,
        sessionKey = 'mockSessionKey',
        sessionId = 'mockSessionId',
        sessionToken = 'mockSessionToken',
        active = true,
        isLogin = true,
        isLoginQR = true,
        passkeyMigrated = false,
        status = Status.active,
        logoutTime = null,
        loginTime = null;

  @Id(assignable: true)
  int id = 0;

  @Property(uid: 1001)
  @Index()
  String sessionKey;

  @Property(uid: 1002)
  @Transient()
  Status? status;

  @Property(uid: 1003)
  String sessionId;

  @Property(uid: 1004, type: PropertyType.date)
  DateTime? logoutTime;

  @Property(uid: 1005, type: PropertyType.date)
  DateTime? loginTime;

  @Property(uid: 1006)
  String sessionToken;

  @Property(uid: 1007)
  bool active;

  @Property(uid: 1008)
  bool isLogin;

  @Property(uid: 1009)
  bool isLoginQR;

  @Property(uid: 1010)
  bool passkeyMigrated;

  String? get dbStatus {
    _ensureStableEnumValues();
    return status?.rawValue;
  }

  set dbStatus(String? value) {
    _ensureStableEnumValues();
    if (value == null) {
      status = null;
    } else {
      status = StatusExtension.fromRawValue(value);
    }
  }

  void _ensureStableEnumValues() {
    assert(
      Status.valid.rawValue == 'valid',
      "Status.valid must have the raw value 'valid'",
    );
    assert(
      Status.expired.rawValue == 'expired',
      "Status.expired must have the raw value 'expired'",
    );
    assert(
      Status.loggedOut.rawValue == 'logged_out',
      "Status.loggedOut must have the raw value 'logged_out'",
    );
    assert(
      Status.deleted.rawValue == 'deleted',
      "Status.deleted must have the raw value 'deleted'",
    );
    assert(
      Status.authRequest.rawValue == 'auth_request',
      "Status.authRequest must have the raw value 'auth_request'",
    );
    assert(
      Status.active.rawValue == 'active',
      "Status.active must have the raw value 'active'",
    );
  }

  factory Session.fromJson(Map<String, dynamic> json) {
    return Session(
      sessionKey: json['sessionKey'],
      sessionId: json['sessionId'],
      sessionToken: json['sessionToken'],
      active: json['active'] ?? false,
      isLogin: json['isLogin'] ?? false,
      status: json['status'] != null
          ? StatusExtension.fromRawValue(json['status'])
          : null,
      logoutTime: json['logoutTime'] != null
          ? DateTime.parse(json['logoutTime'])
          : null,
      loginTime:
          json['loginTime'] != null ? DateTime.parse(json['loginTime']) : null,
      passkeyMigrated: json['passkeyMigrated'] ?? false,
      isLoginQR: json['isLoginQR'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'sessionKey': sessionKey,
      'sessionId': sessionId,
      'sessionToken': sessionToken,
      'active': active,
      'isLogin': isLogin,
      'status': status?.rawValue,
      'logoutTime': logoutTime?.toIso8601String(),
      'loginTime': loginTime?.toIso8601String(),
      'passkeyMigrated': passkeyMigrated,
      'isLoginQR': isLoginQR,
    };
  }
}
