import 'package:flutter/foundation.dart';
import 'package:objectbox/internal.dart';
import 'package:objectbox/objectbox.dart';
import 'package:shared/shared.dart';

class AuthDatabase {
  AuthDatabase(this.store) {
    if (Admin.isAvailable() && kDebugMode && GlobalConfig.enableAuthBoxAdmin) {
      admin = AuthAdmin(store, bindUri: 'http://127.0.0.1:8091');
    }
  }

  AuthAdmin? admin;

  final AuthStore store;
}

class AuthAdmin extends Admin {
  AuthAdmin(this.store, {required this.bindUri})
      : super(store, bindUri: bindUri) {}

  final String bindUri;

  final Store store;
}

class AuthStore extends Store {
  AuthStore(ModelDefinition modelDefinition, {required String directory})
      : super(modelDefinition, directory: directory);
}
