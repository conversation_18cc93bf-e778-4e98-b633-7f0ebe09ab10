import 'package:json_annotation/json_annotation.dart';

part 'client_data.g.dart';

@JsonSerializable(explicitToJson: true)
class ClientData {
  ClientData({
    required this.challenge,
    required this.origin,
    required this.type,
  });

  factory ClientData.fromJson(Map<String, dynamic> json) =>
      _$ClientDataFromJson(json);

  final String challenge;
  final String origin;
  final String type;

  Map<String, dynamic> toJson() => _$ClientDataToJson(this);
}
