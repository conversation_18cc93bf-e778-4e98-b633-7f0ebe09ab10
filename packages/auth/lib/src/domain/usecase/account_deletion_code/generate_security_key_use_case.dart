import 'package:auth_api/auth_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/source/api/client/auth_client.dart';

@Injectable()
class GenerateSecurityKeyUseCase extends BaseFutureUseCase<
    GenerateSecurityKeyInput, GenerateSecurityKeyOutput> {
  GenerateSecurityKeyUseCase();

  @override
  Future<GenerateSecurityKeyOutput> buildUseCase(
    GenerateSecurityKeyInput input,
  ) async {
    try {
      final response = await AuthClient().instance.generateSecurityKey(
            body: input.request,
          );

      return GenerateSecurityKeyOutput(
        ok: response.data?.ok ?? false,
        securityKey: response.data?.data?.securityKey ?? '',
        error: null,
      );
    } catch (e) {
      return GenerateSecurityKeyOutput(
        ok: false,
        securityKey: '',
        error: e.toString(),
      );
    }
  }
}

class GenerateSecurityKeyInput extends BaseInput {
  GenerateSecurityKeyInput({
    required this.request,
  });

  final V3GenerateSecurityKeyRequest request;
}

class GenerateSecurityKeyOutput extends BaseOutput {
  GenerateSecurityKeyOutput({
    required this.ok,
    required this.securityKey,
    required this.error,
  });

  final bool ok;
  final String securityKey;
  final String? error;
}
