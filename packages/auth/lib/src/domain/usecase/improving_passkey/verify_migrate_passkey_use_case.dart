import 'package:auth_api/auth_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/source/api/client/auth_client.dart';

@Injectable()
class VerifyMigratePasskeyUseCase extends BaseFutureUseCase<
    VerifyMigratePasskeyInput, VerifyMigratePasskeyOutput> {
  VerifyMigratePasskeyUseCase();

  @override
  Future<VerifyMigratePasskeyOutput> buildUseCase(
    VerifyMigratePasskeyInput input,
  ) async {
    var request = V3VerifyMigratePasskeyRequestBuilder();
    request.reqId = input.reqId;
    request.reqVerifier = input.reqVerifier;
    request.credential = input.credential.toBuilder();

    final response =
        await AuthClient().instance.verifyMigratePasskey(body: request.build());

    return VerifyMigratePasskeyOutput(ok: response.data!.ok!);
  }
}

class VerifyMigratePasskeyInput extends BaseInput {
  final String reqId;
  final String reqVerifier;
  final CommonAttestationResult credential;

  VerifyMigratePasskeyInput({
    required this.reqId,
    required this.reqVerifier,
    required this.credential,
  });
}

class VerifyMigratePasskeyOutput extends BaseOutput {
  VerifyMigratePasskeyOutput({required this.ok});

  final bool ok;
}
