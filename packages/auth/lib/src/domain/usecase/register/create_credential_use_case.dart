import 'dart:convert';

import 'package:auth_api/auth_api.dart';
import 'package:injectable/injectable.dart';
import 'package:passkeys/authenticator.dart';
import 'package:passkeys/types.dart';
import 'package:shared/shared.dart';
import 'package:webauthn/webauthn.dart';

import '../../../data/repositories/source/api/hash_cash/hash_cash.dart';

@Injectable()
class CreateCredentialUseCase
    extends BaseFutureUseCase<CreateCredentialInput, CreateCredentialOutput> {
  CreateCredentialUseCase();

  @override
  Future<CreateCredentialOutput> buildUseCase(
    CreateCredentialInput input,
  ) async {
    bool shouldRegisterWithOldAPI = await checkAuthWithOldAPI(true);

    V3RegisterWithUserKeyRequest? registerResponse = shouldRegisterWithOldAPI
        ? await _handleRegisterWithWebAPI(input)
        : await _handleRegisterWithPassKeys(input);

    return CreateCredentialOutput(registerRequest: registerResponse);
  }

  Future<V3RegisterWithUserKeyRequest> _handleRegisterWithWebAPI(
    CreateCredentialInput input,
  ) async {
    final registerRequest = input.registerRequest;

    final credentialCreationOptions = registerRequest.credentialCreationOptions;
    final requestId = registerRequest.reqId;

    final createCredentialOptions = jsonDecode(
      standardSerializers.toJson(
        CommonPublicKeyCredentialCreationOptions.serializer,
        credentialCreationOptions,
      ),
    );

    final jsonData = createCredentialOptions as Map<String, dynamic>;

    final authenticatorSelectionCriteria = {
      'attachment': 'platform',
      'requireResidentKey': true,
      'residentKeyRequirement': 'preferred',
    };

    jsonData['authenticatorSelection'] = authenticatorSelectionCriteria;

    try {
      final webApi = WebAPI();
      final rpOptions = CreateCredentialOptions.fromJson(<String, dynamic>{
        'publicKey': jsonData,
      });

      final (clientData, makeCredentialOptions) =
          await webApi.createMakeCredentialOptions(
        'https://ziichat.com',
        rpOptions,
        true,
      );

      final attestation =
          await Authenticator.handleMakeCredential(makeCredentialOptions);
      final responseObj =
          await webApi.createAttestationResponse(clientData, attestation);

      final attestationResponse = CommonAttestationResponseBuilder()
        ..attestationObject = b64e(responseObj.response.attestationObject)
        ..clientDataJSON = b64e(responseObj.response.clientDataJSON);

      final credential = CommonAttestationResultBuilder()
        ..rawId = b64e(responseObj.rawId)
        ..id = responseObj.id
        ..response = attestationResponse;

      final registerWithUserKeyRequest = V3RegisterWithUserKeyRequestBuilder()
        ..credential = credential
        ..reqId = requestId
        ..reqVerifier = input.reqChallenge;

      return registerWithUserKeyRequest.build();
    } catch (e) {
      throw new Exception(e);
    }
  }

  Future<V3RegisterWithUserKeyRequest?> _handleRegisterWithPassKeys(
    CreateCredentialInput input,
  ) async {
    final registerRequest = input.registerRequest;

    final creationOptions = registerRequest.credentialCreationOptions;

    final PasskeyAuthenticator passkeyAuthenticator = PasskeyAuthenticator();
    List<PubKeyCredParamType> pubKeyCredParams = [];

    creationOptions?.pubKeyCredParams?.forEach((param) {
      pubKeyCredParams.add(
        PubKeyCredParamType(
          type: param.type ?? '',
          alg: param.alg ?? 255,
        ),
      );
    });

    RegisterRequestType request = RegisterRequestType(
      challenge: creationOptions?.challenge ?? '',
      relyingParty: RelyingPartyType(
        id: creationOptions?.rp?.id ?? '',
        name: creationOptions?.rp?.name ?? '',
      ),
      user: UserType(
        id: creationOptions?.user?.id ?? '',
        name: creationOptions?.user?.name ?? '',
        displayName: creationOptions?.user?.displayName ?? '',
      ),
      authSelectionType: AuthenticatorSelectionType(
        authenticatorAttachment: 'platform',
        requireResidentKey: false,
        residentKey: 'preferred',
        userVerification: 'required',
      ),
      pubKeyCredParams: pubKeyCredParams,
      timeout: creationOptions?.timeout ?? 60000,
      attestation: creationOptions?.attestation ?? '',
      excludeCredentials: [],
    );

    final registerResult = await passkeyAuthenticator.register(request);

    final attestationResponse = CommonAttestationResponseBuilder()
      ..attestationObject = registerResult.attestationObject
      ..clientDataJSON = registerResult.clientDataJSON;

    final credential = CommonAttestationResultBuilder()
      ..rawId = registerResult.rawId
      ..id = registerResult.id
      ..response = attestationResponse;

    final registerWithUserKeyRequest = V3RegisterWithUserKeyRequestBuilder()
      ..credential = credential
      ..reqId = registerRequest.reqId
      ..reqVerifier = input.reqChallenge;

    return registerWithUserKeyRequest.build();
  }
}

class CreateCredentialInput extends BaseInput {
  CreateCredentialInput({
    required this.registerRequest,
    required this.reqChallenge,
    required this.hashCash,
  });

  final V3RegisterRequestUserKey registerRequest;
  final String reqChallenge;
  final HashCash hashCash;
}

class CreateCredentialOutput extends BaseOutput {
  CreateCredentialOutput({required this.registerRequest});

  final V3RegisterWithUserKeyRequest? registerRequest;
}
