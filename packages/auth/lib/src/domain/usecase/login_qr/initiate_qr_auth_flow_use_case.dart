import 'package:auth_api/auth_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/source/api/client/auth_client.dart';
import '../../../data/repositories/source/api/hash_cash/hash_cash.dart';

@Injectable()
class InitiateQRAuthFlowUseCase extends BaseFutureUseCase<
    InitiateQRAuthFlowUseCaseInput, InitiateQRAuthFlowUseCaseOutput> {
  InitiateQRAuthFlowUseCase();

  @override
  Future<InitiateQRAuthFlowUseCaseOutput> buildUseCase(
    InitiateQRAuthFlowUseCaseInput input,
  ) async {
    final request = V3InitiateQRAuthFlowRequestBuilder()
      ..reqId = input.requestId;

    final response = await AuthClient().instance.initiateQRAuthFlow(
          body: request.build(),
          headers: input.hashCash.getHashCashMetadata(),
        );

    return InitiateQRAuthFlowUseCaseOutput(response: response.data!);
  }
}

class InitiateQRAuthFlowUseCaseInput extends BaseInput {
  InitiateQRAuthFlowUseCaseInput({
    required this.requestId,
    required this.hashCash,
  });

  final String requestId;
  final HashCash hashCash;
}

class InitiateQRAuthFlowUseCaseOutput extends BaseOutput {
  InitiateQRAuthFlowUseCaseOutput({required this.response});

  final V3InitiateQRAuthFlowResponse response;
}
