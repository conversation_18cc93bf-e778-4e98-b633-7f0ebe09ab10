import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../auth.dart';

@Injectable()
class SessionLoginIsQRUseCase
    extends BaseFutureUseCase<SessionLoginIsQRInput, SessionLoginIsQROutput> {
  const SessionLoginIsQRUseCase(this._repository);

  final SessionRepository _repository;

  @protected
  @override
  Future<SessionLoginIsQROutput> buildUseCase(
    SessionLoginIsQRInput input,
  ) async {
    Session? session = _repository.getActiveSession();

    return SessionLoginIsQROutput(session: session!);
  }
}

class SessionLoginIsQRInput extends BaseInput {
  SessionLoginIsQRInput();
}

class SessionLoginIsQROutput extends BaseOutput {
  final Session session;

  SessionLoginIsQROutput({required this.session});
}
