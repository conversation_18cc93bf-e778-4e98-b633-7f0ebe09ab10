import 'package:auth_api/auth_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/source/api/client/auth_client.dart';
import '../hash_cash_use_case.dart';

@Injectable()
class VerifyQRAuthUseCaseUseCase extends BaseFutureUseCase<
    VerifyQRAuthUseCaseInput, VerifyQRAuthUseCaseOutput> {
  VerifyQRAuthUseCaseUseCase(this._hashCashUseCase);

  final HashCashUseCase _hashCashUseCase;

  @override
  Future<VerifyQRAuthUseCaseOutput> buildUseCase(
    VerifyQRAuthUseCaseInput input,
  ) async {
    final hashCashOutput = await _hashCashUseCase.execute(HashCashInput());

    final authRequest = V3VerifyQRAuthRequestBuilder()
      ..reqId = input.requestId
      ..reqChallenge = input.reqChallenge
      ..qrAuthCode = input.qrAuthCode;

    final verifyResponse = await AuthClient().instance.verifyQRAuth(
          body: authRequest.build(),
          headers: hashCashOutput.hashCash.getHashCashMetadata(),
        );

    return VerifyQRAuthUseCaseOutput(
      v3VerifyQRAuthData: verifyResponse.data!,
    );
  }
}

class VerifyQRAuthUseCaseInput extends BaseInput {
  VerifyQRAuthUseCaseInput({
    required this.requestId,
    required this.qrAuthCode,
    required this.reqChallenge,
  });

  final String requestId;
  final String qrAuthCode;
  final String reqChallenge;
}

class VerifyQRAuthUseCaseOutput extends BaseOutput {
  VerifyQRAuthUseCaseOutput({required this.v3VerifyQRAuthData});

  final V3VerifyQRAuthResponse v3VerifyQRAuthData;
}
