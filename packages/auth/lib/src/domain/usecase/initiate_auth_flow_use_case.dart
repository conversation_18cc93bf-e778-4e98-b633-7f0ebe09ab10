import 'package:auth_api/auth_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../data/repositories/source/api/client/auth_client.dart';
import '../../data/repositories/source/api/hash_cash/hash_cash.dart';

@Injectable()
class InitiateUserKeyAuthFlowUseCase extends BaseFutureUseCase<
    InitiateUserKeyAuthFlowInput, InitiateUserKeyAuthFlowOutput> {
  InitiateUserKeyAuthFlowUseCase();

  @override
  Future<InitiateUserKeyAuthFlowOutput> buildUseCase(
    InitiateUserKeyAuthFlowInput input,
  ) async {
    final reqChallengeHash = input.reqChallengeHash;

    final hashCash = input.hashCash;
    final deviceId = await getDeviceId();

    final request = V3InitiateUserKeyAuthFlowRequestBuilder()
      ..deviceId = deviceId
      ..userKey = input.username
      ..reqChallenge = reqChallengeHash;

    final response = await AuthClient().instance.initiateUserKeyAuthFlow(
          body: request.build(),
          headers: hashCash.getHashCashMetadata(),
        );

    return InitiateUserKeyAuthFlowOutput(response: response.data!);
  }
}

class InitiateUserKeyAuthFlowInput extends BaseInput {
  InitiateUserKeyAuthFlowInput({
    required this.username,
    required this.reqChallengeHash,
    required this.hashCash,
  });

  final String username;
  final String reqChallengeHash;
  final HashCash hashCash;
}

class InitiateUserKeyAuthFlowOutput extends BaseOutput {
  InitiateUserKeyAuthFlowOutput({required this.response});

  final V3InitiateUserKeyAuthFlowResponse response;
}
