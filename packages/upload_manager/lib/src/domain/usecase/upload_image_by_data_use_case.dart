import 'package:dio/dio.dart';
import 'package:filestore_sdk/filestore_sdk.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../upload_manager.dart';

part 'upload_image_by_data_use_case.freezed.dart';

@Injectable()
class UploadImageByDataUseCase
    extends BaseFutureUseCase<UploadImageByDataInput, UploadImageByDataOutput> {
  final FileStoreSDKClient _fileStoreSDKClient;

  UploadImageByDataUseCase(this._fileStoreSDKClient);

  @protected
  @override
  Future<UploadImageByDataOutput> buildUseCase(
    UploadImageByDataInput input,
  ) async {
    try {
      final file = input.file;
      final String token = input.token;
      final instance = _fileStoreSDKClient.instance;
      final userId = Config.getInstance().activeSessionKey;
      final folderPath = "/$userId/";

      instance.uploadFile(
        UpFile(
          path: file.path,
          name: file.name,
          size: file.size,
        ),
        onSuccess: input.success,
        onError: input.error,
        cancelToken: input.cancelToken,
        token: token,
        folderPath: folderPath,
      );

      return UploadImageByDataOutput(
        success: true,
      );
    } catch (e) {
      return UploadImageByDataOutput(
        success: false,
        errorMessage: e.toString(),
      );
    }
  }
}

@freezed
sealed class UploadImageByDataInput extends BaseInput
    with _$UploadImageByDataInput {
  const UploadImageByDataInput._();
  factory UploadImageByDataInput({
    required String token,
    required UploadFile file,
    Function(UpFile objFile, String fileUrl)? success,
    void Function(double progres)? progress,
    void Function(UpFile objFile, ErrorCode code, String errorMessage)? error,
    CancelToken? cancelToken,
  }) = _UploadImageByDataInput;
}

@freezed
sealed class UploadImageByDataOutput extends BaseOutput
    with _$UploadImageByDataOutput {
  const UploadImageByDataOutput._();
  factory UploadImageByDataOutput({
    required bool success,
    final String? errorMessage,
  }) = _UploadImageByDataOutput;
}
