import 'package:dio/dio.dart';
import 'package:filestore_sdk/filestore_sdk.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../upload_manager.dart';

part 'upload_video_use_case.freezed.dart';

@Injectable()
class UploadVideoUseCase
    extends BaseFutureUseCase<UploadVideoInput, UploadVideoOutput> {
  final FileStoreSDKClient _fileStoreSDKClient;

  UploadVideoUseCase(this._fileStoreSDKClient);

  @protected
  @override
  Future<UploadVideoOutput> buildUseCase(
    UploadVideoInput input,
  ) async {
    try {
      final file = input.file;
      final String token = input.token;
      final instance = _fileStoreSDKClient.instance;
      final userId = Config.getInstance().activeSessionKey;
      final folderPath = "/$userId/";

      instance.uploadFile(
        UpFile(
          path: file.path,
          name: file.name,
          size: file.size,
          fileData: file.fileData,
        ),
        onSuccess: input.success,
        onError: input.error,
        cancelToken: input.cancelToken,
        token: token,
        folderPath: folderPath,
      );

      return UploadVideoOutput(
        success: true,
      );
    } catch (e) {
      return UploadVideoOutput(
        success: false,
        errorMessage: e.toString(),
      );
    }
  }
}

@freezed
sealed class UploadVideoInput extends BaseInput with _$UploadVideoInput {
  const UploadVideoInput._();
  factory UploadVideoInput({
    required String token,
    required UploadFile file,
    Function(UpFile objFile, String fileUrl)? success,
    void Function(double progres)? progress,
    void Function(UpFile objFile, ErrorCode code, String errorMessage)? error,
    CancelToken? cancelToken,
  }) = _UploadVideoInput;
}

@freezed
sealed class UploadVideoOutput extends BaseOutput with _$UploadVideoOutput {
  const UploadVideoOutput._();
  factory UploadVideoOutput({
    required bool success,
    final String? errorMessage,
  }) = _UploadVideoOutput;
}
