name: upload_manager
description: Include core structure elements
version: 0.1.0+1
publish_to: none
environment:
  sdk: ^3.4.0
  flutter: ^3.22.0
dependencies:
  flutter:
    sdk: flutter
  get_it: ^8.0.3
  injectable: ^2.5.0
  app_core:
    path: ../app_core
  shared:
    path: ../shared
  path_provider: ^2.1.5
  freezed_annotation: ^3.0.0
  json_annotation: ^4.9.0
  auth_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.26
      path: apis/auth_api
  filestore_sdk:
    git:
      url: **************:ziichatlabs/filestore-sdk.git
      ref: v0.1.0
dev_dependencies:
  build_runner: ^2.4.15
  flutter_test:
    sdk: flutter
  freezed: ^3.0.6
  injectable_generator: ^2.7.0
  mocktail: ^1.0.4
  very_good_analysis: ^7.0.0
  json_serializable: ^6.9.1
dependency_overrides:
  analyzer: 7.3.0
