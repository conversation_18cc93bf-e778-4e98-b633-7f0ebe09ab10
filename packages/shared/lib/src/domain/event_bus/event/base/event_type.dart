const String WS_CHAT_PREFIX = "com.halome.chat.v3.";
const String WS_IAM_PREFIX = "com.halome.iam.v3.";
const String WS_CALL_PREFIX = "com.halome.call.v3.";
const String WS_USER_PREFIX = "com.halome.user.v3.";
const String WS_USER_PROFILE_PREFIX = "com.halome.user.v3.profile.";
const String WS_REALTIME_PREFIX = "com.halome.realtime.v3";
const String WS_MEETING_PREFIX = "com.halome.meeting.v3";

enum EventType {
  // Gateway
  GATEWAY_CONNECTED(WS_REALTIME_PREFIX + ".gateway.connected"),
  WEBSOCKET_RESUME("com.halome.websocket.v3.reconnection_started"),
  WEBSOCKET_RESUME_END("com.halome.websocket.v3.reconnection_ended"),

  // Message
  MESSAGE_CREATED(WS_CHAT_PREFIX + "message.created"),
  MESSAGE_UPDATED(WS_CHAT_PREFIX + "message.updated"),
  MESSAGE_DELETED(WS_CHAT_PREFIX + "message.deleted"),
  MESSAGES_DELETE_ALL(WS_CHAT_PREFIX + "all_messages.deleted"),
  MESSAGE_REACTION_UPDATED(WS_CHAT_PREFIX + "message.reaction_updated"),
  MESSAGE_USER_REACTION_UPDATED(
    WS_CHAT_PREFIX + "message.user_reaction_updated",
  ),
  UNREAD_MESSAGE_UPDATED(WS_CHAT_PREFIX + "unread_messages.updated"),
  ALL_USER_MESSAGE_DELETED(WS_CHAT_PREFIX + "all_user_messages.deleted"),
  USER_MESSAGE_DELETED(WS_CHAT_PREFIX + "user_message.deleted"),
  PIN_MESSAGE(WS_CHAT_PREFIX + "message.pinned"),
  UNPIN_MESSAGE(WS_CHAT_PREFIX + "message.unpinned"),

  // Workspace
  WORKSPACE_CREATED(WS_CHAT_PREFIX + "workspace.created"),
  WORKSPACE_CREATION_COMPLETED(WS_CHAT_PREFIX + "workspace.creation.completed"),
  WORKSPACE_CREATION_FAILED(WS_CHAT_PREFIX + "workspace.creation.failed"),

  // Channel
  CHANNEL_AVATAR_UPDATED(WS_CHAT_PREFIX + "channel.avatar_updated"),
  CHANNEL_AVATAR_UPLOAD_FAILED(WS_CHAT_PREFIX + "channel.avatar.upload.failed"),
  CHANNEL_CREATED(WS_CHAT_PREFIX + "channel.created"),
  CHANNEL_CREATION_COMPLETED(WS_CHAT_PREFIX + "channel.creation.completed"),
  CHANNEL_CREATION_FAILED(WS_CHAT_PREFIX + "channel.creation.failed"),
  CHANNEL_DELETED(WS_CHAT_PREFIX + "channel.deleted"),
  CHANNEL_NAME_UPDATED(WS_CHAT_PREFIX + "channel.name_updated"),
  CHANNEL_TYPING(WS_CHAT_PREFIX + "channel.typing_signal"),
  CHANNEL_UPDATED(WS_CHAT_PREFIX + "channel.updated"),
  CHANNEL_DM_CREATE(WS_CHAT_PREFIX + "dm_channel.created"),

  // Member
  MEMBER_BANNED(WS_CHAT_PREFIX + "member.banned"),
  MEMBER_JOINED(WS_CHAT_PREFIX + "member.joined"),
  MEMBER_LEFT(WS_CHAT_PREFIX + "member.left"),
  MEMBER_NICKNAME_UPDATED(WS_CHAT_PREFIX + "member.nickname_updated"),
  MEMBER_REMOVED(WS_CHAT_PREFIX + "member.removed"),
  MEMBER_ROLE_REVOKED(WS_CHAT_PREFIX + "member.role_revoked"),
  MEMBER_ROLE_UPDATED(WS_CHAT_PREFIX + "member.role_updated"),
  MEMBER_UNBANNED(WS_CHAT_PREFIX + "member.unbanned"),

  // Friend
  FRIEND_ADDED(WS_CHAT_PREFIX + "friend.added"),
  FRIEND_UNFRIENDED(WS_CHAT_PREFIX + "friend.unfriended"),
  FRIEND_REQUEST_ACCEPTED(WS_CHAT_PREFIX + "friend.request_accepted"),
  FRIEND_REQUEST_CANCELED(WS_CHAT_PREFIX + "friend.request_canceled"),
  FRIEND_REQUEST_DELETED(WS_CHAT_PREFIX + "friend.request_deleted"),
  INCOMING_FRIEND_REQUEST_CREATED(
    WS_CHAT_PREFIX + "incoming_friend_request.created",
  ),
  OUTGOING_FRIEND_REQUEST_CREATED(
    WS_CHAT_PREFIX + "outgoing_friend_request.created",
  ),
  INCOMING_FRIEND_REQUEST_CANCELED(
    WS_CHAT_PREFIX + "incoming_friend_request.canceled",
  ),
  OUTGOING_FRIEND_REQUEST_CANCELED(
    WS_CHAT_PREFIX + "outgoing_friend_request.canceled",
  ),
  INCOMING_FRIEND_REQUEST_ACCEPTED(
    WS_CHAT_PREFIX + "incoming_friend_request.accepted",
  ),
  OUTGOING_FRIEND_REQUEST_ACCEPTED(
    WS_CHAT_PREFIX + "outgoing_friend_request.accepted",
  ),
  INCOMING_FRIEND_REQUEST_DELETED(
    WS_CHAT_PREFIX + "incoming_friend_request.deleted",
  ),
  OUTGOING_FRIEND_REQUEST_DELETED(
    WS_CHAT_PREFIX + "outgoing_friend_request.deleted",
  ),

  // Invitation
  INVITATION(WS_CHAT_PREFIX + "invitation"),
  INVITATION_SENT(WS_CHAT_PREFIX + "invitation.sent"),
  INCOMING_MESSAGE_REQUEST_CREATED(
    WS_CHAT_PREFIX + "incoming_message_request.created",
  ),
  OUTGOING_MESSAGE_REQUEST_CREATED(
    WS_CHAT_PREFIX + "outgoing_message_request.created",
  ),
  INCOMING_MESSAGE_REQUEST_ACCEPTED(
    WS_CHAT_PREFIX + "incoming_message_request.accepted",
  ),
  OUTGOING_MESSAGE_REQUEST_ACCEPTED(
    WS_CHAT_PREFIX + "outgoing_message_request.accepted",
  ),
  MESSAGE_REQUEST_REJECTED(WS_CHAT_PREFIX + "message_request.rejected"),
  MESSAGE_REQUEST_ACCEPTED(WS_CHAT_PREFIX + "message_request.accepted"),

  // Block
  BLOCK_USER(WS_IAM_PREFIX + "user.blocked"),
  UNBLOCK_USER(WS_IAM_PREFIX + "user.unblocked"),
  DELETED_USER(WS_IAM_PREFIX + "user.deleted"),
  DEVICE_UNLINKED(WS_IAM_PREFIX + "device.unlinked"),

  // Call
  CALL_CREATED(WS_CALL_PREFIX + "created"),
  CALL_UPDATED(WS_CALL_PREFIX + "updated"),

  // User Profile
  DISPLAY_NAME_UPDATED(WS_USER_PROFILE_PREFIX + "display_name_updated"),
  AVATAR_UPDATED(WS_USER_PROFILE_PREFIX + "avatar_updated"),
  AVATAR_DELETED(WS_USER_PROFILE_PREFIX + "avatar_deleted"),
  USER_STATUS_UPDATED(WS_USER_PROFILE_PREFIX + "user_status_updated"),
  USER_STATUS_DELETED(WS_USER_PROFILE_PREFIX + "user_status_deleted"),
  USER_STATUS_CREATED(WS_USER_PROFILE_PREFIX + "user_status_created"),
  USER_COVER_DELETED(WS_USER_PROFILE_PREFIX + "cover_deleted"),
  USER_COVER_CREATED(WS_USER_PROFILE_PREFIX + "cover_created"),
  USER_COVER_UPDATED(WS_USER_PROFILE_PREFIX + "cover_updated"),

  // VisitedProfile
  VISITED_PROFILE(WS_USER_PROFILE_PREFIX + "visited_profile"),
  VISITED_PROFILE_DELETE(WS_USER_PROFILE_PREFIX + "visited_profile_deleted"),
  CLEAR_VISITED_PROFILE_NOTIFICATIONS(
    WS_USER_PROFILE_PREFIX + "clear_user_visited_profile_notifications",
  ),

  // Presence
  PRESENCE_UPDATED(WS_REALTIME_PREFIX + ".presence.updated"),

  // Private Data Sync
  PRIVATE_DATA_SYNC(WS_USER_PREFIX + "private_data_sync"),

  // MEETING
  MEETING_ROOM_STARTED(WS_MEETING_PREFIX + ".room.started"),
  MEETING_ROOM_ENDED(WS_MEETING_PREFIX + ".room.ended");

  final String value;

  const EventType(this.value);

  static EventType getEnumByValue(String value) {
    return EventType.values.firstWhere(
      (e) => e.value == value,
      orElse: () {
        throw ArgumentError('Invalid value: $value');
      },
    );
  }

  String getValue() {
    return value;
  }

  static EventType fromJson(String jsonValue) {
    return EventType.getEnumByValue(jsonValue);
  }

  static String toJson(EventType type) {
    return type.value;
  }
}
