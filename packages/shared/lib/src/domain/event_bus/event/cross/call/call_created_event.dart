import '../../../../../../shared.dart';

class CallCreatedEvent extends LocalEvent {
  CallCreatedEvent({
    super.source = BaseEvent.LOCAL_SOURCE,
    required this.callee,
    required this.caller,
    this.isVideoCall = false,
  });

  final Map<String, dynamic> callee;
  final Map<String, dynamic> caller;
  final bool isVideoCall;

  @override
  Map<String, dynamic> toJson() {
    return {
      'source': source,
      'callee': callee,
      'caller': caller,
      'isVideoCall': isVideoCall,
    };
  }
}
