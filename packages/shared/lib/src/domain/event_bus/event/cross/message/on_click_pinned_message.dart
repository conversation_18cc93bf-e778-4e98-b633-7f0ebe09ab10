import '../../../../../../shared.dart';

class OnClickPinnedMessageEvent extends LocalEvent {
  OnClickPinnedMessageEvent({
    required this.workspaceId,
    required this.channelId,
    required this.userId,
    required this.messageId,
    super.source = BaseEvent.LOCAL_SOURCE,
  });

  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final String? messageId;

  @override
  Map<String, dynamic> toJson() => {
        'workspaceId': workspaceId,
        'channelId': channelId,
        'userId': userId,
        'messageId': messageId,
      };
}
