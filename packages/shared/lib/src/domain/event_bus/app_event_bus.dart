import 'package:event_bus/event_bus.dart';
import 'package:get_it/get_it.dart';

import '../../../shared.dart';

/// A singleton class that extends [EventBus] to provide a centralized event bus
/// instance for the application.
///
/// This class uses the singleton pattern to ensure that only one instance of
/// the event bus is created and used throughout the application. It helps in
/// maintaining a single point of event distribution and handling.
///
/// All events fired through this event bus must be instances of [BaseEvent].
/// If an event that is not an instance of [BaseEvent] is fired, an [ArgumentError]
/// will be thrown.
///
/// Usage:
/// ```dart
/// // To get the singleton instance of AppEventBus
/// final eventBus = AppEventBus();
///
/// // To fire an event (must be an instance of BaseEvent)
/// eventBus.fire(MyEvent('Hello World'));
///
/// // To listen for an event
/// eventBus.on<MyEvent>().listen((event) {
///   print(event.message);
/// });
/// ```
///
/// Example:
/// ```dart
/// class MyEvent extends BaseEvent {
///   MyEvent(String id) : super(id: id, type: EventType.someType);
///
///   @override
///   Map<String, dynamic> toJson() {
///     return {'id': id, 'type': type.toString()};
///   }
/// }
///
/// void main() {
///   final eventBus = AppEventBus();
///
///   // Listen for events
///   eventBus.on<MyEvent>().listen((event) {
///     print('Received event: ${event.id}');
///   });
///
///   // Fire an event
///   eventBus.fire(MyEvent('event1'));
/// }
/// ```
class AppEventBus extends EventBus {
  /// Returns the singleton instance of [AppEventBus].
  factory AppEventBus() {
    return _instance;
  }

  // Private constructor for the singleton pattern
  AppEventBus._internal();

  // The singleton instance of AppEventBus
  static final AppEventBus _instance = AppEventBus._internal();

  /// Fires an event that must be an instance of [BaseEvent].
  /// Throws an [ArgumentError] if the event is not an instance of [BaseEvent].
  @override
  void fire(dynamic event) {
    if (event is! BaseEvent) {
      throw ArgumentError('Event must be an instance of BaseEvent');
    }

    super.fire(event);
  }

  static void publish(dynamic event) {
    GetIt.instance.get<AppEventBus>().fire(event);
  }
}
