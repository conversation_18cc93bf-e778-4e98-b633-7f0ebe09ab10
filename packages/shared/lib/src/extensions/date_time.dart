import 'package:intl/intl.dart';
import 'package:localization_client/localization_client.dart';

extension DateTimeExtensions on DateTime {
  String toStringTime({required bool use24Hour}) {
    if (use24Hour) {
      return DateFormat.Hm(Intl.getCurrentLocale()).format(this);
    }
    return DateFormat.jm(Intl.getCurrentLocale()).format(this);
  }

  String toLocaleTime(String locale) {
    final now = DateTime.now();

    if (year == now.year) {
      return DateFormat.MMMMd(locale).format(this);
    } else {
      return DateFormat.yMMMMd(locale).format(this);
    }
  }

  String toShortTimeLocale(String locale) {
    final now = DateTime.now();

    if (year == now.year) {
      return DateFormat.MMMd(locale).format(this);
    }
    return DateFormat.yMd(locale).format(this);
  }

  String toLocaleTimeFullScreen(
    AppLocalizations? appLocalizations,
    String? locale, {
    required bool use24Hour,
  }) {
    if (use24Hour) {
      return DateFormatFullScreen.yMMMMdHm(locale).format(this);
    }
    return DateFormatFullScreen.yMMMMdjm(locale).format(this);
  }

  bool isSameDay([DateTime? other]) {
    if (other != null)
      return year == other.year && month == other.month && day == other.day;
    final now = DateTime.now();
    return year == now.year && month == now.month && day == now.day;
  }

  String toShortYMD(String locale) {
    return DateFormat.yMd(locale).format(this);
  }
}

extension DateFormatFullScreen on DateFormat {
  static DateFormat yMMMMdjm([locale]) =>
      DateFormat('yMMMMd', locale).addPattern(' ').addPattern('jm');

  static DateFormat yMMMMdHm([locale]) =>
      DateFormat('yMMMMd', locale).addPattern(' ').addPattern('Hm');
}
