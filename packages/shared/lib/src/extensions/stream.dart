import 'package:rxdart/rxdart.dart';

import '../../shared.dart';

/// Add extensions into Stream
extension StreamExtensions<T> on Stream<T> {
  /// log function
  Stream<T> log(
    String name, {
    bool logOnListen = false,
    bool logOnData = false,
    bool logOnError = false,
    bool logOnDone = false,
    bool logOnCancel = false,
  }) {
    return doOnListen(() {
      if (GlobalConfig.logOnStreamListen && logOnListen) {
        Log.d('▶️ onSubscribed', time: DateTime.now(), name: name);
      }
    }).doOnData((event) {
      if (GlobalConfig.logOnStreamData && logOnData) {
        Log.d('🟢 onEvent: $event', time: DateTime.now(), name: name);
      }
    }).doOnCancel(() {
      if (GlobalConfig.logOnStreamCancel && logOnCancel) {
        Log.d('🟡 onCanceled', time: DateTime.now(), name: name);
      }
    }).doOnError((e, _) {
      if (GlobalConfig.logOnStreamError && logOnError) {
        Log.e('🔴 onError $e', time: DateTime.now(), name: name);
      }
    }).doOnDone(() {
      if (GlobalConfig.logOnStreamDone && logOnDone) {
        Log.d('☑️️ onCompleted', time: DateTime.now(), name: name);
      }
    });
  }
}
