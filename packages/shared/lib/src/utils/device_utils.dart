import 'dart:io';

import 'package:local_auth/local_auth.dart';
import 'package:webauthn/webauthn.dart';

import '../../shared.dart';

final double VERSION_IOS_USING_PASSKEY = 16.0;
final double VERSION_IOS_USING_SYSTEM_CHECK_AUTH_METHOD = 17.0;

Future<bool> checkAuthWithOldAPI(bool passkeyMigrated) async {
  if (Platform.isAndroid) return false;

  if (passkeyMigrated == false) {
    return true;
  }

  return await getDoubleOSVersion() < VERSION_IOS_USING_PASSKEY;
}

Future<bool> shouldSkipAuthenticate() async {
  if (!Platform.isIOS) return true;
  return await getDoubleOSVersion() >=
      VERSION_IOS_USING_SYSTEM_CHECK_AUTH_METHOD;
}

Future<bool> canAuthenticate() async {
  if (await shouldSkipAuthenticate()) return true;

  if (Platform.isAndroid ||
      await getDoubleOSVersion() > VERSION_IOS_USING_PASSKEY) {
    return Authenticator.isDeviceSupport;
  }

  return Authenticator.isDeviceSupport;
}

Future<bool> hasFaceId() async {
  final List<BiometricType> availableBiometrics =
      await LocalAuthentication().getAvailableBiometrics();
  return availableBiometrics.contains(BiometricType.face);
}

Future<bool> checkAuthWithSuggestUser() async {
  if (Platform.isAndroid) return true;

  return await getDoubleOSVersion() >= VERSION_IOS_USING_PASSKEY;
}
