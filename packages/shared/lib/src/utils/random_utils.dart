import 'dart:math';

import 'package:ulid/ulid.dart';
import 'package:uuid/uuid.dart';

class RandomUtils {
  /// Generates a random alphanumeric string of the given [length].
  /// Uses a secure random generator for better randomness.
  static String generateRandomString(int length) {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    return List.generate(length, (index) => chars[random.nextInt(chars.length)])
        .join();
  }

  /// Generates and returns a random unique identifier (UUID v4).
  static String randomId() {
    return Uuid().v4();
  }

  /// Generates and returns a random integer between 0 and 999999.
  /// Uses a secure random generator.
  static int randomInt() {
    final random = Random.secure();
    return random.nextInt(999999);
  }

  /// Generates and returns a unique ULID (Universally Unique Lexicographically Sortable Identifier).
  /// Uses the current UTC timestamp for generation.
  static String randomUlId() {
    return Ulid(millis: DateTime.now().toUtc().millisecondsSinceEpoch)
        .toString();
  }
}
