import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';

import '../../shared.dart';

class AppUpdateUtils {
  AppUpdateUtils._();

  static final BaseOptions baseOptions = BaseOptions(
    baseUrl: '',
    receiveDataWhenStatusError: true,
    validateStatus: (status) {
      return status != null && status < 1000;
    },
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    },
  );

  static Dio dio = Dio(baseOptions);

  static int versionStringToInt(String version) {
    return int.parse(version.split('.').join());
  }

  static Future<(AppUpdateType, int?, String?)> checkForUpdate() async {
    try {
      final bundleID = AppInfoUtils.packageName;
      final currentVersion = AppInfoUtils.version;
      final remoteConfig = FirebaseRemoteConfigService();
      for (int i = 0; i < 10; i++) {
        if (await remoteConfig.fetch()) {
          break;
        }
        await Future.delayed(Duration(seconds: 1));
      }

      baseOptions.baseUrl = UrlUtils.getAppStoreURL(bundleID);
      final response = await dio.get(
        baseOptions.baseUrl,
        options: Options(headers: baseOptions.headers),
      );

      if (response.statusCode != 200)
        return (AppUpdateType.noUpdate, null, null);

      final jsonData = json.decode(response.toString());
      final results = jsonData['results'] as List;
      if (results.isEmpty) return (AppUpdateType.noUpdate, null, null);

      final appId = results.first['trackId'] as int;
      final appStoreVersion = results.first['version'] as String;

      if (currentVersion == appStoreVersion) {
        return (AppUpdateType.noUpdate, null, appStoreVersion);
      }

      final appstoreForceUpdate =
          remoteConfig.getString("appstore_force_update_from");
      final testflightForceUpdate =
          remoteConfig.getString("testflight_force_update_from");
      final versionTestFlight =
          remoteConfig.getString("current_version_on_testflight");
      final versionAppstoreIntro =
          remoteConfig.getString("store_migrate_version");
      int versionCHPlayIntro =
          remoteConfig.getInt("chplay_migrate_build_number");
      final versionTestflightIntro =
          remoteConfig.getString("testflight_migrate_version");
      final appVersion = AppInfoUtils.version;

      if (canAppleStore(currentVersion, appStoreVersion)) {
        if (shouldForceUpdate(currentVersion, appstoreForceUpdate)) {
          return (AppUpdateType.immediate, appId, appStoreVersion);
        }
        if (shouldFlexibleUpdate(
          appstoreForceUpdate,
          currentVersion,
          appStoreVersion,
        )) {
          return (AppUpdateType.flexible, appId, appStoreVersion);
        }
      } else {
        if (shouldForceUpdate(currentVersion, testflightForceUpdate)) {
          return (AppUpdateType.immediate, appId, testflightForceUpdate);
        }
        if (shouldFlexibleUpdate(
          testflightForceUpdate,
          currentVersion,
          versionTestFlight,
        )) {
          return (AppUpdateType.flexible, appId, versionTestFlight);
        }
      }

      if ((Platform.isIOS &&
              (appVersion.compareTo(versionTestflightIntro) == 0 ||
                  appVersion.compareTo(versionAppstoreIntro) == 0)) ||
          (Platform.isAndroid &&
              AppInfoUtils.buildNumber
                      .compareTo(versionCHPlayIntro.toString()) ==
                  0)) {
        return (AppUpdateType.introduce, appId, null);
      }

      return (AppUpdateType.noUpdate, appId, null);
    } catch (_) {
      return (AppUpdateType.noUpdate, null, null);
    }
  }

  static bool canAppleStore(String begin, String last) {
    return isVersionSmaller(begin, last);
  }

  static bool shouldForceUpdate(String begin, String last) {
    return isVersionSmaller(begin, last);
  }

  static bool shouldFlexibleUpdate(String begin, String middle, String last) {
    return (isVersionSmaller(begin, middle) ||
            (begin.compareTo(middle) == 0)) &&
        isVersionSmaller(middle, last);
  }

  static bool isVersionSmaller(String current, String target) {
    final currentParts = current.split('.').map(int.parse).toList();
    final targetParts = target.split('.').map(int.parse).toList();

    final length = currentParts.length > targetParts.length
        ? currentParts.length
        : targetParts.length;

    while (currentParts.length < length) {
      currentParts.add(0);
    }
    while (targetParts.length < length) {
      targetParts.add(0);
    }

    for (int i = 0; i < length; i++) {
      if (currentParts[i] < targetParts[i]) return true;
      if (currentParts[i] > targetParts[i]) return false;
    }

    return false;
  }
}
