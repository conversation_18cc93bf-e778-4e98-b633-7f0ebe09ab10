import 'dart:io';

import 'package:mime/mime.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:rxdart/rxdart.dart';

import '../../shared.dart';

class FileUtils {
  FileUtils._();

  static final Map<String, String?> _imagePathCache = {};
  static final Map<String, String?> _videoPathCache = {};

  static const allowedImageFileType = [
    'image/jpeg',
    'image/png',
    'image/jpg',
    'image/svg',
    'image/svg+xml',
    'image/heic',
    'image/heif',
  ];

  ///video/quicktime is video recording on camera iphone
  static const allowedVideoFileType = [
    'video/mp4',
    'video/mov',
    'video/avi',
    'video/quicktime',
  ];
  static const supportedVideoExtensions = [
    '.mp4',
    '.mov',
    '.avi',
    '.mkv',
    '.webm',
    '.wmv',
    '.m4v',
    '.quicktime',
  ];

  static const imageCompressedExtensions = '.jpg';

  static String? getMimeType(String uriPath) {
    return lookupMimeType(uriPath);
  }

  static bool isValidImageType(String mimeType) {
    return allowedImageFileType.contains(mimeType);
  }

  static bool isValidVideoType(String mimeType) {
    return allowedVideoFileType.contains(mimeType);
  }

  static bool isSVG(String mimeType) {
    return [
      'image/svg',
      'image/svg+xml',
    ].contains(mimeType);
  }

  static String? _cacheDirPath;

  static Directory? _cacheDir;

  /// Call once during app startup
  static Future<void> init() async {
    if (_cacheDirPath != null) return;

    final dir = await getApplicationCacheDirectory();

    _cacheDir = Directory(dir.path + '/' + GlobalConfig.messageCacheDir);
    await _cacheDir?.create(recursive: true);

    _cacheDirPath = _cacheDir?.path;
  }

  static String get cacheDirPath {
    if (_cacheDirPath == null) {
      throw Exception(
        'FileUtils.init() must be called before accessing cacheDirPath',
      );
    }
    return _cacheDirPath!;
  }

  static Directory get cacheDir {
    if (_cacheDir == null) {
      throw Exception(
        'FileUtils.init() must be called before accessing cacheDirPath',
      );
    }
    return _cacheDir!;
  }

  static Directory getAttachmentDirectory({
    required String messageRef,
    required String fileRef,
  }) {
    if (_cacheDirPath == null) {
      throw Exception(
          'FileUtils.init() must be called before accessing cacheDirPath');
    }

    return Directory('$_cacheDirPath/$messageRef/$fileRef');
  }

// Trả về thư mục chứa ảnh đã nén
  static Directory getImageCompressedDirectory({
    required String messageRef,
    required String fileRef,
  }) {
    if (_cacheDirPath == null) {
      throw Exception(
          'FileUtils.init() must be called before accessing cacheDirPath');
    }

    return Directory('$_cacheDirPath/$messageRef/$fileRef/image_compressed');
  }

  static String getCompressedOutputPath({
    required String messageRef,
    required String fileRef,
    required String originalFilePath,
  }) {
    final compressedDir = getImageCompressedDirectory(
      messageRef: messageRef,
      fileRef: fileRef,
    );

    var fileName = path.basenameWithoutExtension(originalFilePath);
    fileName += imageCompressedExtensions;

    return path.join(compressedDir.path, fileName);
  }

  static Future<String?> getImagePathFromFileRef({
    required String messageRef,
    required String fileRef,
  }) async {
    final key = '$messageRef/$fileRef';
    if (_imagePathCache.containsKey(key)) {
      return _imagePathCache[key];
    }

    if (_cacheDirPath == null) await init();

    try {
      final folder = Directory('$cacheDirPath/$key');
      if (!await folder.exists()) {
        _imagePathCache[key] = null;
        return null;
      }

      final files = await folder.list().whereType<File>().toList()
        ..sort((a, b) => a.path.compareTo(b.path));

      final result = files.isEmpty ? null : files.first.path;
      _imagePathCache[key] = result;
      return result;
    } catch (e) {
      _imagePathCache[key] = null;
      return null;
    }
  }

  /// Gets the video file path from messageRef and fileRef
  /// Searches in the video subfolder: cacheDirPath/messageRef/fileRef/video/
  /// Returns the full path to the video file if found, null otherwise
  /// Uses cache mechanism to avoid repeated file system operations
  static Future<String?> getVideoPathFromFileRef({
    required String messageRef,
    required String fileRef,
  }) async {
    final key = '$messageRef/$fileRef/video';
    if (_videoPathCache.containsKey(key)) {
      return _videoPathCache[key];
    }

    if (_cacheDirPath == null) await init();

    try {
      final folder = Directory('$cacheDirPath/$messageRef/$fileRef/video');
      if (!await folder.exists()) {
        _videoPathCache[key] = null;
        return null;
      }

      final files = await folder.list().whereType<File>().toList()
        ..sort((a, b) => a.path.compareTo(b.path));

      final result = files.isEmpty ? null : files.first.path;
      _videoPathCache[key] = result;
      return result;
    } catch (e) {
      _videoPathCache[key] = null;
      return null;
    }
  }

  /// Gets the video thumbnail file path from messageRef and fileRef
  /// Searches in the thumbnail subfolder: cacheDirPath/messageRef/fileRef/thumbnail/
  /// Returns the full path to the thumbnail file if found, null otherwise
  /// Uses cache mechanism to avoid repeated file system operations
  static Future<String?> getVideoThumbnailPathFromFileRef({
    required String messageRef,
    required String fileRef,
  }) async {
    final key = '$messageRef/$fileRef/thumbnail';
    if (_videoPathCache.containsKey(key)) {
      return _videoPathCache[key];
    }

    if (_cacheDirPath == null) await init();

    try {
      final folder = Directory('$cacheDirPath/$messageRef/$fileRef/thumbnail');
      if (!await folder.exists()) {
        _videoPathCache[key] = null;
        return null;
      }

      final files = await folder.list().whereType<File>().toList()
        ..sort((a, b) => a.path.compareTo(b.path));

      final result = files.isEmpty ? null : files.first.path;
      _videoPathCache[key] = result;
      return result;
    } catch (e) {
      _videoPathCache[key] = null;
      return null;
    }
  }

  static void addImagePathCache({
    required String messageRef,
    required String fileRef,
    required String? path,
  }) {
    _imagePathCache['$messageRef/$fileRef'] = path;
  }

  static void clearImagePathCache({String? messageRef, String? fileRef}) {
    if (messageRef == null || fileRef == null) {
      _imagePathCache.clear();
      return;
    }
    _imagePathCache.remove('$messageRef/$fileRef');
  }

  /// Adds a video file path to the cache for faster subsequent access
  /// Used to cache the result of video file path lookups
  /// Set isVideoFile to true for video files, false for thumbnail files
  static void addVideoPathCache({
    required String messageRef,
    required String fileRef,
    required String? path,
    bool isVideoFile = true,
  }) {
    final suffix = isVideoFile ? '/video' : '/thumbnail';
    _videoPathCache['$messageRef/$fileRef$suffix'] = path;
  }

  /// Clears video path cache entries
  /// If messageRef and fileRef are provided, removes entries for both video and thumbnail
  /// If either is null, clears the entire video cache
  static void clearVideoPathCache({String? messageRef, String? fileRef}) {
    if (messageRef == null || fileRef == null) {
      _videoPathCache.clear();
      return;
    }
    // Remove both video and thumbnail cache entries
    _videoPathCache.remove('$messageRef/$fileRef/video');
    _videoPathCache.remove('$messageRef/$fileRef/thumbnail');
  }

  /// Adds a video thumbnail file path to the cache for faster subsequent access
  /// Convenience method specifically for thumbnail files
  static void addVideoThumbnailPathCache({
    required String messageRef,
    required String fileRef,
    required String? path,
  }) {
    addVideoPathCache(
      messageRef: messageRef,
      fileRef: fileRef,
      path: path,
      isVideoFile: false,
    );
  }

  /// Clears only video thumbnail cache entries
  /// If messageRef and fileRef are provided, removes only the thumbnail entry
  /// If either is null, clears the entire video cache
  static void clearVideoThumbnailPathCache({
    String? messageRef,
    String? fileRef,
  }) {
    if (messageRef == null || fileRef == null) {
      _videoPathCache.clear();
      return;
    }
    _videoPathCache.remove('$messageRef/$fileRef/thumbnail');
  }

  // ============================================================================
  // CONSOLIDATED VIDEO FILE HANDLING METHODS
  // ============================================================================

  /// Unified method to load video and thumbnail paths with download capability
  /// Handles cache loading, downloading from URLs, and proper fallback mechanisms
  /// Returns Map<String, String?> with 'videoPath' and 'thumbnailPath' keys
  static Future<Map<String, String?>> loadVideoAndThumbnailPaths({
    required String messageRef,
    required String fileRef,
    required String? fileUrl,
    required String? thumbnailUrl,
    required String? fallbackFilePath,
    String? filename,
  }) async {
    try {
      // Get video file path from cache using FileUtils
      String? videoPath = await getVideoPathFromFileRef(
        messageRef: messageRef,
        fileRef: fileRef,
      );

      // Get thumbnail file path from cache using FileUtils
      String? thumbnailPath = await getVideoThumbnailPathFromFileRef(
        messageRef: messageRef,
        fileRef: fileRef,
      );

      // If video file not found in cache and URL is available, try to download
      if (videoPath == null && fileUrl != null && fileUrl.isNotEmpty) {
        videoPath = await downloadAndCacheVideoFile(
          messageRef: messageRef,
          fileRef: fileRef,
          fileUrl: fileUrl,
          filename: filename,
          fallbackFilePath: fallbackFilePath,
        );
      }

      // If thumbnail not found in cache and thumbnail URL is available, try to download
      if (thumbnailPath == null &&
          thumbnailUrl != null &&
          thumbnailUrl.isNotEmpty) {
        thumbnailPath = await downloadAndCacheThumbnailFile(
          messageRef: messageRef,
          fileRef: fileRef,
          thumbnailUrl: thumbnailUrl,
        );
      }

      return {
        'videoPath': videoPath ?? fallbackFilePath,
        // Fallback to original path
        'thumbnailPath': thumbnailPath,
      };
    } catch (e) {
      // Error handling: fallback to original paths
      return {
        'videoPath': fallbackFilePath,
        'thumbnailPath': null,
      };
    }
  }

  /// Download and cache video file from URL with proper filename resolution
  /// Handles URLs without extensions by using provided filename
  static Future<String?> downloadAndCacheVideoFile({
    required String messageRef,
    required String fileRef,
    required String fileUrl,
    String? filename,
    String? fallbackFilePath,
  }) async {
    try {
      final videoUrl = UrlUtils.parseCDNUrl(fileUrl);

      // Check if URL has valid extension
      final urlExtension = path.extension(videoUrl);
      final hasValidExtension = urlExtension.isNotEmpty && urlExtension != '.';

      // If URL doesn't have extension and no filename provided, fallback
      if (!hasValidExtension && (filename == null || filename.isEmpty)) {
        return fallbackFilePath;
      }

      // Download file using AppCacheManager
      final downloadedFile = await AppCacheManager().getFile(videoUrl);

      // Determine target filename
      String targetFileName;
      if (filename != null && filename.isNotEmpty) {
        targetFileName = filename;
      } else {
        // Extract filename from URL or generate one
        final urlFileName = path.basename(videoUrl);
        targetFileName =
            urlFileName.isNotEmpty ? urlFileName : 'video_${fileRef}.mp4';
      }

      // Create target directory structure: cacheDirPath/messageRef/fileRef/video/
      final targetDir = Directory('$cacheDirPath/$messageRef/$fileRef/video');
      await targetDir.create(recursive: true);

      // Copy downloaded file to target location with proper filename
      final targetPath = path.join(targetDir.path, targetFileName);
      final targetFile = File(targetPath);
      await downloadedFile.copy(targetFile.path);

      // Update cache with new path
      addVideoPathCache(
        messageRef: messageRef,
        fileRef: fileRef,
        path: targetFile.path,
        isVideoFile: true,
      );

      return targetFile.path;
    } catch (e) {
      // Download failed, return null to use fallback
      return null;
    }
  }

  /// Download and cache thumbnail file from URL
  static Future<String?> downloadAndCacheThumbnailFile({
    required String messageRef,
    required String fileRef,
    required String thumbnailUrl,
  }) async {
    try {
      final parsedThumbnailUrl = UrlUtils.parseCDNUrl(thumbnailUrl);

      // Download thumbnail using AppCacheManager
      final downloadedFile =
          await AppCacheManager().getFile(parsedThumbnailUrl);

      // Extract filename from thumbnail URL
      final urlFileName = path.basename(parsedThumbnailUrl);
      final targetFileName =
          urlFileName.isNotEmpty ? urlFileName : 'thumbnail_${fileRef}.jpg';

      // Create target directory structure: cacheDirPath/messageRef/fileRef/thumbnail/
      final targetDir =
          Directory('$cacheDirPath/$messageRef/$fileRef/thumbnail');
      await targetDir.create(recursive: true);

      // Copy downloaded file to target location
      final targetPath = path.join(targetDir.path, targetFileName);
      final targetFile = File(targetPath);
      await downloadedFile.copy(targetFile.path);

      // Update cache with new path
      addVideoThumbnailPathCache(
        messageRef: messageRef,
        fileRef: fileRef,
        path: targetFile.path,
      );

      return targetFile.path;
    } catch (e) {
      // Download failed, return null
      return null;
    }
  }

  /// Load video and thumbnail paths from cache only (no download)
  /// Used for widgets that only need cached files without download capability
  /// Returns Map<String, String?> with 'videoPath', 'thumbnailPath', and optionally 'thumbnailUrl'
  static Future<Map<String, String?>> loadVideoAndThumbnailPathsFromCache({
    required String messageRef,
    required String fileRef,
    required String? fallbackFilePath,
    String? thumbnailUrl,
    bool includeThumbnailUrl = false,
  }) async {
    try {
      // Get video file path from cache using FileUtils
      final videoPath = await getVideoPathFromFileRef(
            messageRef: messageRef,
            fileRef: fileRef,
          ) ??
          fallbackFilePath; // Fallback to original path if not found in cache

      // Get thumbnail file path from cache using FileUtils
      final thumbnailPath = await getVideoThumbnailPathFromFileRef(
            messageRef: messageRef,
            fileRef: fileRef,
          ) ??
          fallbackFilePath; // Fallback to video path for ZiiShort

      final result = <String, String?>{
        'videoPath': videoPath,
        'thumbnailPath': thumbnailPath,
      };

      // Add thumbnail URL if requested
      if (includeThumbnailUrl) {
        final parsedThumbnailUrl =
            thumbnailUrl != null && thumbnailUrl.isNotEmpty
                ? UrlUtils.parseCDNUrl(thumbnailUrl)
                : null;
        result['thumbnailUrl'] = parsedThumbnailUrl;
      }

      return result;
    } catch (e) {
      // Error handling: fallback to original paths
      final result = <String, String?>{
        'videoPath': fallbackFilePath,
        'thumbnailPath': fallbackFilePath,
      };

      if (includeThumbnailUrl) {
        final parsedThumbnailUrl =
            thumbnailUrl != null && thumbnailUrl.isNotEmpty
                ? UrlUtils.parseCDNUrl(thumbnailUrl)
                : null;
        result['thumbnailUrl'] = parsedThumbnailUrl;
      }

      return result;
    }
  }
}
