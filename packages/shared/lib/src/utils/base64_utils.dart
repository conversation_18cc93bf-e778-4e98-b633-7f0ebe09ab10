import 'dart:convert';
import 'dart:typed_data';

import 'package:json_annotation/json_annotation.dart';

class Uint8ListConverter implements JsonConverter<Uint8List, String> {
  const Uint8ListConverter();

  @override
  Uint8List from<PERSON>son(String json) => b64d(json);

  @override
  String toJson(Uint8List object) => b64e(object);
}

String padBase64(String b64) {
  final padding = 4 - b64.length % 4;
  return padding < 4 ? '$b64${"=" * padding}' : b64;
}

/// Decode a Base64 URL encoded string adding in any required '='
Uint8List b64d(String b64) => base64Url.decode(padBase64(b64));

/// Encode a byte list into Base64 URL encoding, stripping any trailing '='
String b64e(List<int> bytes) => base64Url.encode(bytes).replaceAll('=', '');

String decodeBase64ToString(String base64EncodedString) {
  try {
    Uint8List decodedBytes = base64.decode(base64EncodedString);
    return String.fromCharCodes(
      decodedBytes,
    );
  } catch (e) {
    return '';
  }
}
