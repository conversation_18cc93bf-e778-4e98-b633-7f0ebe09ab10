class SendAttachmentFailureResponse {
  final bool isSendAttachmentFailure;
  final String ref; // message ref
  final String attachmentRef; // attachment ref
  final String? errorMessage;
  SendAttachmentFailureResponse({
    required this.ref,
    required this.attachmentRef,
    this.errorMessage,
    this.isSendAttachmentFailure = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'ref': ref,
      'attachmentRef': attachmentRef,
      'isSendAttachmentFailure': isSendAttachmentFailure,
      'errorMessage': errorMessage,
    };
  }
}
