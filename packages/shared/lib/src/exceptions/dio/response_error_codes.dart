/*
* Author: NMTuong
* Created Date: 24/03/2025
* repository: https://github.com/halomeapis/halome-error-codes/blob/main/src/errors.json
* */
enum ResponseErrorCode {
  failedToGetUser(7000),
  failedToReportUser(7050),
  failedToUpdateUserAvatar(7051),
  failedToUpdateUserDisplayName(7052),
  failedToGenerateUserConnectLink(7053),
  failedToDecodeUserConnectLink(7054),
  failedToCreateUserDataRecord(7055),
  failedToRemoveUserDataRecord(7056),
  failedToBlockUser(7057),
  failedToUnblockUser(7058),
  failedToGetListBlockedUsers(7059),
  failedToSearchUsers(7060),
  failedToUpdateRecoveryCodeSetting(7061),
  failedToUpdateSmartOtpSetting(7062),
  failedToUpdate<PERSON>ser<PERSON><PERSON>(7063),
  failedToUpdateUserEmail(7064),
  failedToUpdateMediaPermissionSetting(7065),
  thisUserHasBeenRemoved(7100),
  thisUserHasBeenBanned(7101),
  failedToGetPrivateUserData(7102),
  failedToAddUserStatus(7103),
  failedToUpdateUserStatus(7104),
  failedToDeleteUserStatus(7105),
  failedToListUserStatus(7106),
  failedToListUserViewedProfile(7107),
  failedToCreateRingBackTone(7108),
  failedToRenameRingBackTone(7109),
  failedToDeleteRingBackTone(7110),
  failedToSetRingBackTone(7111),
  failedToListRingBackTones(7112),
  failedToGetRingBackTone(7113),
  failedToCreateCoverPhoto(7114),
  failedToUpdateCoverPhoto(7115),
  failedToDeleteCoverPhoto(7116),
  failedToDeleteUserAvatar(7117),
  failedToUpdateUserVideoAvatar(7118),
  failedToDeleteUserVideoAvatar(7119),
  failedToCreateAvatarFrame(7120),
  failedToDeleteAvatarFrame(7121),
  failedToUploadDecoratedAvatar(7122),
  failedToGetAvatarFrame(7123),
  failedToListAvatarFrame(7124),
  failedToRemoveDecoratedAvatar(7125),
  failedToUpdateUserScopeForCall(7126),
  failedToUpdateUserScopeForMessage(7127),
  failedToListPrivateUserData(7128),
  failedToVisitedProfile(7129),
  failedToDeleteUserVisitedProfile(7130),
  failedToClearUserVisitedProfileNotification(7131),
  failedToCreateCollection(6100),
  failedToUpdateCollection(6101),
  failedToDeleteCollection(6102),
  failedToGetCollection(6103),
  failedToGetListCollections(6104),
  failedToCreateSticker(6105),
  failedToUpdateSticker(6106),
  failedToDeleteSticker(6107),
  failedToGetSticker(6108),
  failedToGetListStickers(6109),
  failedToSearchStickers(6110),
  failedToSignNewToken(4010),
  failedToTerminateToken(4011),
  failedToRefreshToken(4012),
  failedToInitiateChannelDefaultRoles(4020),
  failedToAssignChannelRoles(4021),
  failedToRevokeChannelRoles(4022),
  failedToAssignChannelPermissions(4023),
  failedToRevokeChannelPermissions(4024),
  failedToRevokeAllChannelPermissions(4025),
  failedToCreateNewWebsocketTicket(4030),
  failedToRevokeAllUserRoles(4031),
  failedToAllowDirectUserAccess(4032),
  failedToBlockDirectUserAccess(4033),
  failedToDeletedDirectUserAccessData(4034),
  failedToInitiateDirectUserAccessData(4035),
  failedToGetDirectUserAccessDeniedList(4036),
  failedToTurnOnGlobalNotification(5000),
  failedToTurnOffGlobalNotification(5001),
  failedToListChannelStates(5002),
  failedToSubscribeChannelNotification(5003),
  failedToUnsubscribeChannelNotification(5004),
  failedToSubscribeNotificationsForDevice(5005),
  failedToUnsubscribeNotificationsForDevice(5006),
  failedToRegisterVoIpToken(5007),
  failedToCreateMessage(6000),
  failedToUpdateMessage(6001),
  failedToDeleteMessage(6002),
  failedToGetMessage(6003),
  failedToReplyMessage(6004),
  failedToAddMessageReaction(6005),
  failedToRevokeMessageReaction(6006),
  failedToGetReaction(6007),
  failedToSendAttachmentMessage(6008),
  failedToForwardMessage(6009),
  failedToReportMessage(6010),
  failedToDeleteMessages(6011),
  failedToUpdateAttachment(6012),
  failedToSendSticker(6013),
  failedToQuoteMessage(6014),
  theTimeLimitForPermissionHasBeenExceeded(6015),
  failedToTranscribeSpeechToText(6016),
  failedToTranslateText(6017),
  failedToTranscribeTextToSpeech(6018),
  failedToSendLocationMessage(6019),
  failedToSendPokeMessage(6020),
  failedToPinMessage(6021),
  failedToUnpinMessage(6022),
  reachedMaximumMessageLimitForStrangers(6023),
  failedToMarkAsRead(6024),
  failedToMarkAllChannelsAsRead(6025),
  failedToCreateMember(3100),
  failedToUpdateMember(3101),
  failedToDeleteMember(3102),
  failedToGetMember(3103),
  failedToLeaveChannel(3104),
  failedToRemoveFromChannel(3105),
  failedToAssignAsAdmin(3106),
  failedToDismissAsAdmin(3107),
  failedToTransferOwnershipChannel(3108),
  failedToBanMember(3109),
  failedToUnbanMember(3110),
  failedToBanUser(9010),
  failedToUnbanUser(9011),
  failedToCreateInvitation(3200),
  failedToUpdateInvitation(3201),
  failedToDeleteInvitation(3202),
  failedToGetInvitation(3203),
  failedToSendInvitation(3204),
  failedToAcceptInvitation(3205),
  failedToGetListInvitableUsers(3206),
  failedToInitiateUserKeyFlow(2000),
  failedToProcessUserKeyLoginRequest(2001),
  failedToProcessUserKeyRegisterRequest(2002),
  failedToInitiateSmartOtpFlow(2010),
  failedToVerifySmartOtpRequest(2011),
  failedToProcessSmartOtpLoginRequest(2012),
  failedToGetPowChallenge(2020),
  failedToInitiateAccountDeletionFlow(2030),
  failedToConfirmAccountDeletion(2031),
  failedToInitiateRecoveryCodeGenerationFlow(2032),
  failedToConfirmRecoveryCodeGenerationRequest(2033),
  failedToInitiateRecoveryAccountFlow(2034),
  failedToRecoverAccount(2035),
  failedToInitiateQrAuthenticationFlow(2036),
  failedToVerifyQrAuthenticationRequest(2037),
  failedToProcessQrLoginRequest(2038),
  failedToGetQrAuthenticationState(2039),
  failedToGenerateQrAuthenticationCode(2040),
  failedToLogoutAccount(2041),
  failedToListAllActiveSessions(2042),
  failedToTerminateAllSessions(2043),
  failedToTerminateSession(2044),
  failedToRenewQrAuthenticationCode(2045),
  thisUsernameIsInvalid(2046),
  failedToInitiateGenerateSecurityKeyFlow(2047),
  failedToGenerateSecurityKey(2048),
  failedToInitiateAccountDeletionBySecurityKeyFlow(2049),
  failedToConfirmAccountDeletionBySecurityKey(2050),
  failedToInitiateViewSecurityKeyFlow(2051),
  failedToConfirmViewSecurityKey(2052),
  thisUsernameIsTemporarilyRequestedByOtherUser(2053),
  failedToCheckMigratePasskeyStatus(2054),
  failedToMigratePasskey(2055),
  failedToProcessMigratePasskeyRequest(2056),
  failedToProcessCreateNewCredential(2057),
  failedToProcessTokenExchange(2058),
  failedToProcessCancellationRequest(2059),
  failedToAddNewFriend(3300),
  failedToUnfriend(3301),
  failedToAcceptFriendRequest(3302),
  failedToCancelFriendRequest(3303),
  failedToDeleteFriendRequest(3304),
  failedToGetFriendRequest(3305),
  failedToGetFriend(3306),
  failedToMarkAllAsReadFriendRequest(3307),
  failedToSearchFriends(3308),
  failedToUploadFile(8000),
  failedToGetFileMetadata(8001),
  failedToDeleteFile(8002),
  failedToInitiateMultipartUpload(8010),
  failedToUploadPart(8011),
  failedToCompleteMultipartUpload(8012),
  failedToInitiateFileCollection(8020),
  failedToCompleteFileCollection(8021),
  failedToCreateMockChannels(1800),
  failedToDeleteMockedChannels(1801),
  failedToCreateMockMessages(1802),
  failedToMockUsersData(1803),
  failedToDeleteMockedUsers(1804),
  failedToCrawlUrls(1100),
  failedToCrawlLocation(1101),
  invalidArgument(1000),
  failedToOpenWebsocketConnection(1200),
  failedToCreateChannel(3000),
  failedToUpdateChannel(3001),
  failedToDeleteChannel(3002),
  failedToGetChannel(3003),
  failedToTypingInChannel(3004),
  failedToResendMessageRequest(3005),
  failedToAcceptMessageRequest(3006),
  failedToRejectMessageRequest(3007),
  failedToGetIncomingMessageRequests(3008),
  failedToGetOutgoingMessageRequests(3009),
  failedToSearchChannels(3010),
  failedToDeleteChannelAvatar(3011),
  failedToCreateCall(10000);

  final int code;

  const ResponseErrorCode(this.code);

  static bool containsCode(int code) {
    return ResponseErrorCode.values.any((errorCode) => errorCode.code == code);
  }

  static ResponseErrorCode? getCodeByValue(int code) {
    return ResponseErrorCode.values.firstWhere(
      (errorCode) => errorCode.code == code,
      orElse: () => throw ArgumentError('Invalid code'),
    );
  }

  static int getValueByCode(ResponseErrorCode errorCode) {
    return errorCode.code;
  }
}
