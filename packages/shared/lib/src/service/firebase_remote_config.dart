import 'package:firebase_remote_config/firebase_remote_config.dart';

import '../../shared.dart';

class FirebaseRemoteConfigService {
  FirebaseRemoteConfigService._()
      : _remoteConfig = FirebaseRemoteConfig.instance;

  static FirebaseRemoteConfigService? _instance;

  factory FirebaseRemoteConfigService() =>
      _instance ??= FirebaseRemoteConfigService._();

  final FirebaseRemoteConfig _remoteConfig;

  String getString(String key) => _remoteConfig.getString(key);

  bool getBool(String key) => _remoteConfig.getBool(key);

  int getInt(String key) => _remoteConfig.getInt(key);

  double getDouble(String key) => _remoteConfig.getDouble(key);

  Future<void> setup() async {
    try {
      await _remoteConfig.setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: const Duration(microseconds: 0),
        ),
      );
      await _remoteConfig.fetchAndActivate();
    } catch (error) {
      print("FirebaseRemoteConfigService:${error.toString()}");

    }
  }

  Future<bool> fetch() async {
    try {
     var result = await _remoteConfig.fetchAndActivate();
     return result;
    } catch (error) {
      print("FirebaseRemoteConfigService:${error.toString()}");
      return true;
    }
  }
}
