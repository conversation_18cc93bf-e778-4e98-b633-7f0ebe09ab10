import 'dart:async';
import 'dart:convert';
import 'dart:developer' as dev;
import 'dart:typed_data';

import '../../shared.dart';

class Log {
  const Log._();

  static const _enableLog = GlobalConfig.enableGeneralLog;

  static const _sensitiveFields = <String>[
    // 'cacheData',
    // 'firstframe',
  ];

  static void d(
    Object? message, {
    String? name,
    DateTime? time,
  }) {
    _log(
      _formatMessage(message),
      name: name ?? '',
      time: time,
    );
  }

  static void e(
    Object? errorMessage, {
    String? name,
    Object? errorObject,
    StackTrace? stackTrace,
    DateTime? time,
  }) {
    _log(
      '❌ ${_formatMessage(errorMessage)}',
      name: name ?? '',
      error: _formatError(errorObject),
      stackTrace: stackTrace,
      time: time,
    );
  }

  static void api(
    Object? message, {
    String? name,
    DateTime? time,
    Object? requestData,
    Object? responseData,
    int? statusCode,
  }) {
    final logMessage = StringBuffer()
      ..writeln('🌐')
      ..writeln(_formatMessage(message));
    if (requestData != null) {
      logMessage.writeln('Request Data: ${_formatMessage(requestData)}');
    }
    if (responseData != null) {
      logMessage.writeln('Response Data: ${_formatMessage(responseData)}');
    }
    if (statusCode != null) {
      logMessage.writeln('Status Code: $statusCode');
    }

    _log(
      logMessage.toString(),
      name: name ?? 'API',
      time: time,
    );
  }

  static void ws(
    Object? message, {
    String? name,
  }) {
    final logMessage = StringBuffer()
      ..writeln('⚡')
      ..writeln(_formatMessage(message));
    _log(
      logMessage.toString(),
      name: name ?? 'Websocket',
      time: DateTime.now(),
    );
  }

  static String prettyJson(Map<String, dynamic> json) {
    if (!GlobalConfig.isPrettyJson) {
      return json.toString();
    }

    final maskedJson = _maskData(json);

    final indent = '  ' * 2;
    final encoder = JsonEncoder.withIndent(indent);
    return encoder.convert(maskedJson);
  }

  static String _formatMessage(Object? message) {
    if (message is Map<String, dynamic>) {
      return prettyJson(message);
    }
    return message.toString();
  }

  static Object? _formatError(Object? error) {
    if (error is Map<String, dynamic>) {
      return prettyJson(error);
    }
    return error;
  }

  static void _log(
    String message, {
    int level = 0,
    String name = '',
    DateTime? time,
    int? sequenceNumber,
    Zone? zone,
    Object? error,
    StackTrace? stackTrace,
  }) {
    if (_enableLog) {
      dev.log(
        message,
        name: name,
        time: time,
        sequenceNumber: sequenceNumber,
        level: level,
        zone: zone,
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  static Map<String, dynamic> _maskData(Map<String, dynamic> json) {
    final result = <String, dynamic>{};

    json.forEach((key, value) {
      if (_sensitiveFields.contains(key)) {
        result[key] = '***';
      } else if (value is Uint8List) {
        result[key] = 'Instance of ${value.runtimeType}';
      } else if (value is Map) {
        result[key] = _maskData(Map<String, dynamic>.from(value));
      } else if (value is List) {
        result[key] = value.map((e) {
          if (e is Map) {
            return _maskData(Map<String, dynamic>.from(e));
          } else if (e is Uint8List) {
            return 'Instance of ${e.runtimeType}';
          }
          return e;
        }).toList();
      } else {
        result[key] = value;
      }
    });

    return result;
  }
}
