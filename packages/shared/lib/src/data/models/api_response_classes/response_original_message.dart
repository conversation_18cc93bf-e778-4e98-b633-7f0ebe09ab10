import 'package:json_annotation/json_annotation.dart';

import 'response_media_attachment.dart';

part 'response_original_message.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseOriginalMessage {
  final String? messageId;
  final String? content;
  final int? attachmentType;
  final ResponseMediaAttachment? mediaAttachments;
  final int? messageType;
  final String? contentLocale;
  final List<String>? contentArguments;
  final String? userId;
  final String? editTime;
  final String? createTime;
  final String? updateTime;

  ResponseOriginalMessage({
    this.messageId,
    this.content,
    this.attachmentType,
    this.mediaAttachments,
    this.messageType,
    this.contentLocale,
    this.contentArguments,
    this.userId,
    this.editTime,
    this.createTime,
    this.updateTime,
  });

  factory ResponseOriginalMessage.fromJson(Map<String, dynamic> json) =>
      _$ResponseOriginalMessageFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseOriginalMessageToJson(this);
}
