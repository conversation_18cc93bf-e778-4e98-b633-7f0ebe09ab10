import 'package:json_annotation/json_annotation.dart';

import '../../../../shared.dart';

part 'response_message.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseMessage {
  final String workspaceId;
  final String channelId;
  final String messageId;
  final String userId;
  final String? content;
  final String? ref;
  final int? messageType;
  final int? messageStatus;
  final ResponseOriginalMessage? originalMessage;
  final Map<String, ResponseReactionData>? reactions;
  final List<String>? mentions;
  final List<ResponseEmbed>? embed;
  final int? attachmentType;
  final List<ResponseReport>? reports;
  final bool? isThread;
  final int? reportCount;
  final bool? isReported;
  final int? attachmentCount;
  final List<ResponseMediaAttachment>? mediaAttachments;
  final String? contentLocale;
  final List<String>? contentArguments;
  final List<ResponseMentionIdentification>? dataMentions;
  final String? editTime;
  final String? createTime;
  final String? updateTime;
  final bool? isPinned;
  final String? pinTime;

  ResponseMessage({
    required this.workspaceId,
    required this.channelId,
    required this.messageId,
    required this.userId,
    this.content,
    this.ref,
    this.messageType,
    this.messageStatus,
    this.originalMessage,
    this.reactions,
    this.mentions,
    this.embed,
    this.attachmentType,
    this.reports,
    this.isThread,
    this.reportCount,
    this.isReported,
    this.attachmentCount,
    this.mediaAttachments,
    this.contentLocale,
    this.contentArguments,
    this.dataMentions,
    this.editTime,
    this.createTime,
    this.updateTime,
    this.isPinned,
    this.pinTime,
  });

  factory ResponseMessage.fromJson(Map<String, dynamic> json) =>
      _$ResponseMessageFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseMessageToJson(this);
}
