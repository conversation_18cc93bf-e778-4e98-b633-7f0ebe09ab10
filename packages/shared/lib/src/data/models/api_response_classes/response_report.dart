import 'package:json_annotation/json_annotation.dart';

part 'response_report.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseReport {
  @Json<PERSON>ey(name: 'reportCategory')
  int? reportCategory;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'pretendingTo')
  int? pretendingTo;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'reportReason')
  String? reportReason;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'reportBy')
  String? reportBy;

  @JsonKey(name: 'reportTime')
  String? reportTime;

  ResponseReport({
    this.reportCategory,
    this.pretendingTo,
    this.reportReason,
    this.reportBy,
    this.reportTime,
  });

  factory ResponseReport.fromJson(Map<String, dynamic> json) =>
      _$ResponseReportFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseReportToJson(this);
}
