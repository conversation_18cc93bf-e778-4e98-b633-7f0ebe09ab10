import 'package:json_annotation/json_annotation.dart';

part 'response_status_data.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseStatusData {
  final String? content;
  final String? status;
  final String? createTime;
  final String? updateTime;
  final String? endTime;
  final int? expireAfterTime;

  ResponseStatusData({
    this.content,
    this.status,
    this.createTime,
    this.updateTime,
    this.endTime,
    this.expireAfterTime,
  });

  factory ResponseStatusData.fromJson(Map<String, dynamic> json) =>
      _$ResponseStatusDataFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseStatusDataToJson(this);
}
