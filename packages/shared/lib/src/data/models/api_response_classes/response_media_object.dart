import 'package:json_annotation/json_annotation.dart';

import 'response_audio_metadata.dart';
import 'response_file_metadata.dart';
import 'response_layout_metadata.dart';

part 'response_media_object.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseMediaObject {
  @Json<PERSON>ey(name: 'fileId')
  String? fileId;

  @Json<PERSON>ey(name: 'attachmentType')
  int? attachmentType;

  @Json<PERSON>ey(name: 'fileUrl')
  String? fileUrl;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'fileMetadata')
  ResponseFileMetadata? fileMetadata;

  @<PERSON><PERSON><PERSON>ey(name: 'fileStatus')
  int? fileStatus;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'thumbnailUrl')
  String? thumbnailUrl;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'thumbnailMetadata')
  ResponseFileMetadata? thumbnailMetadata;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'layoutMetadata')
  ResponseLayoutMetadata? layoutMetadata;

  @Json<PERSON>ey(name: 'audioMetadata')
  ResponseAudioMetadata? audioMetadata;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'fileRef')
  String? fileRef;

  @Json<PERSON><PERSON>(name: 'attachmentId')
  String? attachmentId;

  @J<PERSON><PERSON><PERSON>(name: 'blob')
  String? blob;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'collectionId')
  String? collectionId;

  @JsonKey(name: 'channelId')
  String? channelId;

  @JsonKey(name: 'userId')
  String? userId;

  @JsonKey(name: 'messageId')
  String? messageId;

  ResponseMediaObject({
    this.fileId,
    this.attachmentType,
    this.fileUrl,
    this.fileMetadata,
    this.fileStatus,
    this.thumbnailUrl,
    this.thumbnailMetadata,
    this.layoutMetadata,
    this.audioMetadata,
    this.fileRef,
    this.attachmentId,
    this.blob,
    this.collectionId,
    this.channelId,
    this.userId,
    this.messageId,
  });

  factory ResponseMediaObject.fromJson(Map<String, dynamic> json) =>
      _$ResponseMediaObjectFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseMediaObjectToJson(this);
}
