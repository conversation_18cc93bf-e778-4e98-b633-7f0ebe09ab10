import 'package:json_annotation/json_annotation.dart';

import '../../../../shared.dart';

part 'ws_data.g.dart';

@JsonSerializable(explicitToJson: true)
class WSData {
  final ResponseMessage? message;
  final ResponseChannel? channel;
  final ResponseUser? user;
  final ResponseUser? userData;
  final ResponseMember? member;
  final ResponseFriend? friend;
  final ResponseFriendRequest? friendRequest;
  final ResponseStatusData? statusData;
  final List<ResponseMessage>? messages;
  final List<ResponseChannel>? channels;
  final List<ResponseUser>? users;
  final List<ResponseMember>? members;
  final List<ResponseFriend>? friends;
  final ResponseIncludes? includes;

  //*Channel
  final String? workspaceId;
  final String? channelId;
  final String? lastSeenMessageId;
  final String? name;

  //Member, Unfriend
  final String? actorId;
  final String? targetUserId;
  final String? role;
  final String? nickname;
  final String? userId;

  //update avatar
  final String? avatar;
  final int? avatarType;

  //update cover
  final String? cover;

  final dynamic reactions;
  final String? messageId;
  final List<String>? messageIds;
  final String? createTime;
  final String? updateTime;
  final String? pinTime;
  final bool? isPinned;

  WSData({
    this.message,
    this.channel,
    this.user,
    this.userData,
    this.member,
    this.statusData,
    this.friend,
    this.friendRequest,
    this.messages,
    this.channels,
    this.users,
    this.members,
    this.friends,
    this.includes,
    this.workspaceId,
    this.channelId,
    this.lastSeenMessageId,
    this.name,
    this.role,
    this.actorId,
    this.targetUserId,
    this.nickname,
    this.userId,
    this.avatar,
    this.cover,
    this.avatarType,
    this.reactions,
    this.messageId,
    this.messageIds,
    this.createTime,
    this.updateTime,
    this.isPinned,
    this.pinTime,
  });

  factory WSData.fromJson(Map<String, dynamic> json) => _$WSDataFromJson(json);

  Map<String, dynamic> toJson() => _$WSDataToJson(this);
}

extension WSDataExtension on WSData {
  bool get hasUser => user != null;

  bool get hasChannel => channel != null;

  bool get hasMember => member != null;

  bool get hasMessage => message != null;

  bool get hasUsers => users?.isNotEmpty ?? false;

  bool get hasChannels => channels?.isNotEmpty ?? false;

  bool get hasMembers => members?.isNotEmpty ?? false;

  bool get hasMessages => messages?.isNotEmpty ?? false;

  bool get hasIncludes => includes != null;

  ResponseUser? getUser() => user;

  ResponseChannel? getChannel() => channel;

  ResponseMember? getMember() => member;

  ResponseMessage? getMessage() => message;

  List<ResponseUser>? getUsers() => users;

  List<ResponseChannel>? getChannels() => channels;

  List<ResponseMember>? getMembers() => members;

  List<ResponseMessage>? getMessages() => messages;

  ResponseUser? findUserById(String id) =>
      users?.firstWhere((user) => user.userId == id, orElse: null);

  ResponseChannel? findChannelById(String id) =>
      channels?.firstWhere((channel) => channel.channelId == id, orElse: null);

  ResponseMember? findMemberById(String id) =>
      members?.firstWhere((member) => member.userId == id, orElse: null);

  ResponseMessage? findMessageById(String id) =>
      messages?.firstWhere((message) => message.messageId == id, orElse: null);

  static List<ResponseUser> usersFromJson(List<dynamic> json) => json
      .map((e) => ResponseUser.fromJson(e as Map<String, dynamic>))
      .toList();

  static List<Map<String, dynamic>> usersToJson(List<ResponseUser> users) =>
      users.map((e) => e.toJson()).toList();

  static List<ResponseChannel> channelsFromJson(List<dynamic> json) => json
      .map((e) => ResponseChannel.fromJson(e as Map<String, dynamic>))
      .toList();

  static List<Map<String, dynamic>> channelsToJson(
    List<ResponseChannel> channels,
  ) =>
      channels.map((e) => e.toJson()).toList();

  static List<ResponseMember> membersFromJson(List<dynamic> json) => json
      .map((e) => ResponseMember.fromJson(e as Map<String, dynamic>))
      .toList();

  static List<Map<String, dynamic>> membersToJson(
    List<ResponseMember> members,
  ) =>
      members.map((e) => e.toJson()).toList();

  static List<ResponseMessage> messagesFromJson(List<dynamic> json) => json
      .map((e) => ResponseMessage.fromJson(e as Map<String, dynamic>))
      .toList();

  static List<Map<String, dynamic>> messagesToJson(
    List<ResponseMessage> messages,
  ) =>
      messages.map((e) => e.toJson()).toList();
}
