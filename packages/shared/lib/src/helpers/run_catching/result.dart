import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../shared.dart';

part 'result.freezed.dart';

@freezed
sealed class Result<T> with _$Result<T> {
  const Result._();

  factory Result.success(T data) = ResultSuccess;

  factory Result.failure(AppException exception) = ResultError;
}

extension ResultX<T> on Result<T> {
  R when<R>({
    required R Function(T data) success,
    required R Function(AppException exception) failure,
  }) {
    final result = this;

    if (result is ResultSuccess<T>) {
      return success(result.data);
    }
    if (result is ResultError<T>) {
      return failure(result.exception);
    }

    throw StateError('Unhandled Result state: $result');
  }

  R maybeWhen<R>({
    R Function(T data)? success,
    R Function(AppException exception)? failure,
    required R Function() orElse,
  }) {
    final result = this;

    if (result is ResultSuccess<T> && success != null) {
      return success(result.data);
    }
    if (result is ResultError<T> && failure != null) {
      return failure(result.exception);
    }

    return orElse();
  }
}
