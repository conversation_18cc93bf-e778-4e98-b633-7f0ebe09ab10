import 'package:flutter/foundation.dart';
import 'package:retry/retry.dart';

import '../../shared.dart';

class RetryService {
  final RetryOptions _options = RetryOptions(
    maxAttempts: GlobalConfig.maxAttempts,
    delayFactor: GlobalConfig.delayFactor,
    maxDelay: GlobalConfig.maxDelay,
    randomizationFactor: GlobalConfig.randomizationFactor,
  );

  /// Execution [Action] with retry logic:
  /// only retry when exception is [RetryException]
  ///
  /// Logs detailed information about each retry attempt including:
  /// - Current retry count
  /// - Maximum retry attempts
  /// - Exception details including retry reason if available
  Future<T> execute<T>(Future<T> Function() action) {
    int retryCount = 0;

    return _options.retry(
      action,
      retryIf: (e) => e is RetryException,
      onRetry: (e) {
        retryCount++;
        final remainingRetries = GlobalConfig.maxAttempts - retryCount;

        // Build detailed log message
        final buffer = StringBuffer();
        buffer.write(
          'RetryService: Attempt $retryCount/$GlobalConfig.maxAttempts',
        );
        buffer.write(' (${remainingRetries} remaining)');

        // Add retry reason if available
        if (e is RetryException && e.retryReason != null) {
          buffer.write('\nReason: ${e.retryReason}');
        }

        // Add exception details
        buffer.write('\nException: ${e.toString()}');

        debugPrint(buffer.toString());
      },
    );
  }
}
