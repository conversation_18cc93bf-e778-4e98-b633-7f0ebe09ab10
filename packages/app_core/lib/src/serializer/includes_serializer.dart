import 'package:chat/chat.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart' as user;
import 'package:user_manager/user_manager.dart';

class IncludesSerializer {
  final ResponseIncludes includes;

  IncludesSerializer(this.includes);

  bool get hasUser => includes.user != null;

  bool get hasChannel => includes.channel != null;

  bool get hasMember => includes.member != null;

  bool get hasMessage => includes.message != null;

  bool get hasFriend => includes.friend != null;

  bool get hasUsers => includes.users?.isNotEmpty ?? false;

  bool get hasChannels => includes.channels?.isNotEmpty ?? false;

  bool get hasMembers => includes.members?.isNotEmpty ?? false;

  bool get hasMessages => includes.messages?.isNotEmpty ?? false;

  bool get hasFriends => includes.friends?.isNotEmpty ?? false;

  user.User? getUser() => includes.user != null
      ? user.UserSerializer.serializeFromJson(data: includes.user!.toJson())
      : null;

  Channel? getChannel() => includes.channel != null
      ? ChannelSerializer.serializeFromJson(data: includes.channel!.toJson())
      : null;

  Member? getMember() => includes.member != null
      ? MemberSerializer.serializeFromJson(data: includes.member!.toJson())
      : null;

  Message? getMessage() => includes.message != null
      ? MessageSerializer.serializeFromJson(data: includes.message!.toJson())
      : null;

  Friend? getFriend() => includes.friend != null
      ? FriendSerializer.serializeFromJson(data: includes.friend!.toJson())
      : null;

  List<user.User> getUsers() => includes.users != null
      ? includes.users!.map((u) {
          final _user =
              user.UserSerializer.serializeFromJson(data: u.toJson())!;

          /// include user basic
          _user.partial = true;
          return _user;
        }).toList()
      : [];

  List<ChatUser> getChatUsers() => includes.users != null
      ? includes.users!.map((u) {
          final user = ChatUserSerializer.serializeFromJson(data: u.toJson())!;

          /// include user basic
          user.partial = true;
          return user;
        }).toList()
      : [];

  List<Channel> getChannels() => includes.channels != null
      ? includes.channels!
          .map(
            (channel) =>
                ChannelSerializer.serializeFromJson(data: channel.toJson())!,
          )
          .toList()
      : [];

  List<Member> getMembers() => includes.members != null
      ? includes.members!
          .map(
            (member) =>
                MemberSerializer.serializeFromJson(data: member.toJson())!,
          )
          .toList()
      : [];

  List<Message> getMessages() => includes.messages != null
      ? includes.messages!
          .map(
            (message) =>
                MessageSerializer.serializeFromJson(data: message.toJson())!,
          )
          .toList()
      : [];

  List<Friend> getFriends() => includes.friends != null
      ? includes.friends!
          .map(
            (friend) =>
                FriendSerializer.serializeFromJson(data: friend.toJson())!,
          )
          .toList()
      : [];

  List<ChatFriend> getChatFriends() => includes.friends != null
      ? includes.friends!
          .map((u) => ChatFriendSerializer.serializeFromJson(data: u.toJson())!)
          .toList()
      : [];

  user.User? findUserById({
    required String userId,
  }) =>
      includes.users
          ?.firstWhere((user) => user.userId == userId, orElse: null)
          .let(
            (user) => UserSerializer.serializeFromJson(data: user.toJson())!,
          );

  Channel? findChannelById({
    required String workspaceId,
    required String channelId,
  }) =>
      includes.channels
          ?.firstWhere(
            (channel) =>
                channel.workspaceId == workspaceId &&
                channel.channelId == channelId,
            orElse: null,
          )
          .let(
            (channel) =>
                ChannelSerializer.serializeFromJson(data: channel.toJson())!,
          );

  Member? findMemberById({required String userId}) => includes.members
      ?.firstWhere(
        (member) => member.userId == userId,
        orElse: null,
      )
      .let(
        (member) => MemberSerializer.serializeFromJson(data: member.toJson())!,
      );

  Message? findMessageById({
    required String workspaceId,
    required String channelId,
    required String messageId,
  }) =>
      includes.messages
          ?.firstWhere(
            (message) =>
                message.workspaceId == workspaceId &&
                message.channelId == channelId &&
                message.messageId == messageId,
            orElse: null,
          )
          .let(
            (message) =>
                MessageSerializer.serializeFromJson(data: message.toJson())!,
          );

  Friend? findFriendById({
    required String friendId,
  }) =>
      includes.friends
          ?.firstWhere((friend) => friend.friendId == friendId, orElse: null)
          .let(
            (friend) =>
                FriendSerializer.serializeFromJson(data: friend.toJson())!,
          );
}
