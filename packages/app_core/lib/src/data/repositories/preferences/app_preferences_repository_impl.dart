import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';

import '../../data_mappers/locale_data_mapper.dart';
import '../../datasources/preferences/app_preferences.dart';
import 'app_preferences_repository.dart';

@LazySingleton(as: AppPreferencesRepository)
class AppPreferencesRepositoryImpl extends AppPreferencesRepository {
  AppPreferencesRepositoryImpl(this._appPreferences, this._localeDataMapper);

  final AppPreferences _appPreferences;
  final LocaleDataMapper _localeDataMapper;

  @override
  ThemeMode get getThemeMode => _appPreferences.getThemeMode;

  @override
  Future<bool> setThemeMode({required ThemeMode themeMode}) async {
    return _appPreferences.saveIsDarkMode(themeMode);
  }

  @override
  Locale get getLocale =>
      _localeDataMapper.mapToEntity(_appPreferences.languageCode);

  @override
  Future<void> setLanguage({required Locale locale}) async {
    await _appPreferences.saveLocale(locale);
  }

  bool get isAuthenticated => true;

  @override
  Future<void> setAuthenticated({required bool authenticated}) async {
    await _appPreferences.saveIsAuthenticated(isAuthenticated);
  }
}
