import 'dart:convert';

import 'package:workmanager/workmanager.dart';

import '../enums/back_off_policy.dart';
import '../enums/out_of_quota_policy.dart';
import '../enums/task_type.dart';
import '../enums/worker_task_status.dart';
import '../extensions/extensions.dart';

class WorkerTask {
  int id = 0;

  String taskId;

  String taskName;

  String inputDataJson;

  int workerTaskStatusRaw;

  DateTime creationTime;

  int taskType;

  int? initialDelay;

  int? frequency;

  String? constraintsJson;

  int? backoffPolicy;

  int? backoffPolicyDelay;

  int? outOfQuotaPolicy;

  String? result;

  String? errorResult;

  WorkerTask({
    required this.taskId,
    required this.taskName,
    required this.inputDataJson,
    required this.workerTaskStatusRaw,
    required this.creationTime,
    required this.taskType,
    this.initialDelay,
    this.frequency,
    this.constraintsJson,
    this.backoffPolicy,
    this.backoffPolicyDelay,
    this.outOfQuotaPolicy,
    this.result,
    this.errorResult,
  });

  WorkerTaskStatus get workerTaskStatus =>
      WorkerTaskStatusExtension.fromRawValue(workerTaskStatusRaw);

  TaskTypeEnum get taskTypeEnum => TaskTypeExtension.fromRawValue(taskType);

  BackoffPolicyEnum? get backoffPolicyEnum => backoffPolicy != null
      ? BackoffPolicyExtension.fromRawValue(backoffPolicy!)
      : null;

  OutOfQuotaPolicyEnum? get outOfQuotaPolicyEnum => outOfQuotaPolicy != null
      ? OutOfQuotaPolicyExtension.fromRawValue(outOfQuotaPolicy!)
      : null;

  Constraints? get constraints => constraintsJson != null
      ? ConstraintsJson.fromJson(jsonDecode(constraintsJson!))
      : null;

  set constraints(Constraints? value) {
    constraintsJson = value != null ? jsonEncode(value.toJson()) : null;
  }

  Map<String, dynamic> get resultMap {
    if (result == null) {
      return {};
    }
    try {
      return jsonDecode(result!) as Map<String, dynamic>;
    } catch (e) {
      return {};
    }
  }

  Map<String, dynamic> get errorResultMap {
    if (errorResult == null) {
      return {};
    }
    try {
      return jsonDecode(errorResult!) as Map<String, dynamic>;
    } catch (e) {
      return {};
    }
  }

  factory WorkerTask.fromJson(Map<String, dynamic> json) {
    return WorkerTask(
      taskId: json['taskId'] as String,
      taskName: json['taskName'] as String,
      inputDataJson: json['inputDataJson'] as String,
      workerTaskStatusRaw: json['workerTaskStatusRaw'] as int,
      creationTime: DateTime.parse(json['creationTime'] as String),
      taskType: json['taskType'] as int,
      initialDelay: json['initialDelay'] as int?,
      frequency: json['frequency'] as int?,
      constraintsJson: json['constraintsJson'] as String?,
      backoffPolicy: json['backoffPolicy'] as int?,
      backoffPolicyDelay: json['backoffPolicyDelay'] as int?,
      outOfQuotaPolicy: json['outOfQuotaPolicy'] as int?,
      result: json['result'] as String?,
      errorResult: json['errorResult'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'taskId': taskId,
      'taskName': taskName,
      'inputDataJson': inputDataJson,
      'workerTaskStatusRaw': workerTaskStatusRaw,
      'creationTime': creationTime.toIso8601String(),
      'taskType': taskType,
      'initialDelay': initialDelay,
      'frequency': frequency,
      'constraintsJson': constraintsJson,
      'backoffPolicy': backoffPolicy,
      'backoffPolicyDelay': backoffPolicyDelay,
      'outOfQuotaPolicy': outOfQuotaPolicy,
      'result': result,
      'errorResult': errorResult,
    };
  }
}
