enum TaskNameEnum {
  unDefine('unDefine'),
  sendMessage('sendMessage'),
  editMessage('editMessage'),
  sendQuoteMessage('sendQuoteMessage'),
  sendForwardMessage('sendForwardMessage'),
  uploadFile('uploadFile'),
  compressVideoMessage('compressVideoMessage'),
  compressAndUploadImages('compressAndUploadImages'),
  compressAndUploadVideoMessage('compressAndUploadVideoMessage'),
  sendMessageResult('sendMessageResult'), // Save message sending results
  ;

  final String value;

  const TaskNameEnum(this.value);

  static TaskNameEnum? getEnumByValue(String? value) {
    if (value == null) return TaskNameEnum.unDefine;
    return TaskNameEnum.values.firstWhere(
      (type) => type.value == value,
      orElse: () => TaskNameEnum.unDefine,
    );
  }

  String rawValue() => value;
}

extension TaskNameExtension on TaskNameEnum {
  static TaskNameEnum fromRawValue(String rawValue) {
    return TaskNameEnum.getEnumByValue(rawValue) ?? TaskNameEnum.unDefine;
  }
}
