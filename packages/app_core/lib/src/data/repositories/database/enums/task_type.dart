enum TaskTypeEnum {
  oneOff(0),
  periodic(1),
  processing(2);

  final int value;

  const TaskTypeEnum(this.value);

  static TaskTypeEnum? getEnumByValue(int? value) {
    if (value == null) return TaskTypeEnum.oneOff;
    return TaskTypeEnum.values.firstWhere(
      (type) => type.value == value,
      orElse: () => TaskTypeEnum.oneOff,
    );
  }

  int rawValue() => value;
}

extension TaskTypeExtension on TaskTypeEnum {
  static TaskTypeEnum fromRawValue(int rawValue) {
    return TaskTypeEnum.getEnumByValue(rawValue) ?? TaskTypeEnum.oneOff;
  }
}
