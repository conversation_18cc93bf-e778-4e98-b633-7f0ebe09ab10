import 'package:flutter/foundation.dart';
import 'package:objectbox/internal.dart';
import 'package:objectbox/objectbox.dart';
import 'package:shared/shared.dart';

class PrivateDatabase {
  PrivateDatabase(this.store) {
    if (Admin.isAvailable() && kDebugMode && GlobalConfig.enableChatBoxAdmin) {
      admin = PrivateDataAdmin(store, bindUri: 'http://127.0.0.1:8095');
    }
  }

  PrivateDataAdmin? admin;

  final PrivateDataStore store;
}

class PrivateDataAdmin extends Admin {
  PrivateDataAdmin(this.store, {required this.bindUri})
      : super(store, bindUri: bindUri) {}

  final String bindUri;

  final Store store;
}

class PrivateDataStore extends Store {
  PrivateDataStore(ModelDefinition modelDefinition, {required String directory})
      : super(modelDefinition, directory: directory);
}

/// Database class for managing ObjectBox store for tasks
class TasksDatabase {
  TasksDatabase(this.store) {
    if (Admin.isAvailable() && kDebugMode && GlobalConfig.enableChatBoxAdmin) {
      admin = TasksAdmin(
        store,
        bindUri: 'http://127.0.0.1:8096',
      ); // Use different port from PrivateDatabase
    }
  }

  TasksAdmin? admin;

  final TasksStore store;
}

/// Admin class for TasksDatabase
class TasksAdmin extends Admin {
  TasksAdmin(this.store, {required this.bindUri})
      : super(store, bindUri: bindUri) {}

  final String bindUri;

  final Store store;
}

/// Store class for TasksDatabase
class TasksStore extends Store {
  TasksStore(ModelDefinition modelDefinition, {required String directory})
      : super(modelDefinition, directory: directory);
}
