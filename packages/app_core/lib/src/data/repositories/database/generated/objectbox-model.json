{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "4:3114277235513527030", "lastPropertyId": "16:13014", "name": "CallLogPrivateData", "properties": [{"id": "1:4317574616181321934", "name": "id", "type": 6, "flags": 129}, {"id": "2:13001", "name": "<PERSON><PERSON><PERSON>", "type": 9}, {"id": "3:13002", "name": "callId", "type": 9}, {"id": "4:13003", "name": "callerId", "type": 9}, {"id": "5:13004", "name": "calleeId", "type": 9}, {"id": "6:13005", "name": "callState", "type": 6}, {"id": "7:13006", "name": "endedReason", "type": 6}, {"id": "8:13007", "name": "callTimeInSeconds", "type": 6}, {"id": "9:13008", "name": "isOutgoing", "type": 1}, {"id": "10:13009", "name": "readTime", "type": 9}, {"id": "11:13010", "name": "endedTime", "type": 9}, {"id": "12:5509627311458594822", "name": "privateDataId", "type": 11, "flags": 520, "indexId": "5:8079029435172453924", "relationTarget": "PrivateData"}, {"id": "13:13011", "name": "source", "type": 9}, {"id": "14:13012", "name": "version", "type": 6}, {"id": "15:13013", "name": "createTime", "type": 9}, {"id": "16:13014", "name": "callType", "type": 6}], "relations": []}, {"id": "5:4052016978606803828", "lastPropertyId": "10:12008", "name": "ChannelPrivateData", "properties": [{"id": "1:8075413873946152164", "name": "id", "type": 6, "flags": 129}, {"id": "2:12001", "name": "<PERSON><PERSON><PERSON>", "type": 9}, {"id": "3:12002", "name": "channelId", "type": 9}, {"id": "4:12003", "name": "source", "type": 9}, {"id": "5:12004", "name": "version", "type": 6}, {"id": "6:12005", "name": "unreadCount", "type": 6}, {"id": "7:12006", "name": "lastSeenMessageId", "type": 9}, {"id": "8:6176837994190987624", "name": "privateDataId", "type": 11, "flags": 520, "indexId": "6:5283156962127133860", "relationTarget": "PrivateData"}, {"id": "9:12007", "name": "pinned", "type": 1}, {"id": "10:12008", "name": "sort", "type": 6}], "relations": []}, {"id": "6:1583717143655302177", "lastPropertyId": "5:14004", "name": "PrivateData", "properties": [{"id": "1:1098343080712589812", "name": "id", "type": 6, "flags": 129}, {"id": "2:14001", "name": "userId", "type": 9}, {"id": "3:14002", "name": "createTime", "type": 9}, {"id": "4:14003", "name": "updateTime", "type": 9}, {"id": "5:14004", "name": "<PERSON><PERSON><PERSON>", "type": 9}], "relations": []}, {"id": "7:9140086481094213094", "lastPropertyId": "9:11007", "name": "UserPrivateData", "properties": [{"id": "1:7603814540121263554", "name": "id", "type": 6, "flags": 129}, {"id": "2:11001", "name": "<PERSON><PERSON><PERSON>", "type": 9}, {"id": "3:11002", "name": "userId", "type": 9}, {"id": "4:11003", "name": "<PERSON><PERSON><PERSON>", "type": 9}, {"id": "5:11004", "name": "version", "type": 6}, {"id": "6:11005", "name": "source", "type": 9}, {"id": "7:11006", "name": "dmId", "type": 9}, {"id": "8:8304908658350062370", "name": "privateDataId", "type": 11, "flags": 520, "indexId": "7:3597460472721819595", "relationTarget": "PrivateData"}, {"id": "9:11007", "name": "blocked", "type": 1}], "relations": []}, {"id": "8:2660106734089218489", "lastPropertyId": "14:20013", "name": "TaskEntity", "properties": [{"id": "1:4507081967075083397", "name": "id", "type": 6, "flags": 129}, {"id": "2:20001", "name": "taskId", "type": 9}, {"id": "3:20002", "name": "name", "type": 9}, {"id": "4:20003", "name": "inputDataJson", "type": 9}, {"id": "5:20004", "name": "maxRetries", "type": 6}, {"id": "6:20005", "name": "retry<PERSON><PERSON><PERSON>", "type": 6}, {"id": "7:20006", "name": "timeout", "type": 6}, {"id": "8:20007", "name": "retryCount", "type": 6}, {"id": "9:20008", "name": "statusValue", "type": 6}, {"id": "10:20009", "name": "priorityValue", "type": 6}, {"id": "11:20010", "name": "createdAt", "type": 6}, {"id": "12:20011", "name": "completedAt", "type": 6}, {"id": "13:20012", "name": "isReferenceTask", "type": 1}, {"id": "14:20013", "name": "networkRequired", "type": 1}], "relations": []}, {"id": "9:3074161509164890012", "lastPropertyId": "3:22002", "name": "TaskResultEntity", "properties": [{"id": "1:6872726472692687717", "name": "id", "type": 6, "flags": 129}, {"id": "2:22001", "name": "resultId", "type": 9}, {"id": "3:22002", "name": "result<PERSON><PERSON>", "type": 9}], "relations": []}, {"id": "10:1951413735196757261", "lastPropertyId": "5:21004", "name": "WorkerMetadataEntity", "properties": [{"id": "1:5822294041808398818", "name": "id", "type": 6, "flags": 129}, {"id": "2:21001", "name": "apiHost", "type": 9}, {"id": "3:21002", "name": "fileStoreHost", "type": 9}, {"id": "4:21003", "name": "activeSessionKey", "type": 9}, {"id": "5:21004", "name": "<PERSON><PERSON><PERSON>", "type": 9}], "relations": []}], "lastEntityId": "10:1951413735196757261", "lastIndexId": "7:3597460472721819595", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [3000056928217729461, 5755151949977715663, 3618113809786688771], "retiredIndexUids": [8985066871704480404], "retiredPropertyUids": [6681425491816379943, 3001, 3002, 3003, 3004, 3005, 3006, 184280995837690141, 2001, 2002, 2003, 2006, 2007, 2008, 2010, 2011, 2012, 2013, 2014, 2015, 2016, 7074415731501838283, 9001, 9002, 9003, 9004, 9005, 9006, 9007, 9008], "retiredRelationUids": [], "version": 1}