import 'dart:ui';

import 'package:injectable/injectable.dart';
import 'package:localization_client/localization_client.dart';
import 'package:shared/shared.dart';

@Injectable()
class LocaleDataMapper extends BaseDataMapper<String, Locale> {
  @override
  Locale mapToEntity(String? data) {
    if (data == null) {
      return AppLocalizations.supportedLocales.firstWhere(
        (locale) =>
            locale.languageCode ==
            PlatformDispatcher.instance.locale.languageCode,
        orElse: () => Locale('en'),
      );
    }

    return Locale(data);
  }
}
