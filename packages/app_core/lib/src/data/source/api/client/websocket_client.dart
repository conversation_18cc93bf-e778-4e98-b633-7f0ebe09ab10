import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:websocket_api/websocket_api.dart' as ws;

import '../../../../../core.dart';

@LazySingleton()
class WebsocketClient {
  WebsocketClient() {
    if (Config.getInstance().apiAuthToken.isNotEmpty) {
      BaseClient.addAuthToken(
        BaseClient.dio,
        Config.getInstance().apiAuthToken,
      );
    }
    _instance = ws.WebsocketApi(
      dio: BaseClient.dio,
      serializers: ws.standardSerializers,
    ).getWebsocketManagerServiceApi();
  }

  late final ws.WebsocketManagerServiceApi _instance;

  ws.WebsocketManagerServiceApi get instance => _instance;
}
