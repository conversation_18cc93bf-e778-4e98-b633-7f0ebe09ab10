part of 'app_bloc.dart';

abstract class AppEvent extends BaseBlocEvent {
  const AppEvent();
}

@freezed
sealed class AppInitiated extends AppEvent with _$AppInitiated {
  const AppInitiated._();
  factory AppInitiated() = _AppInitiated;
}

@freezed
sealed class IsAuthenticatedStatusChanged extends AppEvent
    with _$IsAuthenticatedStatusChanged {
  const IsAuthenticatedStatusChanged._();
  factory IsAuthenticatedStatusChanged({required bool isAuthenticated}) =
      _IsAuthenticatedStatusChanged;
}

@freezed
sealed class AppThemeChanged extends AppEvent with _$AppThemeChanged {
  const AppThemeChanged._();
  factory AppThemeChanged({required ThemeMode themeMode}) = _AppThemeChanged;
}

@freezed
sealed class AppLanguageChanged extends AppEvent with _$AppLanguageChanged {
  const AppLanguageChanged._();
  factory AppLanguageChanged({required Locale locale}) = _AppLanguageChanged;
}

@freezed
sealed class AppLogout extends AppEvent with _$AppLogout {
  const AppLogout._();
  factory AppLogout({
    VoidCallback? onSuccess,
  }) = _AppLogout;
}

@freezed
sealed class AppInvalidToken extends AppEvent with _$AppInvalidToken {
  const AppInvalidToken._();
  factory AppInvalidToken({
    VoidCallback? onSuccess,
  }) = _AppInvalidToken;
}

@freezed
sealed class AppTime24HourFormatChanged extends AppEvent
    with _$AppTime24HourFormatChanged {
  const AppTime24HourFormatChanged._();
  factory AppTime24HourFormatChanged({
    required bool is24HourFormat,
  }) = _AppTime24HourFormatChanged;
}
