part of 'block_user_bloc.dart';

sealed class BlockUserEvent extends BaseBloc<PERSON>vent {
  const BlockUserEvent();
}

class OnBlockUserEvent extends BlockUserEvent {
  const OnBlockUserEvent({required this.userId, this.popOnlyMine});

  final String userId;
  final bool? popOnlyMine;
}

class OnUnBlockUserEvent extends BlockUserEvent {
  const OnUnBlockUserEvent({required this.userId, this.popOnlyMine});

  final String userId;
  final bool? popOnlyMine;
}

class OnLoadListBlockUserEvent extends BlockUserEvent {
  const OnLoadListBlockUserEvent();
}

class OnUpdateCloseWarningEvent extends BlockUserEvent {
  const OnUpdateCloseWarningEvent({required this.isClose});

  final bool isClose;
}
