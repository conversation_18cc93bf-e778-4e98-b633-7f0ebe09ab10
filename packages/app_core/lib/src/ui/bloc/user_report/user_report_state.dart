part of 'user_report_bloc.dart';

@freezed
sealed class UserReportState extends BaseBlocState with _$UserReportState {
  const UserReportState._();
  factory UserReportState.initial() = UserReportStateInitial;

  factory UserReportState.showProcessDialog() =
      UserReportStateShowProcessDialog;

  factory UserReportState.updateProcessDialog({
    @Default(false) bool response,
    String? userId,
    String? name,
    bool? isBlock,
  }) = UserReportStateUpdateProcessDialog;
}

extension UserReportStateX on UserReportState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function()? showProcessDialog,
    T Function(
      bool response,
      String? userId,
      String? name,
      bool? isBlock,
    )? updateProcessDialog,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is UserReportStateInitial && initial != null) {
      return initial();
    }
    if (state is UserReportStateShowProcessDialog &&
        showProcessDialog != null) {
      return showProcessDialog();
    }
    if (state is UserReportStateUpdateProcessDialog &&
        updateProcessDialog != null) {
      return updateProcessDialog(
        state.response,
        state.userId,
        state.name,
        state.isBlock,
      );
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function() showProcessDialog,
    required T Function(
      bool response,
      String? userId,
      String? name,
      bool? isBlock,
    ) updateProcessDialog,
  }) {
    final state = this;

    if (state is UserReportStateInitial) {
      return initial();
    }
    if (state is UserReportStateShowProcessDialog) {
      return showProcessDialog();
    }
    if (state is UserReportStateUpdateProcessDialog) {
      return updateProcessDialog(
        state.response,
        state.userId,
        state.name,
        state.isBlock,
      );
    }

    throw StateError('Unhandled UserReportState: $state');
  }
}
