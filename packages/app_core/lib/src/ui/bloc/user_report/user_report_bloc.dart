import 'package:chat/chat.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_report_api/user_report_api.dart';

import '../../../domain/usecase/report/user_report_use_case.dart';

part 'user_report_bloc.freezed.dart';
part 'user_report_event.dart';
part 'user_report_state.dart';

@injectable
class UserReportBloc extends BaseBloc<UserReportEvent, UserReportState> {
  UserReportBloc(this._userReportUseCase, this._getChatUserUseCase)
      : super(UserReportState.initial()) {
    on<OnUserReportEvent>(_onUserReport);
  }

  final UserReportUseCase _userReportUseCase;
  final GetChatUserUseCase _getChatUserUseCase;

  Future<void> _onUserReport(
    OnUserReportEvent event,
    Emitter<UserReportState> emit,
  ) async {
    emit(UserReportState.showProcessDialog());
    UserReportOutput userBlockOutput = await _userReportUseCase.execute(
      UserReportInput(
        userId: event.userId,
        pretendingTo: convertPretending(event.pretendingTo),
        reportCategory: convertCategory(event.reportCategory),
        reason: event.reason,
      ),
    );
    bool? isBlocked;
    try {
      var getUser = await _getChatUserUseCase
          .execute(GetChatUserInput(userId: event.userId));
      isBlocked = getUser.user?.blocked;
    } catch (error) {}

    emit(
      UserReportState.updateProcessDialog(
        response: userBlockOutput.ok,
        userId: event.userId,
        name: event.name,
        isBlock: isBlocked,
      ),
    );
  }

  V3ReportCategory? convertCategory(String? category) {
    if (category == null) return null;
    try {
      final reportCategory = V3ReportCategory.values.firstWhere(
        (item) => item.name
            .replaceAll('_', '')
            .toLowerCase()
            .contains(category.toLowerCase()),
      );
      return reportCategory;
    } catch (_) {
      return null;
    }
  }

  V3PretendingTo? convertPretending(String? pretending) {
    if (pretending == null) return null;
    try {
      final pretendingTo = V3PretendingTo.values.firstWhere(
        (item) => pretending
            .toLowerCase()
            .contains(item.name.replaceAll('PRETENDING_TO_', '').toLowerCase()),
      );
      return pretendingTo;
    } catch (_) {
      return null;
    }
  }
}
