part of 'setting_notification_bloc.dart';

sealed class SettingNotificationEvent extends BaseBloc<PERSON>vent {
  const SettingNotificationEvent();
}

class OnSubscribeChannelEvent extends SettingNotificationEvent {
  const OnSubscribeChannelEvent({
    this.workspaceId,
    this.channelId,
    this.userId,
  });

  final String? workspaceId;
  final String? channelId;
  final String? userId;
}

class OnUnsubscribeChannelEvent extends SettingNotificationEvent {
  const OnUnsubscribeChannelEvent({
    this.workspaceId,
    this.channelId,
    this.userId,
  });

  final String? workspaceId;
  final String? channelId;
  final String? userId;
}

class OnTurnOnOffGlobalNotificationEvent extends SettingNotificationEvent {
  const OnTurnOnOffGlobalNotificationEvent({
    required this.isTurnOn,
  });

  final bool isTurnOn;
}
