part of 'report_message_bloc.dart';

sealed class ReportMessageEvent extends BaseBlocEvent {
  const ReportMessageEvent();
}

class OnReportMessageEvent extends ReportMessageEvent {
  const OnReportMessageEvent({
    this.userId,
    this.name,
    required this.messageId,
    this.workspaceId,
    this.channelId,
    this.pretendingTo,
    this.reportCategory,
    this.other,
  });

  final String? workspaceId;
  final String? channelId;
  final String messageId;
  final String? userId;
  final String? name;
  final String? pretendingTo;
  final String? reportCategory;
  final String? other;
}
