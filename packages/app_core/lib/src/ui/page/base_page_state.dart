import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';

import '../bloc/app/app_bloc.dart';
import '../bloc/common/common_bloc.dart';

abstract class BasePageState<T extends StatefulWidget, B extends BaseBloc>
    extends BasePageStateDelegate<T, B> {}

abstract class BasePageStateDelegate<T extends StatefulWidget,
    B extends BaseBloc> extends State<T> {
  late final AppBloc appBloc = GetIt.instance.get<AppBloc>();

  bool get isAppWidget => false;

  late final CommonBloc commonBloc = GetIt.instance.get<CommonBloc>()
    ..appBloc = appBloc;

  late final B bloc = GetIt.instance.get<B>();

  late final ValueNotifier<bool> isAuthenticated = ValueNotifier(false);

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => bloc),
        BlocProvider(create: (_) => commonBloc),
      ],
      child: buildPageListeners(
        child: isAppWidget
            ? buildPage(context)
            : Stack(
                children: [
                  buildPage(context),
                  BlocBuilder<CommonBloc, CommonState>(
                    buildWhen: (previous, current) =>
                        previous.isLoading != current.isLoading,
                    builder: (context, state) => Visibility(
                      visible: state.isLoading,
                      child: buildPageLoading(),
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget buildPageListeners({required Widget child}) => child;

  Widget buildPageLoading() => const Center(
        child: CircularProgressIndicator(),
      );

  Widget buildPage(BuildContext context);
}
