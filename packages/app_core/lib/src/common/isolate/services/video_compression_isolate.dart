import 'dart:async';
import 'dart:isolate';

import 'package:flutter/foundation.dart';
import 'package:flutter_isolate/flutter_isolate.dart';
import 'package:uuid/uuid.dart';

import '../handlers/compress_video_message_handler.dart';
import '../input/worker_compress_video_input.dart';
import '../output/worker_compress_video_output.dart';
import '../core/utils/logger.dart';

/// Separate isolate for video compression processing
///
/// This class creates and manages a dedicated isolate just for video compression tasks,
/// helping to avoid affecting other tasks in the main ResilientIsolate.
class VideoCompressionIsolate {
  static final VideoCompressionIsolate _instance =
      VideoCompressionIsolate._internal();
  factory VideoCompressionIsolate() => _instance;

  VideoCompressionIsolate._internal();

  FlutterIsolate? _videoIsolate;
  final _uuid = Uuid();
  final _callbackMap = <String, Completer<WorkerCompressVideoOutput>>{};
  bool _isInitialized = false;

  // Communication ports
  SendPort? _requestPort;
  ReceivePort? _responsePort;

  // Getters
  bool get isInitialized => _isInitialized;

  /// Initialize video compression isolate
  Future<void> initialize() async {
    if (_isInitialized) {
      RILogger.printClassMethodDebug(
        'VideoCompressionIsolate',
        'initialize',
        'Already initialized',
      );
      return;
    }

    await _ensureIsolateRunning();
    _isInitialized = true;

    RILogger.printClassMethodDebug(
      'VideoCompressionIsolate',
      'initialize',
      'Initialization complete',
    );
  }

  /// Ensure isolate is running
  Future<void> _ensureIsolateRunning() async {
    if (_videoIsolate != null) {
      return;
    }

    try {
      // Create ports for communication
      _responsePort = ReceivePort();

      // Start the isolate
      _videoIsolate = await FlutterIsolate.spawn(
        _videoIsolateEntryPoint,
        [_responsePort!.sendPort],
      );

      // Wait for the request port from the isolate
      _requestPort = await _responsePort!.first;

      // Set up response listener
      _responsePort = ReceivePort();
      _requestPort!.send(_responsePort!.sendPort);

      // Listen for responses from the isolate
      _responsePort!.listen(_handleResponse);

      RILogger.printClassMethodDebug(
        'VideoCompressionIsolate',
        '_ensureIsolateRunning',
        'Video compression isolate started',
      );
    } catch (e) {
      RILogger.printError('Failed to start video compression isolate', e);
      _videoIsolate = null;
      _responsePort?.close();
      _responsePort = null;
      _requestPort = null;
      rethrow;
    }
  }

  /// Process response from isolate
  void _handleResponse(dynamic message) {
    if (message is! Map<String, dynamic>) {
      RILogger.printError('Invalid response format', message);
      return;
    }

    final taskId = message['taskId'] as String?;
    if (taskId == null) {
      RILogger.printError('Missing taskId in response', message);
      return;
    }

    if (_callbackMap.containsKey(taskId)) {
      try {
        if (message.containsKey('error')) {
          // Handle error case
          RILogger.printClassMethodDebug(
            'VideoCompressionIsolate',
            '_handleResponse',
            'Task $taskId failed with error: ${message['error']}',
          );
          _callbackMap[taskId]!.completeError(Exception(message['error']));
        } else if (message.containsKey('result')) {
          // Handle success case
          final result = WorkerCompressVideoOutput.fromJson(
            message['result'] as Map<String, dynamic>,
          );

          RILogger.printClassMethodDebug(
            'VideoCompressionIsolate',
            '_handleResponse',
            '[DEBUG][MessageRef:${result.messageRef}] Video compression completed in dedicated isolate for task: $taskId',
          );

          _callbackMap[taskId]!.complete(result);
        } else {
          RILogger.printClassMethodDebug(
            'VideoCompressionIsolate',
            '_handleResponse',
            'Invalid response format for task: $taskId',
          );
          _callbackMap[taskId]!
              .completeError(Exception('Invalid response format'));
        }
      } catch (e) {
        RILogger.printClassMethodDebug(
          'VideoCompressionIsolate',
          '_handleResponse',
          'Error handling response for task $taskId: $e',
        );
        _callbackMap[taskId]!.completeError(e);
      }
      _callbackMap.remove(taskId);
    }
  }

  /// Compress video and return result
  Future<WorkerCompressVideoOutput> compressVideo(
    WorkerCompressVideoInput input,
  ) async {
    if (!_isInitialized) {
      await initialize();
    }

    await _ensureIsolateRunning();

    final taskId = _uuid.v4();
    final completer = Completer<WorkerCompressVideoOutput>();
    _callbackMap[taskId] = completer;
    final messageRef = input.messageRef ?? input.file.ref;

    RILogger.printClassMethodDebug(
      'VideoCompressionIsolate',
      'compressVideo',
      'Sending video compression task: $taskId',
    );

    RILogger.printClassMethodDebug(
      'VideoCompressionIsolate',
      'compressVideo',
      '[DEBUG][MessageRef:$messageRef] Starting video compression in dedicated isolate for task: $taskId',
    );

    // Send task to isolate
    _requestPort!.send({
      'taskId': taskId,
      'input': input.toJson(),
    });

    return completer.future;
  }

  /// Release resources
  Future<void> dispose() async {
    RILogger.printClassMethodDebug(
      'VideoCompressionIsolate',
      'dispose',
      'Disposing video compression isolate',
    );

    _videoIsolate?.kill();
    _videoIsolate = null;
    _responsePort?.close();
    _responsePort = null;
    _requestPort = null;
    _isInitialized = false;

    // Complete any pending callbacks with error
    for (final entry in _callbackMap.entries) {
      entry.value.completeError(
        Exception('Video compression isolate disposed while task was running'),
      );
    }
    _callbackMap.clear();
  }
}

/// Entry point for video compression isolate
@pragma('vm:entry-point')
void _videoIsolateEntryPoint(List<dynamic> args) async {
  final mainSendPort = args[0] as SendPort;

  // Create a receive port for incoming requests
  final receivePort = ReceivePort();

  // Send the send port back to the main isolate
  mainSendPort.send(receivePort.sendPort);

  // Wait for the response port
  final responseSendPort = await receivePort.first as SendPort;

  // Listen for compression requests
  receivePort.listen((message) async {
    if (message is! Map<String, dynamic>) return;

    final taskId = message['taskId'] as String?;
    if (taskId == null) return;

    try {
      final input = WorkerCompressVideoInput.fromJson(
        message['input'] as Map<String, dynamic>,
      );

      final messageRef = input.messageRef ?? input.file.ref;
      RILogger.printClassMethodDebug(
        'VideoCompressionIsolate',
        '_videoIsolateEntryPoint',
        '[DEBUG][MessageRef:$messageRef] Processing video compression task $taskId in isolate',
      );

      // Compress the video
      // Use CompressVideoHandler to compress video
      final compressHandler = CompressVideoHandler();
      final result = await compressHandler.compressVideo(input);

      // Add messageRef to result for logging purposes
      final resultWithRef = result.copyWith(messageRef: messageRef);

      RILogger.printClassMethodDebug(
        'VideoCompressionIsolate',
        '_videoIsolateEntryPoint',
        '[DEBUG][MessageRef:$messageRef] Completed video compression task $taskId in isolate',
      );

      // Send the result back
      responseSendPort.send({
        'taskId': taskId,
        'result': resultWithRef.toJson(),
      });
    } catch (e, stackTrace) {
      debugPrint('Error in video compression isolate: $e\n$stackTrace');
      RILogger.printClassMethodDebug(
        'VideoCompressionIsolate',
        '_videoIsolateEntryPoint',
        'Error processing task $taskId: $e',
      );

      // Create an error result with the original path if possible
      try {
        final input = WorkerCompressVideoInput.fromJson(
          message['input'] as Map<String, dynamic>,
        );
        final filePath = input.file.path;

        // Return result with original path to at least continue the process
        responseSendPort.send({
          'taskId': taskId,
          'result': WorkerCompressVideoOutput(
            videoPath: filePath,
            thumbnailPath: '',
            duration: 0,
          ).toJson(),
        });
      } catch (innerError) {
        // If unable to get original path, send error
        responseSendPort.send({
          'taskId': taskId,
          'error': e.toString(),
        });
      }
    }
  });
}
