import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

/// A logging utility class for Flutter applications.
class RILogger {
  // Configuration properties
  static bool _isDebugEnabled = kDebugMode;
  static bool _isFileLoggingEnabled = false;
  static File? _logFile;
  static IOSink? _logSink;

  // Constants
  static const String _defaultPrefix = '[RI]';
  static const String _logFileName = 'ri_logs.txt';

  /// Initializes file logging in the application cache directory
  static Future<void> initFileLogging() async {
    if (!_isFileLoggingEnabled) return;
    if (_logFile != null) return;

    try {
      final cacheDir = await getApplicationCacheDirectory();
      _logFile = File('${cacheDir.path}/$_logFileName');

      // Open file for writing logs
      _logSink = _logFile!.openWrite(mode: FileMode.append);

      printDebug('File logging initialized: ${_logFile!.path}');
    } catch (e, stackTrace) {
      debugPrint('$_defaultPrefix Failed to initialize file logging: $e');
      debugPrint('$_defaultPrefix Stack trace: $stackTrace');
    }
  }

  /// Disables file logging and closes the file
  static Future<void> closeFileLogging() async {
    if (_logSink != null) {
      await _logSink!.flush();
      await _logSink!.close();
      _logSink = null;
    }
    _isFileLoggingEnabled = false;
  }

  /// Gets or sets whether debug logging is enabled
  static bool get isDebugEnabled => _isDebugEnabled;
  static set isDebugEnabled(bool value) => _isDebugEnabled = value;

  /// Gets or sets whether file logging is enabled
  static bool get isFileLoggingEnabled => _isFileLoggingEnabled;
  static set isFileLoggingEnabled(bool value) => _isFileLoggingEnabled = value;

  // MARK: - Private Helpers

  /// Writes log message to file if file logging is enabled
  static void _writeToFile(String message) {
    if (_isFileLoggingEnabled && _logSink != null) {
      try {
        final timestamp = DateTime.now().toIso8601String();
        _logSink!.writeln('$timestamp $message');
      } catch (e) {
        debugPrint('$_defaultPrefix Failed to write to log file: $e');
      }
    }
  }

  /// Creates a formatted log message with optional components
  static String _formatLogMessage({
    required String message,
    String? className,
    String? methodName,
    String? taskId,
    bool isError = false,
    dynamic error,
    StackTrace? stackTrace,
  }) {
    final timestamp = DateTime.now().toIso8601String();
    final buffer = StringBuffer('$_defaultPrefix [$timestamp]');

    if (isError) {
      buffer.write(' [ERROR]');
    }

    if (className != null && methodName != null) {
      buffer.write('[$className.$methodName]');
    }

    if (taskId != null) {
      buffer.write('[Task:$taskId]');
    }

    buffer.write(' $message');

    if (error != null) {
      buffer.write('\nError: $error');
    }

    if (stackTrace != null) {
      buffer.write('\nStack trace: $stackTrace');
    }

    return buffer.toString();
  }

  // MARK: - Public Debug Logging Methods

  /// Prints a simple debug message
  static void printDebug(String message) {
    if (!_isDebugEnabled) return;

    final logMessage = _formatLogMessage(message: message);
    debugPrint(logMessage);
    _writeToFile(logMessage);
  }

  /// Prints a debug message with class and method context
  static void printClassMethodDebug(
    String className,
    String methodName,
    String message,
  ) {
    if (!_isDebugEnabled) return;

    final logMessage = _formatLogMessage(
      message: message,
      className: className,
      methodName: methodName,
    );
    debugPrint(logMessage);
    _writeToFile(logMessage);
  }

  /// Prints a debug message with task ID context
  static void printTaskDebug(
    String className,
    String methodName,
    String taskId,
    String message,
  ) {
    if (!_isDebugEnabled) return;

    final logMessage = _formatLogMessage(
      message: message,
      className: className,
      methodName: methodName,
      taskId: taskId,
    );
    debugPrint(logMessage);
    _writeToFile(logMessage);
  }

  /// Prints a debug message with only task ID context
  static void printTaskOnlyDebug(
    String taskId,
    String message,
  ) {
    if (!_isDebugEnabled) return;

    final logMessage = _formatLogMessage(
      message: message,
      taskId: taskId,
    );
    debugPrint(logMessage);
    _writeToFile(logMessage);
  }

  // MARK: - Error Logging Methods

  /// Prints an error message with optional error details and stack trace
  static void printError(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) {
    if (!_isDebugEnabled) return;

    final logMessage = _formatLogMessage(
      message: message,
      isError: true,
      error: error,
      stackTrace: stackTrace,
    );
    debugPrint(logMessage);
    _writeToFile(logMessage);
  }

  /// Prints an error message with task ID and optional error details
  static void printTaskError(
    String taskId,
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) {
    if (!_isDebugEnabled) return;

    final logMessage = _formatLogMessage(
      message: message,
      taskId: taskId,
      isError: true,
      error: error,
      stackTrace: stackTrace,
    );
    debugPrint(logMessage);
    _writeToFile(logMessage);
  }

  /// Prints an error message with class, method, and task context
  static void printTaskClassMethodError(
    String className,
    String methodName,
    String taskId,
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) {
    if (!_isDebugEnabled) return;

    final logMessage = _formatLogMessage(
      message: message,
      className: className,
      methodName: methodName,
      taskId: taskId,
      isError: true,
      error: error,
      stackTrace: stackTrace,
    );
    debugPrint(logMessage);
    _writeToFile(logMessage);
  }
}
