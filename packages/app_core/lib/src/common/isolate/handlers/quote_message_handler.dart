import 'package:shared/shared.dart';

import '../../../data/source/api/client/isolate_api_client.dart';
import '../../network/retry_manager.dart';
import '../input/worker_send_quote_message_input.dart';
import '../output/worker_send_message_output.dart';
import 'base_message_handler.dart';

/// Specialized handler for quote messages
class QuoteMessageHandler extends BaseMessageHandler<WorkerSendQuoteMessageInput> {
  QuoteMessageHandler({
    required super.apiClient,
    required super.retryManager,
  });

  @override
  WorkerSendQuoteMessageInput parseInput(Map<String, dynamic> inputData) {
    return WorkerSendQuoteMessageInput.fromJson(inputData);
  }

  @override
  String getApiEndpoint(WorkerSendQuoteMessageInput input) {
    return '/Message/' + (input.isDm() ? 'QuoteDMMessage' : 'QuoteMessage');
  }

  @override
  Future<WorkerSendMessageOutput> executeMessageOperation(
    WorkerSendQuoteMessageInput input,
    DateTime createTime,
  ) async {
    return await makeRetryableApiCall(
      input: input,
      createTime: createTime,
      apiCall: () => apiClient.post(
        getApiEndpoint(input),
        body: input.toRequest(),
      ),
    );
  }

  /// Legacy method for backward compatibility
  Future<WorkerSendMessageOutput> handleQuoteMessage(
    Map<String, dynamic> inputData,
  ) async {
    return await handleMessage(inputData);
  }
}
