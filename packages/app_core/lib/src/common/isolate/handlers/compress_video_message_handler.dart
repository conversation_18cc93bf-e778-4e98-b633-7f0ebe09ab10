import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:isolate';

import 'package:flutter/widgets.dart';
import 'package:flutter_isolate/flutter_isolate.dart';
import 'package:path/path.dart' as path;
import 'package:shared/shared.dart';
import 'package:video_compressor/video_compressor.dart';

import '../../../../core.dart';
import '../exceptions/file_not_found_exception.dart';
import '../exceptions/send_message_timeout_exception.dart';
import '../output/worker_compress_video_output.dart';

@pragma("vm:entry-point")
class CompressVideoHandler {
  /// Public API - giữ nguyên interface
  Future<WorkerCompressVideoOutput> compressVideo(
    WorkerCompressVideoInput inputData,
  ) async {
    if (inputData.creationTime
        .add(GlobalConfig.sendTimeoutDuration)
        .isBefore(DateTime.now())) {
      throw SendMessageTimeoutException(msgRef: inputData.file.ref);
    }

    final receivePort = ReceivePort();
    try {
      await FlutterIsolate.spawn(
        _compressVideoEntry,
        [receivePort.sendPort, jsonEncode(inputData.toJson())],
      );

      final outputJson = await receivePort.first as String;
      final resultMap = jsonDecode(outputJson) as Map<String, dynamic>;

      if (resultMap.containsKey('error')) {
        throw Exception(resultMap['error']);
      }

      return WorkerCompressVideoOutput.fromJson(resultMap);
    } finally {
      receivePort.close();
    }
  }

  @pragma("vm:entry-point")
  static Future<void> _compressVideoEntry(List<dynamic> args) async {
    WidgetsFlutterBinding.ensureInitialized();

    final sendPort = args[0] as SendPort;
    final inputMap = jsonDecode(args[1] as String) as Map<String, dynamic>;
    final inputData = WorkerCompressVideoInput.fromJson(inputMap);

    try {
      final output = await _compressVideoInternal(inputData);
      sendPort.send(jsonEncode(output.toJson()));
    } catch (e, st) {
      sendPort.send(
        jsonEncode({
          'error': e.toString(),
          'stackTrace': st.toString(),
        }),
      );
    }
  }

  static Future<WorkerCompressVideoOutput> _compressVideoInternal(
    WorkerCompressVideoInput inputData,
  ) async {
    final filePath = inputData.file.path;
    if (!File(filePath).existsSync()) {
      throw FileNotFoundException(message: 'File $filePath not exists');
    }

    final messageRef = inputData.messageRef ?? inputData.file.ref;
    final fileRef = inputData.file.fileRef ?? inputData.file.ref;
    final safeFilePath = _getSafeFilePath(filePath);

    // Use FileUtils structure for cache directories
    final videoDir = await _createVideoSubdirectory(messageRef, fileRef);
    final thumbnailDir = await _createThumbnailSubdirectory(messageRef, fileRef);

    final originalFileName = path.basename(filePath);
    final thumbnailName = _createUniqueFileName(originalFileName, fileRef, isVideo: false);
    final uniqueThumbnailPath = path.join(thumbnailDir.path, thumbnailName);

    final resultList = await Future.wait([
      VideoCompressor.compressVideo(path: safeFilePath),
      VideoCompressor.getFileThumbnail(
        path: safeFilePath,
        quality: 100,
        position: -1,
      ),
    ]);

    final videoInfo = resultList[0] as VideoInfo?;
    final fileThumbnail = resultList[1] as File;

    String compressedVideoPath = safeFilePath;

    if (videoInfo?.file != null) {
      final originalVideoName = path.basename(videoInfo!.file!.path);
      final compressedVideoName = _createUniqueFileName(originalVideoName, fileRef);
      final uniqueCompressedVideoPath = path.join(videoDir.path, compressedVideoName);

      try {
        videoInfo.file!.copySync(uniqueCompressedVideoPath);
        compressedVideoPath = uniqueCompressedVideoPath;

        // Update video cache using FileUtils
        FileUtils.addVideoPathCache(
          messageRef: messageRef,
          fileRef: fileRef,
          path: uniqueCompressedVideoPath,
          isVideoFile: true,
        );
      } catch (_) {
        compressedVideoPath = videoInfo.file!.path;
      }
    }

    String thumbnailPath = '';
    if (fileThumbnail.existsSync()) {
      try {
        fileThumbnail.copySync(uniqueThumbnailPath);
        thumbnailPath = uniqueThumbnailPath;

        // Update thumbnail cache using FileUtils
        FileUtils.addVideoThumbnailPathCache(
          messageRef: messageRef,
          fileRef: fileRef,
          path: uniqueThumbnailPath,
        );
      } catch (_) {
        thumbnailPath = fileThumbnail.path;
      }
    }

    return WorkerCompressVideoOutput(
      videoPath: compressedVideoPath,
      thumbnailPath: thumbnailPath,
      duration: videoInfo?.duration?.toInt() ?? 0,
    );
  }

  /// Helper methods optimized with FileUtils
  static String _getSafeFilePath(String filePath) {
    try {
      final fileName = path.basename(filePath);
      final directory = path.dirname(filePath);

      final safeFileName = fileName
          .replaceAll(' ', '_')
          .replaceAll('%', '_percent_')
          .replaceAll('#', '_hash_')
          .replaceAll('&', '_and_')
          .replaceAll('+', '_plus_')
          .replaceAll('?', '_question_')
          .replaceAll('=', '_equal_')
          .replaceAll('file://', '');

      if (safeFileName == fileName) {
        return filePath.replaceAll('file://', '');
      }

      final newFilePath = path.join(directory, safeFileName);
      final originalFile = File(filePath.replaceAll('file://', ''));

      if (originalFile.existsSync()) {
        originalFile.copySync(newFilePath);
        return newFilePath;
      }

      return filePath.replaceAll('file://', '');
    } catch (e) {
      return filePath.replaceAll('file://', '');
    }
  }

  /// Create video subdirectory using FileUtils structure
  /// Structure: FileUtils.cacheDirPath/<messageRef>/<fileRef>/video/
  static Future<Directory> _createVideoSubdirectory(
    String messageRef,
    String fileRef,
  ) async {
    await FileUtils.init(); // Ensure FileUtils is initialized
    final videoDir = Directory('${FileUtils.cacheDirPath}/$messageRef/$fileRef/video');

    if (!await videoDir.exists()) {
      await videoDir.create(recursive: true);
    }

    return videoDir;
  }

  /// Create thumbnail subdirectory using FileUtils structure
  /// Structure: FileUtils.cacheDirPath/<messageRef>/<fileRef>/thumbnail/
  static Future<Directory> _createThumbnailSubdirectory(
    String messageRef,
    String fileRef,
  ) async {
    await FileUtils.init(); // Ensure FileUtils is initialized
    final thumbnailDir = Directory('${FileUtils.cacheDirPath}/$messageRef/$fileRef/thumbnail');

    if (!await thumbnailDir.exists()) {
      await thumbnailDir.create(recursive: true);
    }

    return thumbnailDir;
  }

  /// Create unique file name using RandomUtils for better uniqueness
  static String _createUniqueFileName(String originalFileName, String fileRef, {bool isVideo = true}) {
    final extension = path.extension(originalFileName);
    final baseName = path.basenameWithoutExtension(originalFileName);
    final uniqueId = RandomUtils.generateRandomString(8);
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    // Use .jpg for thumbnails, keep original extension for videos
    final finalExtension = isVideo ? extension : '.jpg';

    return '${baseName}_${fileRef}_${uniqueId}_$timestamp$finalExtension';
  }
}
