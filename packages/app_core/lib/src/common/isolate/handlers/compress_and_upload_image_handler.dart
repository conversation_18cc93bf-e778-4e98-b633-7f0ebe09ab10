import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:filestore_sdk/filestore_sdk.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import 'package:shared/shared.dart';

import '../../../../core.dart';
import '../../../data/source/api/client/isolate_api_client.dart';
import '../models/attachment_error.dart';
import '../models/worker_upload_file.dart';
import '../output/worker_compress_and_upload_images_output.dart';
import 'image_compress_handler.dart';
import 'upload_file_handler.dart';

class SafeCompleter<T> {
  final Completer<T> _completer = Completer<T>();
  bool _isCompleted = false;

  void complete(T value) {
    if (!_isCompleted) {
      _isCompleted = true;
      _completer.complete(value);
    }
  }

  void completeError(Object error, [StackTrace? stackTrace]) {
    if (!_isCompleted) {
      _isCompleted = true;
      _completer.completeError(error, stackTrace);
    }
  }

  bool get isCompleted => _isCompleted;

  Future<T> get future => _completer.future;
}

class CompressAndUploadImageHandler {
  final IsolateApiClient apiClient;
  final FilestoreClientV2 fileStoreClient;
  final String folderPath;
  final ImageCompressHandler imageCompressHandler;
  final RetryManager retryManager;

  CompressAndUploadImageHandler({
    required this.apiClient,
    required this.fileStoreClient,
    required this.folderPath,
    ImageCompressHandler? imageCompressHandler,
    RetryManager? retryManager,
  })  : imageCompressHandler = imageCompressHandler ?? ImageCompressHandler(),
        retryManager = retryManager ?? RetryManager();

  Future<CompressAndUploadImagesOutput> processing(
    WorkerUploadFile uploadFile,
    DateTime creationTime,
    String uploadKey,
  ) async {
    final completer = SafeCompleter<CompressAndUploadImagesOutput>();

    void safeComplete(CompressAndUploadImagesOutput output) {
      completer.complete(output);
    }

    runZonedGuarded(() async {
      if (creationTime
          .add(GlobalConfig.sendTimeoutDuration)
          .isBefore(DateTime.now())) {
        safeComplete(
          _handleError(
            AttachmentError(
              msgRef: uploadFile.ref,
              attachmentRef: uploadFile.fileRef!,
              message: 'Send timeout',
              canRetry: false,
            ),
          ),
        );
        return;
      }

      final filePath = await FileUtils.getImagePathFromFileRef(
        messageRef: uploadFile.messageRef!,
        fileRef: uploadFile.fileRef!,
      );

      if (filePath == null) {
        safeComplete(
          _handleError(
            AttachmentError(
              msgRef: uploadFile.ref,
              attachmentRef: uploadFile.fileRef!,
              message: 'File path is null',
              canRetry: false,
            ),
          ),
        );
        return;
      }

      final file = File(filePath);
      if (!await file.exists()) {
        safeComplete(
          _handleError(
            AttachmentError(
              msgRef: uploadFile.ref,
              attachmentRef: uploadFile.fileRef!,
              message: 'File does not exist at path: $filePath',
              canRetry: false,
            ),
          ),
        );
        return;
      }

      final outputPath = FileUtils.getCompressedOutputPath(
        messageRef: uploadFile.messageRef!,
        fileRef: uploadFile.fileRef!,
        originalFilePath: filePath,
      );

      await Directory(path.dirname(outputPath)).create(recursive: true);

      try {
        final originalSizeKB = file.lengthSync() / 1024;
        final sourceSize =
            await imageCompressHandler.getImageDimensionsFormFile(file);

        final targetSize = imageCompressHandler.getTargetSize(
          sourceSize.width,
          sourceSize.height,
        );

        final quality = imageCompressHandler.determineCompressionQuality(
          sourceSize,
          targetSize,
          originalSizeKB.toInt(),
        );

        final compressedFile = await imageCompressHandler.compressAndGetFile(
          filePath: filePath,
          targetSize: targetSize,
          quality: quality,
          outputPath: outputPath,
        );

        if (!compressedFile.existsSync()) {
          safeComplete(
            _handleError(
              AttachmentError(
                msgRef: uploadFile.ref,
                attachmentRef: uploadFile.fileRef!,
                message: 'Compression failed',
                canRetry: false,
              ),
            ),
          );
          return;
        }

        _logDebug(
          compressedFile,
          originalSizeKB,
          sourceSize,
          filePath,
          targetSize,
          quality,
        );

        final uploadFileInput = WorkerUploadFileInput(
          ref: uploadKey,
          taskName: TaskNameEnum.uploadFile.value,
          filePath: compressedFile.path,
          fileName: uploadFile.name,
          fileSize: uploadFile.size,
          fileRef: uploadFile.fileRef!,
          uploadType: UploadFileTypeEnum.video.value,
          messageRef: uploadFile.messageRef!,
          messageId: '',
        );

        final uploadFileOutput = await UploadFileHandler(
          apiClient: apiClient,
          fileStoreClient: fileStoreClient,
          folderPath: folderPath,
          retryManager: retryManager,
        ).uploadFile(uploadFileInput);

        unawaited(
          overwriteOriginalFile(
            filePath: filePath,
            newFile: compressedFile,
          ),
        );

        safeComplete(
          CompressAndUploadImagesOutput.success(
            uploadOutput: uploadFileOutput!,
            imageSize: targetSize,
          ),
        );
      } catch (e) {
        safeComplete(
          _handleError(
            AttachmentError(
              msgRef: uploadFile.ref,
              attachmentRef: uploadFile.fileRef!,
              message: 'Processing exception: $e',
            ),
          ),
        );
      }
    }, (Object error, StackTrace stackTrace) {
      safeComplete(
        _handleError(
          AttachmentError(
            msgRef: uploadFile.ref,
            attachmentRef: uploadFile.fileRef!,
            message: 'Upload exception: $error',
          ),
        ),
      );
    });

    return completer.future;
  }

  Future<List<CompressAndUploadImagesOutput>> processingMultiFiles(
    List<WorkerUploadFile> uploadFiles,
    DateTime creationTime,
    String uploadKey,
  ) async {
    if (creationTime
        .add(GlobalConfig.sendTimeoutDuration)
        .isBefore(DateTime.now())) {
      return uploadFiles
          .map(
            (file) => _handleError(
              AttachmentError(
                msgRef: file.ref,
                attachmentRef: file.fileRef!,
                message: 'Send timeout',
                canRetry: false,
              ),
            ),
          )
          .toList();
    }

    final outputs = <CompressAndUploadImagesOutput>[];
    final pathMap = <WorkerUploadFile, String>{};

    // Cache file paths and handle missing files
    for (final file in uploadFiles) {
      final path = await FileUtils.getImagePathFromFileRef(
        messageRef: file.messageRef!,
        fileRef: file.fileRef!,
      );
      if (path == null) {
        outputs.add(
          _handleError(
            AttachmentError(
              msgRef: file.ref,
              attachmentRef: file.fileRef!,
              message: 'File ${file.path} not exists',
              canRetry: false,
            ),
          ),
        );
      } else {
        pathMap[file] = path;
      }
    }

    if (pathMap.isEmpty) return outputs;

    // Prepare source and target sizes
    final filesToUpload = pathMap.keys.toList();
    final sourceSizes = await Future.wait(
      filesToUpload.map(
        (file) => imageCompressHandler.getImageDimensionsFormFile(
          File(pathMap[file]!),
        ),
      ),
    );
    final targetSizes = sourceSizes
        .map(
          (size) => imageCompressHandler.getTargetSize(size.width, size.height),
        )
        .toList();

    final completer = Completer<List<CompressAndUploadImagesOutput>>();

    runZonedGuarded(() async {
      final futures = <Future<CompressAndUploadImagesOutput>>[];

      for (var i = 0; i < filesToUpload.length; i++) {
        final file = filesToUpload[i];
        final path = pathMap[file]!;
        final originalSizeKB = File(path).lengthSync() / 1024;
        final quality = imageCompressHandler.determineCompressionQuality(
          sourceSizes[i],
          targetSizes[i],
          originalSizeKB.toInt(),
        );

        futures.add(
          _processSingleFile(
            file: file,
            filePath: path,
            targetSize: targetSizes[i],
            quality: quality,
            uploadKey: uploadKey,
          ),
        );
      }

      outputs.addAll(await Future.wait(futures));
      completer.complete(outputs);
    }, (error, stack) {
      for (final file in filesToUpload) {
        outputs.add(
          _handleError(
            AttachmentError(
              msgRef: file.ref,
              attachmentRef: file.fileRef!,
              message: 'Upload exception: $error',
            ),
          ),
        );
      }
      completer.complete(outputs);
    });

    return completer.future;
  }

  // Extracted single file processing into its own method
  Future<CompressAndUploadImagesOutput> _processSingleFile({
    required WorkerUploadFile file,
    required String filePath,
    required Size targetSize,
    required int quality,
    required String uploadKey,
  }) async {
    final compressedDir = FileUtils.getImageCompressedDirectory(
      messageRef: file.messageRef!,
      fileRef: file.fileRef!,
    );
    await compressedDir.create(recursive: true);

    final outputPath = FileUtils.getCompressedOutputPath(
      messageRef: file.messageRef!,
      fileRef: file.fileRef!,
      originalFilePath: filePath,
    );

    final compressedFile = await imageCompressHandler.compressAndGetFile(
      filePath: filePath,
      targetSize: targetSize,
      quality: quality,
      outputPath: outputPath,
    );

    if (!compressedFile.existsSync()) {
      return _handleError(
        AttachmentError(
          msgRef: file.ref,
          attachmentRef: file.fileRef!,
          message: 'Compression failed',
          canRetry: false,
        ),
      );
    }

    final uploadFileInput = WorkerUploadFileInput(
      ref: uploadKey,
      taskName: TaskNameEnum.uploadFile.value,
      filePath: compressedFile.path,
      fileName: file.name,
      fileSize: file.size,
      fileRef: file.fileRef!,
      uploadType: UploadFileTypeEnum.video.value,
      messageRef: file.messageRef!,
      messageId: '',
    );

    try {
      final output = await UploadFileHandler(
        apiClient: apiClient,
        fileStoreClient: fileStoreClient,
        folderPath: folderPath,
        retryManager: retryManager,
      ).uploadFile(uploadFileInput);

      unawaited(
        overwriteOriginalFile(
          filePath: filePath,
          newFile: compressedFile,
        ),
      );

      return CompressAndUploadImagesOutput.success(
        uploadOutput: output!,
        imageSize: targetSize,
      );
    } catch (e) {
      return _handleError(
        AttachmentError(
          msgRef: file.ref,
          attachmentRef: file.fileRef!,
          message: 'Processing exception: $e',
        ),
      );
    }
  }

  Future<void> overwriteOriginalFile({
    required String filePath,
    required File newFile,
  }) async {
    try {
      final originalFile = File(filePath);

      if (await originalFile.exists()) {
        await originalFile.delete();
      }

      await newFile.rename(filePath);
    } catch (e) {
      debugPrint('Failed to overwrite file at $filePath: $e');
    }
  }

  void _logDebug(
    File imageCompressData,
    double originalSizeKB,
    Size sourceSize,
    String filePath,
    Size targetSize,
    int quality,
  ) {
    if (!kDebugMode) return;

    final compressedSizeKB = imageCompressData.lengthSync() / 1024;
    final sizeReduction = originalSizeKB - compressedSizeKB;
    final actualReductionPercentage = (sizeReduction / originalSizeKB * 100);

    debugPrint('''
   CompressAndUploadImageHandler Result:
    | OriginalWidth: ${sourceSize.width}
    | OriginalHeight: ${sourceSize.height}
    | OriginalSize: ${originalSizeKB.toStringAsFixed(2)}KB
    | OriginalPath: $filePath
    | width: ${targetSize.width.toStringAsFixed(0)}
    | height: ${targetSize.height.toStringAsFixed(0)}
    | quality: $quality%
    | Original size: ${originalSizeKB.toStringAsFixed(2)}KB
    | Compressed size: ${compressedSizeKB.toStringAsFixed(2)}KB
    | Reduced: ${sizeReduction.toStringAsFixed(2)}KB (${actualReductionPercentage.toStringAsFixed(2)}%)
    | Effective quality: ${(quality * (compressedSizeKB / originalSizeKB)).toStringAsFixed(0)}%
    ''');
  }

  CompressAndUploadImagesOutput _handleError(AttachmentError error) {
    // Always notify about attachment errors, regardless of retry status
    final sendPort = IsolateNameServer.lookupPortByName(error.msgRef);
    if (sendPort != null) {
      sendPort.send(
        SendAttachmentFailureResponse(
          ref: error.msgRef,
          attachmentRef: error.attachmentRef,
          errorMessage: error.message,
        ).toJson(),
      );
      if (kDebugMode) {
        debugPrint(
          'CompressAndUploadImageHandler: Notified about attachment error: ${error.message}',
        );
      }
    } else if (kDebugMode) {
      debugPrint(
        'CompressAndUploadImageHandler: Could not notify about attachment error (SendPort not found): ${error.message}',
      );

      // As a fallback, try to notify using the general message failure mechanism
      final generalSendPort = IsolateNameServer.lookupPortByName(error.msgRef);
      if (generalSendPort != null) {
        generalSendPort.send(
          SendMessageFailureResponse(
            ref: error.msgRef,
            errorMessage: 'Attachment error: ${error.message}',
          ).toJson(),
        );
        debugPrint(
          'CompressAndUploadImageHandler: Sent general message failure notification as fallback',
        );
      }
    }
    return CompressAndUploadImagesOutput.error(error: error);
  }
}
