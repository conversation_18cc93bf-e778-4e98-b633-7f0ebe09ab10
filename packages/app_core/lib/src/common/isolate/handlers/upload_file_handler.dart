import 'dart:async';
import 'dart:io';

import 'package:filestore_sdk/filestore_sdk.dart';
import 'package:flutter/foundation.dart';
import 'package:shared/shared.dart';

import '../../../data/source/api/client/isolate_api_client.dart';
import '../../network/retry_manager.dart';
import '../core/utils/logger.dart';
import '../exceptions/file_not_found_exception.dart';
import '../exceptions/send_message_timeout_exception.dart';
import '../input/worker_upload_file_input.dart';
import '../models/worker_file_metadata.dart';
import '../output/worker_upload_file_output.dart';

/// Wrapper for Completer to check completion status
class SafeCompleter<T> {
  final Completer<T> _completer = Completer<T>();
  bool _isCompleted = false;

  /// Complete the completer if not already completed
  void complete(T value) {
    if (!_isCompleted) {
      _isCompleted = true;
      _completer.complete(value);
    }
  }

  /// Complete the completer with error if not already completed
  void completeError(Object error, [StackTrace? stackTrace]) {
    if (!_isCompleted) {
      _isCompleted = true;
      _completer.completeError(error, stackTrace);
    }
  }

  /// Check if the completer has been completed
  bool get isCompleted => _isCompleted;

  /// Access the future of the completer
  Future<T> get future => _completer.future;
}

class UploadFileHandler {
  final IsolateApiClient apiClient;
  final FilestoreClientV2 fileStoreClient;
  final String folderPath;
  final RetryManager retryManager;

  UploadFileHandler({
    required this.apiClient,
    required this.fileStoreClient,
    required this.folderPath,
    required this.retryManager,
  });

  Future<WorkerUploadFileOutput?> uploadFile(
    WorkerUploadFileInput inputData,
  ) async {
    const int maxRetries = 3; // Reduced from 100 to 3 for better performance
    const Duration retryDelay = GlobalConfig.retryDelayAfterWsUploadBusy;
    int attempt = 0;
    final messageRef = inputData.messageRef;
    final fileRef = inputData.fileRef;
    final uploadType = UploadFileTypeEnum.getEnumByValue(inputData.uploadType);

    RILogger.printClassMethodDebug(
      'UploadFileHandler',
      'uploadFile',
      '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Starting upload file with type: ${uploadType?.value}',
    );

    while (true) {
      try {
        final completer = SafeCompleter<WorkerUploadFileOutput?>();
        runZonedGuarded(() {
          if (inputData.mediaMessageBody?.creationTime
                  .add(GlobalConfig.sendTimeoutDuration)
                  .isBefore(DateTime.now()) ??
              false) {
            RILogger.printClassMethodDebug(
              'UploadFileHandler',
              'uploadFile',
              '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Task timed out, throwing SendMessageTimeoutException',
            );
            throw SendMessageTimeoutException(
              msgRef: inputData.mediaMessageBody!.ref,
            );
          }

          final path =
              inputData.uploadType == UploadFileTypeEnum.videoThumbnail.value
                  ? inputData.thumbnailPath!
                  : inputData.filePath;
          final filePath = path;

          RILogger.printClassMethodDebug(
            'UploadFileHandler',
            'uploadFile',
            '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Checking if file exists: $filePath',
          );

          if (!File(filePath).existsSync()) {
            RILogger.printClassMethodDebug(
              'UploadFileHandler',
              'uploadFile',
              '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] File not found: $filePath',
            );
            throw FileNotFoundException(message: 'File ${filePath} not exists');
          }

          RILogger.printClassMethodDebug(
            'UploadFileHandler',
            'uploadFile',
            '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] File exists, size: ${File(filePath).lengthSync() ~/ 1024} KB',
          );

          final name =
              inputData.uploadType == UploadFileTypeEnum.videoThumbnail.value
                  ? ''
                  : inputData.fileName;
          final size =
              inputData.uploadType == UploadFileTypeEnum.videoThumbnail.value
                  ? File(inputData.thumbnailPath!).lengthSync()
                  : inputData.fileSize;
          RILogger.printClassMethodDebug(
            'UploadFileHandler',
            'uploadFile',
            '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Starting file upload to server with fileStoreClient.uploadFile',
          );

          final uploadStartTime = DateTime.now();

          fileStoreClient.uploadFile(
            UpFile(
              path: path,
              name: name,
              size: size,
              key: inputData.mediaMessageBody?.uploadKey,
              ref: inputData.fileRef,
            ),
            onSuccess: (file, fileUrl) {
              final uploadDuration =
                  DateTime.now().difference(uploadStartTime).inMilliseconds;
              RILogger.printClassMethodDebug(
                'UploadFileHandler',
                'uploadFile',
                '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Upload successful in ${uploadDuration}ms, fileUrl: $fileUrl',
              );

              return completer.complete(
                WorkerUploadFileOutput(
                  url: fileUrl,
                  metadata: WorkerFileMetadata(
                    filesize: file.metaData?.fileSize,
                    filename: file.metaData?.name,
                    extension: file.metaData?.fileType,
                    mimetype: file.metaData?.type,
                  ),
                ),
              );
            },
            onError: (file, errorCode, errorMessage) {
              final uploadDuration =
                  DateTime.now().difference(uploadStartTime).inMilliseconds;
              RILogger.printClassMethodDebug(
                'UploadFileHandler',
                'uploadFile',
                '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Upload failed after ${uploadDuration}ms with error code: $errorCode, message: $errorMessage',
              );

              // Check if error is WebSocket busy related
              if (_isWebSocketBusyError(errorMessage)) {
                return completer.completeError(
                  WebSocketBusyException(errorMessage),
                );
              }
              return completer.completeError(errorMessage);
            },
            getTokenCallback: getTokenExchange,
            folderPath: folderPath,
          );
        }, (Object error, StackTrace stackTrace) {
          RILogger.printClassMethodDebug(
            'UploadFileHandler',
            'uploadFile',
            '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Error in runZonedGuarded: $error',
          );
          RILogger.printClassMethodDebug(
            'UploadFileHandler',
            'uploadFile',
            '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Stack trace: $stackTrace',
          );

          // Check if error is WebSocket busy related
          if (_isWebSocketBusyError(error.toString())) {
            return completer.completeError(
              WebSocketBusyException(error.toString()),
            );
          }
          return completer.completeError(error);
        });

        final result = await completer.future;
        return result;
      } catch (e) {
        // Handle WebSocket busy errors with retry logic
        if (_isWebSocketBusyError(e.toString()) || e is WebSocketBusyException) {
          attempt++;
          if (attempt >= maxRetries) {
            RILogger.printClassMethodDebug(
              'UploadFileHandler',
              'uploadFile',
              '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Max retry attempts ($maxRetries) reached for WebSocket busy error. Giving up.',
            );
            rethrow;
          }

          RILogger.printClassMethodDebug(
            'UploadFileHandler',
            'uploadFile',
            '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] WebSocket busy detected, retrying after ${retryDelay.inSeconds}s... (attempt $attempt/$maxRetries)',
          );

          await Future.delayed(retryDelay);
          continue;
        }
        rethrow;
      }
    }
  }

  /// Check if the error message indicates WebSocket busy condition
  bool _isWebSocketBusyError(String errorMessage) {
    return errorMessage.contains('All WebSockets are busy') ||
           errorMessage.contains('WebSocket') && errorMessage.contains('busy');
  }

  Future<WorkerUploadFileOutput?> uploadFileData(
    Uint8List fileData,
    String uploadKey,
    String fileRef,
  ) async {
    final completer = SafeCompleter<WorkerUploadFileOutput?>();

    runZonedGuarded(() {
      fileStoreClient.uploadFile(
        UpFile(
          path: '',
          name: '',
          fileData: fileData,
          size: fileData.length,
          key: uploadKey,
          ref: fileRef,
        ),
        onSuccess: (file, fileUrl) {
          return completer.complete(
            WorkerUploadFileOutput(
              url: fileUrl,
              metadata: WorkerFileMetadata(
                filesize: file.metaData?.fileSize,
                filename: file.metaData?.name,
                extension: file.metaData?.fileType,
                mimetype: file.metaData?.type,
              ),
            ),
          );
        },
        onError: (file, errorCode, errorMessage) {
          return completer.completeError(errorMessage);
        },
        getTokenCallback: getTokenExchange,
        folderPath: folderPath,
      );
    }, (Object error, StackTrace stackTrace) {
      return completer.completeError(error);
    });
    return completer.future;
  }

  Future<String?> getTokenExchange() async {
    try {
      RILogger.printClassMethodDebug(
        'UploadFileHandler',
        'getTokenExchange',
        'Getting token exchange from API',
      );

      // Use RetryManager to handle network retries
      final response = await retryManager.retry(
        task: () => apiClient.post(
          '/Auth/TokenExchange',
          body: {'tokenType': 1},
        ),
        taskId: 'token_exchange',
      );

      RILogger.printClassMethodDebug(
        'UploadFileHandler',
        'getTokenExchange',
        'Token exchange successful',
      );

      return response.data['data'];
    } catch (e) {
      RILogger.printClassMethodDebug(
        'UploadFileHandler',
        'getTokenExchange',
        'Error getting token exchange: $e',
      );
      return null;
    }
  }
}
