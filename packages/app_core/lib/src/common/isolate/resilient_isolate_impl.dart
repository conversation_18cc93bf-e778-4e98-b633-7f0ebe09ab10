import 'dart:async';
import 'dart:isolate';
import 'dart:math' as math;
import 'dart:ui';

import 'package:flutter_isolate/flutter_isolate.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';
import 'package:synchronized/extension.dart';
import 'package:synchronized/synchronized.dart';
import 'package:workmanager/workmanager.dart';

import '../../../core.dart';
import '../../data/event/message_created_from_me.dart';
import '../config/send_message_config.dart';
import 'data/stores/ack_store.dart';
import 'data/stores/shared_preferences_store.dart';
import 'domain/isolate_event_listener.dart';
import 'domain/models/task_model.dart';
import 'domain/models/task_priority.dart';
import 'domain/priority_task_queue.dart';
import 'services/isolate_health_monitor.dart';
import 'services/worker_isolate.dart';

/// Type of executor to process tasks
enum ExecutorType {
  /// Use isolate to process tasks (foreground)
  isolate,

  /// Use worker to process tasks (background)
  worker,
}

extension ExecutorTypeX on ExecutorType {
  bool sameType(ExecutorType other) => this == other;
}

/// Class to manage locks for sequential tasks
class TaskLock {
  final String messageRef;
  final String taskName;
  final String taskId;
  final DateTime createdAt;
  bool isReleased = false;

  TaskLock({
    required this.messageRef,
    required this.taskName,
    required this.taskId,
  }) : createdAt = DateTime.now();

  /// Release the lock
  void release() {
    isReleased = true;
  }

  /// Check if the lock has expired
  bool isExpired(Duration timeout) {
    return DateTime.now().difference(createdAt) > timeout;
  }
}

class ResilientIsolate {
  static final ResilientIsolate _instance = ResilientIsolate._internal();

  factory ResilientIsolate() {
    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      'factory',
      'Getting ResilientIsolate instance: ${identityHashCode(_instance)}',
    );
    return _instance;
  }

  ResilientIsolate._internal() {
    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      '_internal',
      'Creating new ResilientIsolate instance: ${identityHashCode(this)}',
    );

    // Log initial value of _currentExecutor
    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      '_internal',
      'Initial value of _currentExecutor: $_currentExecutor',
    );
    AppEventBus().on<MessageCreatedFromMeEvent>().listen(_onMessageSent);

    // Listen for WebSocket resume completed event
    _webSocketResumeSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<WebSocketResumeCompletedEvent>()
        .listen((_) {
      // Mark WebSocket as resumed
      _webSocketResumeCompleted = true;

      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_internal',
        'WebSocket resume completed, checking if isolate is initialized',
      );

      // If isolate has been initialized, proceed with task recovery
      if (_isolateInitialized) {
        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_internal',
          'Isolate is initialized, recovering pending tasks',
        );
        _recoverPendingTasks();
      }
    });
  }

  void _onMessageSent(MessageCreatedFromMeEvent event) {
    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      '_onMessageSent',
      'Received message created from me event: ${event.toJson()}',
    );

    final data = event.data as Map<String, dynamic>?;
    if (data == null) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_onMessageSent',
        'Event data is null, cannot process',
      );
      return;
    }

    final ref = data['ref'] as String?;

    if (ref == null) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_onMessageSent',
        'Ref is null, cannot process',
      );
      return;
    }

    // Remove task from queue
    _removeTaskFromQueueByRef(ref);

    SharedPreferencesStore.deleteTask(ref);
  }

  /// Remove task from queue based on ref
  void _removeTaskFromQueueByRef(String ref) {
    _taskQueue.remove(ref);
  }

  FlutterIsolate? _workerIsolate;
  final _taskQueue = PriorityTaskQueue();
  final _callbackMap = <String, Completer<bool>>{};
  final _taskResults = <String, dynamic>{};
  int _runningTaskCount = 0;
  int _maxConcurrent = 10;

  // Map to track running tasks by task name
  final Map<String, int> _runningTasksByName = {};
  bool _isInitialized = false;
  bool _isWorkerInitialized = false;
  bool _isProcessingQueue = false;

  // Variable to track the initialization state of the isolate
  bool _isolateInitialized = false;

  // Variable to track the resume state of WebSocket
  bool _webSocketResumeCompleted = false;

  // Subscription to listen for WebSocket resume completed event
  StreamSubscription? _webSocketResumeSubscription;

  // Variable to track the lifecycle state of the app
  AppLifecycleState _appLifecycleState = AppLifecycleState.resumed;

  // Stream controller to emit AppLifecycleState change events
  final _appLifecycleStateController =
      StreamController<AppLifecycleState>.broadcast();

  // Lock to ensure only one process is switching between isolate and worker at a time
  final _executorSwitchLock = Lock();

  // Variable to track the state of switching between isolate and worker
  bool _isSwitchingExecutors = false;

  // Variable to track the current executor (isolate or worker)
  ExecutorType _currentExecutor = ExecutorType.isolate;

  // List of tasks waiting for executor switch
  final List<TaskModel> _pendingSwitchTasks = [];

  // Map to store timeout timers for tasks
  final Map<String, Timer> _taskTimeoutTimers = {};

  // Lock to ensure only one isolate initialization process at a time
  final _isolateInitLock = Object();
  bool _isInitializingIsolate = false;

  // Number of retry attempts when initializing isolate
  int _isolateInitRetryCount = 0;

  int _heartbeatInterval = 30000;

  bool _enableHealthMonitoring = true;

  // Store tasks that need to be recovered after isolate initialization
  List<TaskModel> _pendingRecoveryTasks = [];

  // Store tasks that are waiting for isolate to be ready
  List<TaskModel> _pendingIsolateReadyTasks = [];

  // Special handling for sequential tasks
  List<String> get _sequentialTasks => SendMessageConfig.sequentialTasks;

  // Map to track running sequential tasks by messageRef
  final Map<String, String> _runningSequentialTasksByMessageRef = {};

  // Map to track start time of running tasks
  final Map<String, int> _runningTasksStartTime = {};

  // Separate queue for video tasks (compressAndUploadVideoMessage, compressVideoMessage)
  final PriorityTaskQueue _videoTaskQueue = PriorityTaskQueue();

  // Variable to track the state of processing video tasks
  bool _isProcessingVideoTask = false;

  // Lock to ensure only one video task processing at a time
  final _videoTaskLock = Lock();

  // Variable to track the time of the most recent _processQueue call
  int _lastProcessQueueTime = 0;

  // Class to manage locks for sequential tasks
  final Map<String, TaskLock> _sequentialTaskLocks = {};

  /// Create timer to automatically detect timeout for tasks
  void _createTaskTimeoutTimer(TaskModel task) {
    _cancelTaskTimeoutTimer(task.id);

    // Use GlobalConfig.sendTimeoutDuration for all tasks
    int timeoutDuration = GlobalConfig.sendTimeoutDuration.inMilliseconds;

    // Calculate timeout duration based on task type
    if (task.name == TaskNameEnum.compressVideoMessage.value ||
        task.name == TaskNameEnum.compressAndUploadVideoMessage.value) {
      // 10 minutes for video compression task or GlobalConfig.sendTimeoutDuration, whichever is longer
      timeoutDuration = math.max(timeoutDuration, 10 * 60 * 1000);
    } else if (task.name == 'uploadFile') {
      // 5 minutes for file upload task or GlobalConfig.sendTimeoutDuration, whichever is longer
      timeoutDuration = math.max(timeoutDuration, 5 * 60 * 1000);
    }

    _taskTimeoutTimers[task.id] =
        Timer(Duration(milliseconds: timeoutDuration), () {
      RILogger.printTaskDebug(
        'ResilientIsolate',
        '_createTaskTimeoutTimer',
        task.id,
        'Task timed out after ${timeoutDuration}ms',
      );

      // Emit timeout event
      _eventListener.emitEvent(
        IsolateEvent(
          type: IsolateEventType.taskTimeout,
          taskId: task.id,
          data: {
            'name': task.name,
            'timeout': timeoutDuration,
            'inputData': task.inputData,
          },
        ),
      );

      // Handle timeout for SendMessageListener if needed
      _handleTaskTimeout(task);

      // Update task status and emit failure event
      _updateTaskStatusToFailed(task);
    });
  }

  /// Cancel timeout timer for task
  void _cancelTaskTimeoutTimer(String taskId) {
    if (_taskTimeoutTimers.containsKey(taskId)) {
      _taskTimeoutTimers[taskId]?.cancel();
      _taskTimeoutTimers.remove(taskId);
    }
  }

  /// Handle timeout for SendMessageListener
  void _handleTaskTimeout(TaskModel task) {
    // Check if the task is a message sending task
    if (task.name.contains('Message') || task.name.contains('Upload')) {
      // Find msgRef in inputData
      final msgRef = task.inputData['ref'] as String?;
      if (msgRef != null) {
        // Send timeout notification to SendMessageListener
        final sendPort = IsolateNameServer.lookupPortByName(msgRef);
        sendPort?.send({
          'isSendMessageFailure': true,
          'ref': msgRef,
          'errorMessage': 'Task timed out after ${task.timeout}ms',
        });
      }
    }
  }

  /// Update task status to failed due to timeout
  Future<void> _updateTaskStatusToFailed(TaskModel task) async {
    try {
      // Special handling for sequential tasks when timeout occurs
      if (_sequentialTasks.contains(task.name)) {
        // Check if there is a messageRef in inputData
        final messageRef = task.inputData['messageRef'] as String? ??
            task.inputData['ref'] as String?;

        if (messageRef != null) {
          // Release the lock for sequential task
          _releaseSequentialTaskLock(messageRef);

          // Keep old handling for _runningSequentialTasksByMessageRef to ensure backward compatibility
          if (_runningSequentialTasksByMessageRef.containsKey(messageRef)) {
            final taskName =
                _runningSequentialTasksByMessageRef.remove(messageRef);
            RILogger.printTaskDebug(
              'ResilientIsolate',
              '_updateTaskStatusToFailed',
              task.id,
              'Removed timed out sequential task $taskName with messageRef $messageRef from running list',
            );
          }

          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_updateTaskStatusToFailed',
            task.id,
            'Released sequential task lock for timed out task ${task.name} with messageRef $messageRef',
          );
        }
      }

      // Update task status in storage
      final now = DateTime.now().millisecondsSinceEpoch;
      final updatedTask =
          task.copyWith(status: TaskStatus.failed, completedAt: now);
      await SharedPreferencesStore.updateTask(updatedTask);

      // Emit failure event
      _eventListener.emitEvent(
        IsolateEvent(
          type: IsolateEventType.taskFailed,
          taskId: task.id,
          data: {
            'error': 'Task timed out after ${task.timeout}ms',
            'name': task.name,
          },
        ),
      );

      // Decrease the number of running tasks
      _runningTaskCount = math.max(0, _runningTaskCount - 1);

      // Handle retry if needed
      if (task.retryCount < task.maxRetries) {
        final retryTask = task.copyWith(
          status: TaskStatus.pending,
          retryCount: task.retryCount + 1,
        );

        await SharedPreferencesStore.updateTask(retryTask);

        // Add back to queue after delay
        Future.delayed(Duration(milliseconds: task.retryDelay), () {
          _taskQueue.add(retryTask);
          // Only call _processQueue() when there are waiting tasks
          if (_taskQueue.isNotEmpty) {
            _processQueue();
          }
        });

        RILogger.printTaskDebug(
          'ResilientIsolate',
          '_updateTaskStatusToFailed',
          task.id,
          'Task scheduled for retry after timeout, attempt: ${retryTask.retryCount}',
        );
      } else if (_callbackMap.containsKey(task.id)) {
        // Complete the future if it exists
        _callbackMap[task.id]!.complete(false);
        _callbackMap.remove(task.id);
      }

      // Process queue to start next task if there are pending tasks
      if (_taskQueue.isNotEmpty) {
        _scheduleQueueProcessing();
      }
    } catch (e) {
      RILogger.printTaskError(task.id, 'Error updating task status: $e');
    }
  }

  // Currently running sequential task
  // We don't use a global _runningSequentialTask anymore, but track by messageRef

  // Event listener for broadcasting events
  final _eventListener = IsolateEventListener();

  // Communication ports
  SendPort? _requestPort;
  ReceivePort? _responsePort;
  ReceivePort? _ackPort;

  // Port to register tasks from worker isolate
  SendPort? _taskRegistrationPort;

  // Getters
  bool get isInitialized => _isInitialized;
  int get maxConcurrent => _maxConcurrent;
  set maxConcurrent(int value) {
    _maxConcurrent = math.max(1, value); // Ensure at least 1
  }

  // Get the event listener
  IsolateEventListener get eventListener => _eventListener;

  Map<String, dynamic> getAllTaskResults() {
    return Map.from(_taskResults);
  }

  /// Handle app lifecycle state change events
  ///
  /// This method is called from outside (usually from main.dart) when
  /// the app's lifecycle state changes. It will update the state
  /// and emit events through the stream.
  void handleAppLifecycleState(AppLifecycleState state) {
    // Save old state for comparison
    final oldState = _appLifecycleState;
    _appLifecycleState = state;

    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      'handleAppLifecycleState',
      'App lifecycle state changed from $oldState to $state. Current executor: $_currentExecutor',
    );

    // Emit event through stream
    if (!_appLifecycleStateController.isClosed) {
      _appLifecycleStateController.add(state);
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        'handleAppLifecycleState',
        'Event added to stream',
      );
    } else {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        'handleAppLifecycleState',
        'Stream is closed, cannot add event',
      );
    }

    // Only handle executor switching if not already in the process of switching
    if (!_isSwitchingExecutors) {
      // Handle switching between isolate and worker based on lifecycle state
      _handleExecutorSwitchBasedOnLifecycle(state);
    } else {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        'handleAppLifecycleState',
        'Executor switch already in progress, ignoring lifecycle state change',
      );
    }
  }

  /// Handle switching between isolate and worker based on lifecycle state
  void _handleExecutorSwitchBasedOnLifecycle(AppLifecycleState state) {
    // Early return if not initialized
    if (!_isInitialized) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_handleExecutorSwitchBasedOnLifecycle',
        'ResilientIsolate not initialized, ignoring lifecycle state change to $state',
      );
      return;
    }

    // Early return if already switching
    if (_isSwitchingExecutors) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_handleExecutorSwitchBasedOnLifecycle',
        'Already switching executors, ignoring lifecycle state change to $state',
      );
      return;
    }

    // Early return if unknown state
    if (state != AppLifecycleState.paused &&
        state != AppLifecycleState.inactive &&
        state != AppLifecycleState.detached &&
        state != AppLifecycleState.resumed) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_handleExecutorSwitchBasedOnLifecycle',
        'Unknown state: $state, not switching',
      );
      return;
    }

    // Switch to worker when app goes to background
    if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.inactive ||
        state == AppLifecycleState.detached) {
      if (_currentExecutor != ExecutorType.isolate) {
        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_handleExecutorSwitchBasedOnLifecycle',
          'Current executor is not isolate, not switching. Current executor: $_currentExecutor',
        );
        return;
      }
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_handleExecutorSwitchBasedOnLifecycle',
        'App going to background, switching from isolate to worker. State: $state',
      );
      _switchToWorker();
      return;
    }

    // Switch to isolate when app returns to foreground
    if (state == AppLifecycleState.resumed) {
      if (_currentExecutor != ExecutorType.worker) {
        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_handleExecutorSwitchBasedOnLifecycle',
          'Current executor is not worker, not switching. Current executor: $_currentExecutor',
        );
        return;
      }
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_handleExecutorSwitchBasedOnLifecycle',
        'App returning to foreground, switching from worker to isolate. State: $state',
      );
      _switchToIsolate();
    }
  }

  /// Stream of app lifecycle state change events
  Stream<AppLifecycleState> get appLifecycleStateStream =>
      _appLifecycleStateController.stream;

  /// Switch from isolate to worker when app goes to background
  Future<void> _switchToWorker() async {
    await SharedPreferencesStore.setExecutorType(ExecutorType.worker);
    // Use lock to ensure only one switching process at a time
    return _executorSwitchLock.synchronized(() async {
      try {
        // If already switching or not using isolate, skip
        if (_isSwitchingExecutors || _currentExecutor != ExecutorType.isolate) {
          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_switchToWorker',
            'Already switching or not using isolate, skipping. Current executor: $_currentExecutor, isSwitching: $_isSwitchingExecutors',
          );
          return;
        }

        _isSwitchingExecutors = true;

        // Add timeout to ensure _isSwitchingExecutors doesn't get "stuck" in true state
        Timer(Duration(seconds: 30), () {
          if (_isSwitchingExecutors) {
            RILogger.printClassMethodDebug(
              'ResilientIsolate',
              '_switchToWorker',
              'Executor switch timeout reached, forcing _isSwitchingExecutors to false',
            );
            _isSwitchingExecutors = false;
          }
        });

        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_switchToWorker',
          'Starting switch from isolate to worker',
        );

        // 1. Kill isolate first to avoid tasks running in both executors
        await _killIsolate();

        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_switchToWorker',
          'Isolate killed successfully',
        );

        if (_enableHealthMonitoring) {
          IsolateHealthMonitor().stopMonitoring();

          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_switchToWorker',
            'Stopped isolate health monitoring when switching to worker',
          );
        }

        _currentExecutor = ExecutorType.worker;

        try {
          await _registerWorkerTask();
        } catch (e, stackTrace) {
          RILogger.printError(
            'Error registering composite task with Workmanager',
            e,
            stackTrace,
          );
          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_switchToWorker',
            'Error registering composite task: $e',
          );
        }

        final tasks = await SharedPreferencesStore.loadAllTasks();

        // 5. Save tasks to SharedPreferencesStore
        int successCount = 0;
        int failCount = 0;

        if (tasks.isNotEmpty) {
          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_switchToWorker',
            'Saving ${tasks.length} tasks to SharedPreferencesStore',
          );

          for (final task in tasks) {
            // Only save tasks that are pending or running
            if (task.status == TaskStatus.pending ||
                task.status == TaskStatus.running) {
              try {
                // Update task creation time
                final updatedTask = task.copyWith(
                  createdAt: DateTime.now().millisecondsSinceEpoch,
                );

                // Save task to SharedPreferencesStore
                await SharedPreferencesStore.saveTask(updatedTask);
                successCount++;
              } catch (e, stackTrace) {
                failCount++;
                RILogger.printError(
                  'Error saving task ${task.id} (${task.name}) to SharedPreferencesStore',
                  e,
                  stackTrace,
                );
              }
            }
          }
        }

        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_switchToWorker',
          'Switch to worker completed successfully. $successCount tasks registered, $failCount tasks failed to register.',
        );
      } catch (e, stackTrace) {
        RILogger.printError(
          'Error switching from isolate to worker',
          e,
          stackTrace,
        );
      } finally {
        _isSwitchingExecutors = false;

        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_switchToWorker',
          'Executor switch completed, _isSwitchingExecutors set to false',
        );
      }
    });
  }

  Future<void> _registerWorkerTask() async {
    await Workmanager().registerOneOffTask(
      "process_pending_tasks",
      "process_pending_tasks",
      existingWorkPolicy: ExistingWorkPolicy.replace,
      constraints: Constraints(networkType: NetworkType.connected),
      backoffPolicy: BackoffPolicy.linear,
      initialDelay: Duration.zero,
    );

    final tasks = await SharedPreferencesStore.loadAllTasks();

    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      '_registerWorkerTask',
      'TaskBox changed: ${tasks.length} tasks | ' +
          'IDs: ${tasks.map(
                (t) => '\n${t.id} - ${t.status}',
              ).join(', ')}',
    );

    // Debug log for registration
    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      '_registerWorkerTask',
      'Registered composite task with Workmanager (debounced)',
    );
  }

  /// Switch from worker to isolate when app returns to foreground
  Future<void> _switchToIsolate() async {
    await SharedPreferencesStore.setExecutorType(ExecutorType.isolate);
    // Use lock to ensure only one switching process at a time
    return _executorSwitchLock.synchronized(() async {
      try {
        // If already switching or not using worker, skip
        if (_isSwitchingExecutors || _currentExecutor != ExecutorType.worker) {
          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_switchToIsolate',
            'Already switching or not using worker, skipping. Current executor: $_currentExecutor, isSwitching: $_isSwitchingExecutors',
          );
          return;
        }

        _isSwitchingExecutors = true;

        // Add timeout to ensure _isSwitchingExecutors doesn't get "stuck" in true state
        Timer(Duration(seconds: 30), () {
          if (_isSwitchingExecutors) {
            RILogger.printClassMethodDebug(
              'ResilientIsolate',
              '_switchToIsolate',
              'Executor switch timeout reached, forcing _isSwitchingExecutors to false',
            );
            _isSwitchingExecutors = false;
          }
        });

        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_switchToIsolate',
          'Starting switch from worker to isolate',
        );

        // 1. Stop worker (cancel tasks registered with Workmanager)
        await _stopWorker();

        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_switchToIsolate',
          'Worker stopped successfully',
        );

        // 2. Get task list ONLY from SharedPreferencesStore
        final tasks = await SharedPreferencesStore.loadAllTasks();

        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_switchToIsolate',
          'Retrieved ${tasks.length} tasks from SharedPreferencesStore',
        );

        // 3. Reset all variables in memory
        _resetInMemoryTaskState();

        // 4. Restart isolate
        await _ensureIsolateRunning();

        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_switchToIsolate',
          'Isolate started successfully',
        );

        // 4.1 Re-enable heartbeat monitoring when switching back to isolate
        if (_enableHealthMonitoring) {
          IsolateHealthMonitor().startMonitoring(
            heartbeatInterval: _heartbeatInterval,
            onIsolateUnresponsive: _handleUnresponsiveIsolate,
          );

          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_switchToIsolate',
            'Started isolate health monitoring when switching to isolate with interval: $_heartbeatInterval ms',
          );
        }

        // 5. Set current state to isolate
        _currentExecutor = ExecutorType.isolate;

        // 6. Re-register tasks with isolate
        int successCount = 0;
        int failCount = 0;

        if (tasks.isNotEmpty) {
          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_switchToIsolate',
            'Registering ${tasks.length} tasks with isolate',
          );

          for (final task in tasks) {
            // Only register tasks that are pending or running
            if (task.status == TaskStatus.pending ||
                task.status == TaskStatus.running) {
              try {
                await registerTask(
                  taskId: task.id,
                  taskName: task.name,
                  inputData: task.inputData,
                  priority: task.priority,
                  isReferenceTask: task.isReferenceTask,
                  networkRequired: task.networkRequired,
                );
                successCount++;
              } catch (e, stackTrace) {
                failCount++;
                RILogger.printError(
                  'Error registering task ${task.id} (${task.name}) with isolate',
                  e,
                  stackTrace,
                );
              }
            }
          }
        }

        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_switchToIsolate',
          'Switch to isolate completed successfully. $successCount tasks registered, $failCount tasks failed to register.',
        );

        // 7. Process tasks waiting in the queue if any
        if (_taskQueue.isNotEmpty) {
          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_switchToIsolate',
            'Processing ${_taskQueue.length} tasks in queue',
          );

          Future.microtask(() => _processQueue());
        }

        // 8. Process tasks in the pending list
        if (_pendingSwitchTasks.isNotEmpty) {
          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_switchToIsolate',
            'Processing ${_pendingSwitchTasks.length} tasks in pending list',
          );

          final pendingTasks = List<TaskModel>.from(_pendingSwitchTasks);
          _pendingSwitchTasks.clear();

          for (final task in pendingTasks) {
            try {
              await registerTask(
                taskId: task.id,
                taskName: task.name,
                inputData: task.inputData,
                priority: task.priority,
                isReferenceTask: task.isReferenceTask,
                networkRequired: task.networkRequired,
              );

              RILogger.printClassMethodDebug(
                'ResilientIsolate',
                '_switchToIsolate',
                'Processed pending task ${task.id} (${task.name})',
              );
            } catch (e, stackTrace) {
              RILogger.printError(
                'Error processing pending task ${task.id} (${task.name})',
                e,
                stackTrace,
              );
            }
          }
        }
      } catch (e, stackTrace) {
        RILogger.printError(
          'Error switching from worker to isolate',
          e,
          stackTrace,
        );
      } finally {
        _isSwitchingExecutors = false;

        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_switchToIsolate',
          'Executor switch completed, _isSwitchingExecutors set to false',
        );
      }
    });
  }

  /// Update heartbeat check interval
  ///
  /// This method will update IsolateHealthMonitor if it's enabled
  /// Uses the value from SendMessageConfig.heartbeatInterval
  void updateHeartbeatInterval() {
    // Use value from static configuration
    _heartbeatInterval = SendMessageConfig.heartbeatInterval;

    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      'updateHeartbeatInterval',
      'Updated heartbeat interval to $_heartbeatInterval ms',
    );

    // Update IsolateHealthMonitor if it's enabled
    if (_enableHealthMonitoring && _isInitialized) {
      IsolateHealthMonitor().updateHeartbeatInterval(_heartbeatInterval);
    }
  }

  Future<void> initialize({SendPort? taskRegistrationPort}) async {
    if (_isInitialized) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        'initialize',
        'Already initialized',
      );
      return;
    }

    // Stop all worker tasks before starting
    try {
      await _stopWorker();
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        'initialize',
        'Stopped all worker tasks before initialization',
      );
    } catch (e, stackTrace) {
      RILogger.printError(
        'Error stopping worker tasks during initialization',
        e,
        stackTrace,
      );
    }

    // Delete all old tasks in SharedPreferences
    final clearResult = await SharedPreferencesStore.clearAllTasks();
    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      'initialize',
      'Cleared all tasks from SharedPreferences, result: $clearResult',
    );

    unawaited(_initWorkerManager());

    // Update task registration port if provided
    if (taskRegistrationPort != null) {
      SendMessageConfig.setTaskRegistrationPort(taskRegistrationPort);
    }

    // Use values from SendMessageConfig
    _maxConcurrent = SendMessageConfig.maxConcurrent;
    _heartbeatInterval = SendMessageConfig.heartbeatInterval;
    _enableHealthMonitoring = SendMessageConfig.enableHealthMonitoring;
    _taskRegistrationPort = SendMessageConfig.taskRegistrationPort;

    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      'initialize',
      'Initializing with maxConcurrent: $_maxConcurrent, heartbeatInterval: $_heartbeatInterval, '
          'enableHealthMonitoring: $_enableHealthMonitoring',
    );

    // Task cleanup service has been removed
    // Tasks will expire automatically after GlobalConfig.sendTimeoutDuration

    // Load all persisted tasks
    final tasks = await SharedPreferencesStore.loadAllTasks();
    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      'initialize',
      'Loaded ${tasks.length} tasks from storage',
    );

    // Log detailed information about loaded tasks
    for (final task in tasks) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        'initialize',
        'Loaded task: ${task.id}, name: ${task.name}, status: ${task.status}, createdAt: ${DateTime.fromMillisecondsSinceEpoch(task.createdAt)}',
      );
    }

    // Store tasks for recovery after isolate is initialized
    if (tasks.isNotEmpty) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        'initialize',
        'Storing ${tasks.length} tasks for recovery after isolate initialization',
      );

      // Store tasks for later recovery
      _pendingRecoveryTasks = tasks;

      // Don't add tasks to queue yet - we'll do this after isolate is initialized
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        'initialize',
        'Tasks stored for recovery, will be processed after isolate initialization',
      );
    }

    // Update event listener with task list
    _eventListener.updateFromTaskList(tasks);

    // Load and process any unacknowledged results
    final results = await AckStore.loadAllResults();
    _taskResults.addAll(results);
    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      'initialize',
      'Loaded ${results.length} unacknowledged results',
    );

    // Process any pending tasks
    if (_taskQueue.isNotEmpty || _videoTaskQueue.isNotEmpty) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        'initialize',
        'Found ${_taskQueue.length} regular tasks and ${_videoTaskQueue.length} video tasks, starting processing',
      );
      await _ensureIsolateRunning();

      // Start processing the queues
      if (_taskQueue.isNotEmpty) {
        _scheduleQueueProcessing(immediate: true);
      }

      if (_videoTaskQueue.isNotEmpty) {
        Future.microtask(() => _processVideoTasks());
      }
    }

    // Initialize isolate health monitoring if enabled
    if (_enableHealthMonitoring) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        'initialize',
        'Starting isolate health monitoring with interval: $_heartbeatInterval ms',
      );
      IsolateHealthMonitor().startMonitoring(
        heartbeatInterval: _heartbeatInterval,
        onIsolateUnresponsive: _handleUnresponsiveIsolate,
      );
    } else {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        'initialize',
        'Isolate health monitoring is disabled',
      );
    }

    _isInitialized = true;
    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      'initialize',
      'Initialization complete',
    );
  }

  Future<void> _initWorkerManager() async {
    if (_isWorkerInitialized) return;

    await Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: false,
    );

    _isWorkerInitialized = true;
  }

  /// Handle when isolate is unresponsive
  void _handleUnresponsiveIsolate() {
    final now = DateTime.now().millisecondsSinceEpoch;
    final lastHeartbeat = now - IsolateHealthMonitor().lastHeartbeatTime;

    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      '_handleUnresponsiveIsolate',
      'Worker isolate unresponsive for ${lastHeartbeat}ms, restarting...',
    );

    // Add log to check state before restart
    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      '_handleUnresponsiveIsolate',
      'Before restart: requestPort=${_requestPort != null ? "available" : "null"}, workerIsolate=${_workerIsolate != null ? "active" : "null"}, instanceId=${identityHashCode(this)}',
    );

    // Emit event for isolate unresponsive
    _eventListener.emitEvent(
      IsolateEvent(
        type: IsolateEventType.isolateUnresponsive,
        taskId: 'system',
        data: {'lastHeartbeat': lastHeartbeat},
      ),
    );

    // Kill current isolate
    _workerIsolate?.kill();
    _workerIsolate = null;

    // Close ports
    _responsePort?.close();
    _ackPort?.close();
    _responsePort = null;
    _ackPort = null;
    _requestPort = null;

    // Move running tasks to pending state
    _resetRunningTasks();

    // Restart isolate
    Future.microtask(() async {
      await _ensureIsolateRunning();

      // Add log to check state after restart
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_handleUnresponsiveIsolate',
        'After restart: requestPort=${_requestPort != null ? "available" : "null"}, workerIsolate=${_workerIsolate != null ? "active" : "null"}',
      );

      // Emit event for isolate restarted
      _eventListener.emitEvent(
        IsolateEvent(
          type: IsolateEventType.isolateRestarted,
          taskId: 'system',
          data: {'timestamp': DateTime.now().millisecondsSinceEpoch},
        ),
      );

      // Process queue after restart
      _scheduleQueueProcessing(immediate: true);
    });
  }

  /// Move running tasks to pending or interrupted state
  Future<void> _resetRunningTasks() async {
    final tasks = await SharedPreferencesStore.loadAllTasks();
    int resetCount = 0;
    int interruptedCount = 0;
    List<String> resetTaskIds = [];

    for (final task in tasks) {
      if (task.status == TaskStatus.running) {
        // For uploadFile task, don't convert running to pending to match video flow
        if (task.name == TaskNameEnum.uploadFile.value) {
          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_resetRunningTasks',
            task.id,
            'Skipping running uploadFile task to match video flow',
          );
          continue;
        }

        // All running tasks will be moved to pending to be executed again
        // Users will mark interrupted in the handler of each specific task
        final updatedTask = task.copyWith(status: TaskStatus.pending);
        await SharedPreferencesStore.updateTask(updatedTask);

        // Categorize video tasks and regular tasks
        if (_sequentialTasks.contains(task.name)) {
          _videoTaskQueue.add(updatedTask);
        } else {
          _taskQueue.add(updatedTask);
        }

        resetCount++;
        resetTaskIds.add(task.id);

        RILogger.printTaskDebug(
          'ResilientIsolate',
          '_resetRunningTasks',
          task.id,
          'Converted running task to pending',
        );

        // For message tasks, notify the UI that the task is being retried
        if (task.name.contains('Message') || task.name.contains('message')) {
          // Check if the task has a ref in inputData
          final ref = task.inputData['ref'] as String?;
          if (ref != null) {
            final sendPort = IsolateNameServer.lookupPortByName(ref);
            if (sendPort != null) {
              sendPort.send(
                SendMessageRetryResponse(
                  ref: ref,
                  message:
                      'Message task is being retried due to isolate restart',
                ).toJson(),
              );
            } else {
              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_resetRunningTasks',
                task.id,
                'SendPort not found for ref: $ref',
              );
            }
          }
        }
      }
    }

    // Reset running task count
    _runningTaskCount = 0;

    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      '_resetRunningTasks',
      'Reset $resetCount running tasks to pending, marked $interruptedCount tasks as interrupted',
    );
  }

  Future<bool> registerTask({
    required String taskId,
    required String taskName,
    required Map<String, dynamic> inputData,
    int maxRetries = 100,
    int retryDelay = 30000,
    int timeout = 30000,
    TaskPriority priority = TaskPriority.medium,
    bool isReferenceTask = false,
    bool networkRequired = true,
  }) async {
    // Use GlobalConfig.sendTimeoutDuration for timeout
    timeout = GlobalConfig.sendTimeoutDuration.inMilliseconds;
    if (!_isInitialized) {
      await initialize();
    }

    // If switching between isolate and worker, save task to waiting list
    if (_isSwitchingExecutors) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        'registerTask',
        'Currently switching executors, adding task to pending list: $taskName (ID: $taskId)',
      );

      // Create task model
      final pendingTask = TaskModel(
        id: taskId,
        name: taskName,
        inputData: inputData,
        maxRetries: maxRetries,
        retryDelay: retryDelay,
        timeout: timeout,
        priority: priority,
        isReferenceTask: isReferenceTask,
        networkRequired: networkRequired,
        status: TaskStatus.pending,
      );

      // Add to waiting list
      _pendingSwitchTasks.add(pendingTask);

      // Create completer for this task
      final completer = Completer<bool>();
      _callbackMap[taskId] = completer;

      return completer.future;
    }

    // If using worker, register task with worker
    if (_currentExecutor == ExecutorType.worker) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        'registerTask',
        'Using worker executor, registering task with worker: $taskName (ID: $taskId)',
      );

      final success = await _registerTaskWithWorker(
        taskId: taskId,
        taskName: taskName,
        inputData: inputData,
        priority: priority,
        isReferenceTask: isReferenceTask,
        networkRequired: networkRequired,
      );

      return success;
    }

    final task = TaskModel(
      id: taskId,
      name: taskName,
      inputData: inputData,
      maxRetries: maxRetries,
      retryDelay: retryDelay,
      timeout: timeout,
      priority: priority,
      isReferenceTask: isReferenceTask,
      networkRequired: networkRequired,
    );

    RILogger.printTaskDebug(
      'ResilientIsolate',
      'registerOneOffTask',
      taskId,
      'Creating new task with name: $taskName, priority: $priority',
    );

    // Save the task
    await SharedPreferencesStore.saveTask(task);
    RILogger.printTaskDebug(
      'ResilientIsolate',
      'registerOneOffTask',
      taskId,
      'Task registered and saved to storage',
    );

    // _registerTaskWithWorker(taskId, taskName, inputData);

    // Emit event for task registration
    _eventListener.emitEvent(
      IsolateEvent(
        type: IsolateEventType.taskRegistered,
        taskId: taskId,
        data: {'name': taskName, 'inputData': inputData},
      ),
    );

    // Categorize video tasks and regular tasks
    if (_sequentialTasks.contains(taskName)) {
      // Add to video queue
      _videoTaskQueue.add(task);

      RILogger.printTaskDebug(
        'ResilientIsolate',
        'registerOneOffTask',
        taskId,
        'Added task to video queue: $taskName',
      );

      // Process video tasks using push mechanism
      Future.microtask(() => _processVideoTasks());
    } else {
      // Add to regular queue
      _taskQueue.add(task);

      RILogger.printTaskDebug(
        'ResilientIsolate',
        'registerOneOffTask',
        taskId,
        'Added task to regular queue: $taskName',
      );

      // Process regular queue
      _scheduleQueueProcessing(immediate: true);
    }

    // Create a completer for this task
    final completer = Completer<bool>();
    _callbackMap[taskId] = completer;

    // Emit task registered event
    IsolateEventListener().emitEvent(
      IsolateEvent(
        type: IsolateEventType.taskRegistered,
        taskId: taskId,
        data: {'name': taskName, 'inputData': inputData},
      ),
    );

    return completer.future;
  }

  /// Register task with Workmanager
  Future<bool> _registerTaskWithWorker({
    required String taskId,
    required String taskName,
    required Map<String, dynamic> inputData,
    TaskPriority priority = TaskPriority.medium,
    bool isReferenceTask = false,
    bool networkRequired = true,
  }) async {
    try {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_registerTaskWithWorker',
        'Registering task with worker: $taskName (ID: $taskId)',
      );

      // Ensure Workmanager has been initialized
      if (!_isWorkerInitialized) {
        await _initWorkerManager();
      }

      // Add taskName and taskId fields to inputData for use in callbackDispatcher
      final workerInputData = Map<String, dynamic>.from(inputData);
      workerInputData['taskName'] = taskName;
      workerInputData['taskId'] = taskId;
      workerInputData['priority'] = priority.index;
      workerInputData['isReferenceTask'] = isReferenceTask;
      workerInputData['networkRequired'] = networkRequired;

      // Update creationTime to current time to avoid timeout
      workerInputData['creationTime'] = DateTime.now().toIso8601String();

      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_registerTaskWithWorker',
        'Updated creationTime to current time for task: $taskName (ID: $taskId)',
      );

      // Create task model
      final task = TaskModel(
        id: taskId,
        name: taskName,
        inputData: workerInputData,
        priority: priority,
        isReferenceTask: isReferenceTask,
        networkRequired: networkRequired,
        maxRetries: 3, // Maximum retry attempts
        retryDelay: 5000, // Wait time between retry attempts (ms)
        timeout: GlobalConfig
            .sendTimeoutDuration.inMilliseconds, // Timeout duration (ms)
      );

      // Save task to SharedPreferencesStore
      await SharedPreferencesStore.saveTask(task);

      // Update task in SharedPreferences if it exists
      final existingTask = await SharedPreferencesStore.loadTask(taskId);
      if (existingTask != null) {
        final updatedTask = existingTask.copyWith(
          inputData: workerInputData,
        );
        await SharedPreferencesStore.updateTask(updatedTask);
      }

      await _registerWorkerTask();

      return true;
    } catch (e, stackTrace) {
      RILogger.printError(
        'Error registering task with worker: $taskName (ID: $taskId)',
        e,
        stackTrace,
      );
      return false;
    }
  }

  Future<bool> cancelTask(String taskId) async {
    if (!_isInitialized) {
      await initialize();
    }

    final task = await SharedPreferencesStore.loadTask(taskId);
    if (task == null) {
      RILogger.printTaskError(
        taskId,
        'Cannot cancel - task not found in storage',
      );
      return false;
    }

    if (task.status == TaskStatus.completed ||
        task.status == TaskStatus.failed ||
        task.status == TaskStatus.canceled) {
      RILogger.printTaskDebug(
        'ResilientIsolate',
        'cancelTask',
        taskId,
        'Cannot cancel - task already in final state: ${task.status}',
      );
      return false;
    }

    // Special handling for sequential tasks when canceled
    if (_sequentialTasks.contains(task.name)) {
      // Check if there is a messageRef in inputData
      final messageRef = task.inputData['messageRef'] as String? ??
          task.inputData['ref'] as String?;

      if (messageRef != null) {
        // Release the lock for sequential task
        _releaseSequentialTaskLock(messageRef);

        // Keep old handling for _runningSequentialTasksByMessageRef to ensure backward compatibility
        if (_runningSequentialTasksByMessageRef.containsKey(messageRef)) {
          final taskName =
              _runningSequentialTasksByMessageRef.remove(messageRef);
          RILogger.printTaskDebug(
            'ResilientIsolate',
            'cancelTask',
            taskId,
            'Removed canceled sequential task $taskName with messageRef $messageRef from running list',
          );
        }

        RILogger.printTaskDebug(
          'ResilientIsolate',
          'cancelTask',
          taskId,
          'Released sequential task lock for canceled task ${task.name} with messageRef $messageRef',
        );
      }
    }

    // Update task status
    final updatedTask = task.copyWith(status: TaskStatus.canceled);
    await SharedPreferencesStore.updateTask(updatedTask);

    // Emit event for task cancellation
    _eventListener.emitEvent(
      IsolateEvent(type: IsolateEventType.taskCanceled, taskId: taskId),
    );

    // Remove from queue if not yet dispatched
    _taskQueue.remove(taskId);

    // Complete the future if it exists
    if (_callbackMap.containsKey(taskId)) {
      _callbackMap[taskId]!.complete(false);
      _callbackMap.remove(taskId);
    }

    RILogger.printTaskDebug(
      'ResilientIsolate',
      'cancelTask',
      taskId,
      'Task successfully canceled',
    );
    return true;
  }

  // Lock to ensure only one queue processing at a time
  final _processQueueLock = Lock();
  int _processQueueRetryCount = 0;
  static const int _maxProcessQueueRetries = 5;

  /// New method to schedule queue processing with debounce mechanism
  Future<void> _scheduleQueueProcessing({bool immediate = false}) async {
    final now = DateTime.now().millisecondsSinceEpoch;
    final timeSinceLastProcess = now - _lastProcessQueueTime;

    // If immediate processing is requested or debounce time has elapsed
    if (immediate ||
        timeSinceLastProcess >= SendMessageConfig.processQueueDebounceTime) {
      _lastProcessQueueTime = now;
      Future.microtask(() => _processQueue());
    } else {
      // Schedule processing after the remaining debounce time
      final delayTime =
          SendMessageConfig.processQueueDebounceTime - timeSinceLastProcess;
      Future.delayed(Duration(milliseconds: delayTime), () {
        _lastProcessQueueTime = DateTime.now().millisecondsSinceEpoch;
        _processQueue();
      });
    }
  }

  Future<void> _processQueue() async {
    // Use lock to ensure only one queue processing at a time
    return _processQueueLock.synchronized(() async {
      if (_isProcessingQueue) {
        return; // Already processing queue
      }

      _isProcessingQueue = true;

      try {
        // Add log to check state of _requestPort
        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_processQueue',
          'Processing queue: requestPort=${_requestPort != null ? "available" : "null"}, queueSize=${_taskQueue.length}, runningCount=$_runningTaskCount, instanceId=${identityHashCode(this)}',
        );

        // Log detailed queue stats
        final videoTasksInQueue = _taskQueue
            .countTasksByName(TaskNameEnum.compressAndUploadVideoMessage.value);
        final runningVideoTasks = _runningTasksByName[
                TaskNameEnum.compressAndUploadVideoMessage.value] ??
            0;

        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_processQueue',
          'Queue stats: Total=${_taskQueue.length}, Running=$_runningTaskCount, Max=$_maxConcurrent, VideoTasks in queue=$videoTasksInQueue, Running video tasks=$runningVideoTasks',
        );

        // Log running tasks by name
        if (_runningTasksByName.isNotEmpty) {
          final runningTasksInfo = _runningTasksByName.entries
              .map((e) => '${e.key}:${e.value}')
              .join(', ');
          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_processQueue',
            'Running tasks by type: $runningTasksInfo',
          );
        }

        if (_taskQueue.isEmpty || _runningTaskCount >= _maxConcurrent) {
          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_processQueue',
            'Skipping: queueEmpty=${_taskQueue.isEmpty}, runningFull=${_runningTaskCount >= _maxConcurrent}',
          );
          return;
        }

        // Check if worker isolate is ready
        if (_requestPort == null) {
          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_processQueue',
            'Request port is null, worker isolate not ready. Ensuring isolate is running before processing queue.',
          );

          // Add log to check state of isolate
          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_processQueue',
            'Isolate state: workerIsolate=${_workerIsolate != null ? "active" : "null"}, isInitialized=$_isInitialized, isProcessingQueue=$_isProcessingQueue',
          );

          try {
            await _ensureIsolateRunning();

            // Add log after ensuring isolate is running
            RILogger.printClassMethodDebug(
              'ResilientIsolate',
              '_processQueue',
              'After _ensureIsolateRunning: requestPort=${_requestPort != null ? "available" : "null"}, workerIsolate=${_workerIsolate != null ? "active" : "null"}',
            );

            // If still not ready after ensuring, schedule retry with exponential backoff
            if (_requestPort == null) {
              _processQueueRetryCount++;
              final delaySeconds = math
                  .min(2 * math.pow(2, _processQueueRetryCount - 1), 30)
                  .toInt();

              RILogger.printClassMethodDebug(
                'ResilientIsolate',
                '_processQueue',
                'Worker isolate still not ready after _ensureIsolateRunning(). ' +
                    'Retry ${_processQueueRetryCount}/${_maxProcessQueueRetries} scheduled in $delaySeconds seconds.',
              );

              if (_processQueueRetryCount <= _maxProcessQueueRetries) {
                if (_taskQueue.isNotEmpty) {
                  Future.delayed(
                    Duration(seconds: delaySeconds),
                    () => _scheduleQueueProcessing(),
                  );
                }
              } else {
                RILogger.printClassMethodDebug(
                  'ResilientIsolate',
                  '_processQueue',
                  'Maximum retry attempts reached (${_maxProcessQueueRetries}), resetting retry count',
                );
                _processQueueRetryCount = 0;
              }
              return;
            }

            // Reset retry count on success
            _processQueueRetryCount = 0;

            // Check if there are any tasks waiting for isolate to be ready
            if (_pendingIsolateReadyTasks.isNotEmpty) {
              RILogger.printClassMethodDebug(
                'ResilientIsolate',
                '_processQueue',
                'Isolate is now ready, processing ${_pendingIsolateReadyTasks.length} pending tasks',
              );

              // Add waiting tasks to main queue
              for (final pendingTask in _pendingIsolateReadyTasks) {
                _taskQueue.add(pendingTask);
              }
              _pendingIsolateReadyTasks.clear();
            }
          } catch (e) {
            _processQueueRetryCount++;
            final delaySeconds = math
                .min(5 * math.pow(2, _processQueueRetryCount - 1), 60)
                .toInt();

            RILogger.printClassMethodDebug(
              'ResilientIsolate',
              '_processQueue',
              'Error ensuring isolate is running: $e. ' +
                  'Retry ${_processQueueRetryCount}/${_maxProcessQueueRetries} scheduled in $delaySeconds seconds.',
            );

            if (_processQueueRetryCount <= _maxProcessQueueRetries) {
              if (_taskQueue.isNotEmpty) {
                Future.delayed(Duration(seconds: delaySeconds), _processQueue);
              }
            } else {
              RILogger.printClassMethodDebug(
                'ResilientIsolate',
                '_processQueue',
                'Maximum retry attempts reached (${_maxProcessQueueRetries}), resetting retry count',
              );
              _processQueueRetryCount = 0;
            }
            return;
          }
        }

        // Reset retry count on success
        _processQueueRetryCount = 0;

        try {
          // Check and process hung tasks before processing queue
          await _checkAndHandleStuckTasks();

          // Double-check that isolate is still running
          await _ensureIsolateRunning();

          // No global sequential task check anymore - we'll check per messageRef

          // Limit the number of tasks processed at once to avoid overload
          int processedTaskCount = 0;
          final maxTasksPerBatch = 5; // Process maximum 5 tasks per batch

          // Check if any tasks can be processed ("pull" model)
          bool canProcessAnyTask = false;
          final taskSnapshot = _taskQueue.getAll();

          for (final task in taskSnapshot) {
            // Check limit on number of tasks of the same type
            final runningTasksOfSameType = _runningTasksByName[task.name] ?? 0;
            final maxSameTypeTask = 3; // Maximum 3 tasks of the same type

            if (runningTasksOfSameType < maxSameTypeTask) {
              // Check sequential task
              bool isSequentialTask = _sequentialTasks.contains(task.name);
              bool canProcessSequential = true;

              if (isSequentialTask) {
                String? messageRef = task.inputData['ref'] as String?;
                if (messageRef != null &&
                    _runningSequentialTasksByMessageRef
                        .containsKey(messageRef)) {
                  canProcessSequential = false;
                }
              }

              // If this task can be processed
              if (canProcessSequential) {
                canProcessAnyTask = true;
                break;
              }
            }
          }

          // If no tasks can be processed, don't schedule immediate reprocessing
          if (!canProcessAnyTask) {
            RILogger.printClassMethodDebug(
              'ResilientIsolate',
              '_processQueue',
              'Cannot process any task due to constraints (same type limits, sequential tasks, or network), waiting for capacity',
            );
            return; // Don't schedule reprocessing
          }

          while (_taskQueue.isNotEmpty &&
              _runningTaskCount < _maxConcurrent &&
              processedTaskCount < maxTasksPerBatch) {
            // Look at next task without removing it from queue
            final nextTask = _taskQueue.peekFirst();
            if (nextTask == null) break;

            // Check constraints before processing task
            final currentTask =
                await SharedPreferencesStore.loadTask(nextTask.id);
            if (currentTask == null) {
              // Task doesn't exist in storage, remove from queue
              _taskQueue.removeFirst();
              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_processQueue',
                nextTask.id,
                'Task not found in storage, removing from queue',
              );
              continue;
            } else if (currentTask.status == TaskStatus.canceled) {
              // Task has been canceled, remove from queue
              _taskQueue.removeFirst();
              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_processQueue',
                currentTask.id,
                'Task was canceled, removing from queue',
              );
              continue;
            }

            // Skip video tasks as they are processed separately in _processVideoTasks
            if (_sequentialTasks.contains(currentTask.name)) {
              // Don't process this task in _processQueue
              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_processQueue',
                currentTask.id,
                'Task ${currentTask.name} is a video task, should be handled by _processVideoTasks. Removing from regular queue.',
              );
              _taskQueue.removeFirst();
              continue; // Continue with next task
            }

            // Check if there are too many tasks of the same type running
            final maxSameTypeTask = 3; // Maximum 3 tasks of the same type
            if (currentTask.name !=
                    TaskNameEnum.compressAndUploadVideoMessage
                        .value && // Already specially handled above
                _runningTasksByName.containsKey(currentTask.name) &&
                (_runningTasksByName[currentTask.name] ?? 0) >=
                    maxSameTypeTask) {
              // Don't process this task in current call
              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_processQueue',
                currentTask.id,
                'Too many tasks of type ${currentTask.name} running (${_runningTasksByName[currentTask.name]} >= $maxSameTypeTask), skipping for now',
              );
              break; // Stop loop, wait for capacity
            }

            // Remove task from queue after checking constraints
            _taskQueue.removeFirst();

            RILogger.printTaskDebug(
              'ResilientIsolate',
              '_processQueue',
              currentTask.id,
              'Removed task from queue: name=${currentTask.name}, status=${currentTask.status}, priority=${currentTask.priority}',
            );

            // Check if the task is a sequential task
            bool isSequentialTask = _sequentialTasks.contains(currentTask.name);

            if (isSequentialTask) {
              // Get messageRef from inputData
              final String? messageRef =
                  currentTask.inputData['messageRef'] as String? ??
                      currentTask.inputData['ref'] as String?;

              if (messageRef != null) {
                // Clean up expired locks before checking
                _cleanupExpiredTaskLocks();

                // Check if any task is running with the same messageRef
                final existingLock = _getSequentialTaskLock(messageRef);

                // Keep old handling for _runningSequentialTasksByMessageRef to ensure backward compatibility
                if (_runningSequentialTasksByMessageRef
                        .containsKey(messageRef) ||
                    (existingLock != null && !existingLock.isReleased)) {
                  RILogger.printTaskDebug(
                    'ResilientIsolate',
                    '_processQueue',
                    currentTask.id,
                    'Sequential task ${currentTask.name} with messageRef $messageRef must wait for other tasks to complete, adding back to queue',
                  );
                  _taskQueue.add(currentTask);

                  // Stop processing queue when encountering a sequential task that must wait
                  break;
                }

                // Create new lock for sequential task
                _createSequentialTaskLock(
                  messageRef: messageRef,
                  taskName: currentTask.name,
                  taskId: currentTask.id,
                );

                // Keep old handling for _runningSequentialTasksByMessageRef to ensure backward compatibility
                _runningSequentialTasksByMessageRef[messageRef] =
                    currentTask.name;

                RILogger.printTaskDebug(
                  'ResilientIsolate',
                  '_processQueue',
                  currentTask.id,
                  'Created sequential task lock for ${currentTask.name} with messageRef $messageRef',
                );
              }
            }

            // Update task status to running
            final runningTask =
                currentTask.copyWith(status: TaskStatus.running);

            RILogger.printTaskDebug(
              'ResilientIsolate',
              '_processQueue',
              currentTask.id,
              'Updating task status to running',
            );

            final updateSuccess =
                await SharedPreferencesStore.updateTask(runningTask);

            // Check if task was updated successfully
            final updatedTaskInStorage =
                await SharedPreferencesStore.loadTask(runningTask.id);
            if (updatedTaskInStorage == null) {
              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_processQueue',
                runningTask.id,
                'Task not found in storage after updating status to running. This will cause issues later.',
              );
            } else {
              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_processQueue',
                runningTask.id,
                'Task updated to running status: ${updatedTaskInStorage.status}, update result: $updateSuccess',
              );
            }

            // Send task to worker isolate
            _sendTaskToWorker(runningTask);

            _runningTaskCount++;
            processedTaskCount++;

            // Increment running task count for this task name
            _runningTasksByName[runningTask.name] =
                (_runningTasksByName[runningTask.name] ?? 0) + 1;

            // Record start time for this task
            _runningTasksStartTime[runningTask.id] =
                DateTime.now().millisecondsSinceEpoch;

            RILogger.printTaskDebug(
              'ResilientIsolate',
              '_processQueue',
              runningTask.id,
              'Task dispatched, running count: $_runningTaskCount, ${runningTask.name} running: ${_runningTasksByName[runningTask.name]}',
            );
          }

          // Only schedule reprocessing if there are tasks in queue and capacity to process more
          if (_taskQueue.isNotEmpty &&
              _runningTaskCount < _maxConcurrent &&
              canProcessAnyTask) {
            // Schedule next processing after a short delay
            _scheduleQueueProcessing();
          }
        } catch (e, stackTrace) {
          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_processQueue',
            'Error processing queue: $e\n$stackTrace',
          );

          // Schedule retry after a delay if there are waiting tasks
          if (_taskQueue.isNotEmpty) {
            Future.delayed(
              Duration(seconds: 2),
              () => _scheduleQueueProcessing(),
            );
          }
        }
      } finally {
        _isProcessingQueue = false;
      }
    });
  }

  Future<void> _ensureIsolateRunning() async {
    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      '_ensureIsolateRunning',
      'Ensuring worker isolate is running, current isolate: ${_workerIsolate != null ? "active" : "null"}',
    );

    if (_workerIsolate != null) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_ensureIsolateRunning',
        'Worker isolate already running, skipping initialization',
      );
      return;
    }

    // Use lock to ensure only one isolate initialization process at a time
    if (_isInitializingIsolate) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_ensureIsolateRunning',
        'Another initialization process is already in progress, waiting...',
      );

      // Wait a short time and check again
      await Future.delayed(Duration(milliseconds: 500));

      // If isolate has been successfully initialized, return
      if (_workerIsolate != null) {
        return;
      }

      // If still initializing, wait a bit longer
      if (_isInitializingIsolate) {
        await Future.delayed(Duration(seconds: 2));

        // Check one more time
        if (_workerIsolate != null) {
          return;
        }

        // If still initializing after wait time, there might be a problem
        // Reset state to try again
        if (_isInitializingIsolate) {
          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_ensureIsolateRunning',
            'Initialization process seems stuck, resetting state to try again',
          );
          _isInitializingIsolate = false;
        }
      }
    }

    // Mark as initializing
    await _isolateInitLock.synchronized(() async {
      if (_isInitializingIsolate) return;

      _isInitializingIsolate = true;
      try {
        await _initializeIsolate();
      } finally {
        // Ensure state is always reset even if there's an error
        // Note: _initializeIsolate() already resets _isInitializingIsolate when completed
        // This is just an additional safeguard
        if (_isInitializingIsolate) {
          _isInitializingIsolate = false;
        }
      }
    });
  }

  /// Method that actually initializes the isolate, called from _ensureIsolateRunning
  Future<void> _initializeIsolate() async {
    try {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_initializeIsolate',
        'Creating ports for communication (attempt ${_isolateInitRetryCount + 1}/${SendMessageConfig.maxIsolateInitRetries})',
      );

      // Add log to check state before initialization
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_initializeIsolate',
        'Before initialization: requestPort=${_requestPort != null ? "available" : "null"}, workerIsolate=${_workerIsolate != null ? "active" : "null"}, instanceId=${identityHashCode(this)}',
      );

      // Ensure old ports are closed
      _responsePort?.close();
      _ackPort?.close();

      // Create ports for communication
      _responsePort = ReceivePort();
      _ackPort = ReceivePort();

      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_initializeIsolate',
        'Spawning worker isolate',
      );

      // Start the worker isolate
      _workerIsolate = await FlutterIsolate.spawn(workerIsolateEntryPoint, [
        _responsePort!.sendPort,
        _ackPort!.sendPort,
        _taskRegistrationPort, // Pass task registration port to worker isolate
      ]);

      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_initializeIsolate',
        'Worker isolate spawned, waiting for connection',
      );

      // Listen for the request port from the worker
      final requestPortCompleter = Completer<SendPort>();
      StreamSubscription<dynamic>? responseSubscription;

      responseSubscription = _responsePort!.listen(
        (message) {
          if (message is SendPort) {
            // This is the initial port exchange
            _requestPort = message;
            if (!requestPortCompleter.isCompleted) {
              requestPortCompleter.complete(message);
            }
            RILogger.printClassMethodDebug(
              'ResilientIsolate',
              '_initializeIsolate',
              'Worker isolate connected, received requestPort: ${_requestPort != null ? "available" : "null"}',
            );
          } else if (message is Map<String, dynamic>) {
            // Update heartbeat whenever we receive any message from worker (if health monitoring is enabled)
            if (_enableHealthMonitoring) {
              IsolateHealthMonitor().updateHeartbeat();

              // Check if this is a heartbeat message
              if (message.containsKey('heartbeat')) {
                // No need to log every heartbeat to avoid excessive logging
                return;
              }
            }

            // This is a task result
            _handleTaskResult(message);
          }
        },
        onError: (error) {
          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_initializeIsolate',
            'Error in response port listener: $error',
          );
          if (!requestPortCompleter.isCompleted) {
            requestPortCompleter.completeError(error);
          }
        },
        onDone: () {
          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_initializeIsolate',
            'Response port stream closed',
          );
        },
      );

      // Listen for acknowledgments
      StreamSubscription<dynamic>? ackSubscription;
      ackSubscription = _ackPort!.listen(
        (message) {
          // Update heartbeat whenever we receive any message from worker (if health monitoring is enabled)
          if (_enableHealthMonitoring) {
            IsolateHealthMonitor().updateHeartbeat();
          }
          if (message is String) {
            if (message.startsWith('persist:')) {
              // This is a request to persist an unacknowledged result
              final parts = message.split(':');
              if (parts.length == 3) {
                final taskId = parts[1];
                final success = parts[2] == 'true';
                AckStore.saveResult(taskId, success);
                RILogger.printClassMethodDebug(
                  'ResilientIsolate',
                  '_initializeIsolate',
                  'Persisted unacknowledged result: $taskId, success: $success',
                );
              }
            } else {
              // This is an acknowledgment of a result
              _handleAcknowledgment(message);
            }
          }
        },
        onError: (error) {
          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_initializeIsolate',
            'Error in ack port listener: $error',
          );
        },
        onDone: () {
          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_initializeIsolate',
            'Ack port stream closed',
          );
        },
      );

      // Wait for the request port with timeout
      try {
        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_initializeIsolate',
          'Waiting for request port from worker isolate (timeout: ${SendMessageConfig.requestPortTimeoutSeconds}s)',
        );

        await requestPortCompleter.future.timeout(
          Duration(seconds: SendMessageConfig.requestPortTimeoutSeconds),
          onTimeout: () {
            RILogger.printClassMethodDebug(
              'ResilientIsolate',
              '_initializeIsolate',
              'Timeout waiting for request port from worker isolate after ${SendMessageConfig.requestPortTimeoutSeconds}s',
            );
            throw TimeoutException('Timeout waiting for request port');
          },
        );

        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_initializeIsolate',
          'Received request port from worker isolate, sending initialization data',
        );

        // Send initialization info to worker isolate
        await _sendInitializationData();

        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_initializeIsolate',
          'Worker isolate started and initialized',
        );

        // Reset retry count on successful initialization
        _isolateInitRetryCount = 0;

        // Mark initialization process as completed
        _isInitializingIsolate = false;

        // Mark isolate as initialized
        _isolateInitialized = true;

        // Wait a short time for worker isolate to stabilize before recovering tasks
        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_initializeIsolate',
          'Prioritizing initialization: delaying task recovery to ensure isolate stability',
        );

        // Check if WebSocket resume is complete, proceed with task recovery
        if (_webSocketResumeCompleted) {
          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_initializeIsolate',
            'WebSocket already resumed, recovering pending tasks',
          );

          // Schedule task recovery after isolate has stabilized
          Future.delayed(Duration(milliseconds: 500), () {
            _recoverPendingTasks();

            // Schedule increasing heartbeatInterval to 60s after recovering tasks
            Future.delayed(Duration(seconds: 10), () {
              if (_isInitialized && _enableHealthMonitoring) {
                RILogger.printClassMethodDebug(
                  'ResilientIsolate',
                  '_initializeIsolate',
                  'Updating heartbeat interval after successful initialization',
                );
                updateHeartbeatInterval();
              }
            });
          });
        } else {
          RILogger.printClassMethodDebug(
            'ResilientIsolate',
            '_initializeIsolate',
            'WebSocket not yet resumed, waiting for resume before recovering tasks',
          );
          // Don't call _recoverPendingTasks() immediately
          // Will be called when WebSocketResumeCompletedEvent is received
        }
      } catch (timeoutError) {
        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_initializeIsolate',
          'Error waiting for request port: $timeoutError',
        );

        // Unregister listeners to avoid memory leaks
        await responseSubscription.cancel();
        await ackSubscription.cancel();

        throw timeoutError; // Re-throw to be caught by outer catch block
      }
    } catch (e, stackTrace) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_initializeIsolate',
        'Error starting worker isolate: $e\n$stackTrace',
      );

      // Kill isolate if it was created but failed to initialize
      if (_workerIsolate != null) {
        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_initializeIsolate',
          'Killing partially initialized worker isolate',
        );
        _workerIsolate!.kill();
      }

      _workerIsolate = null;
      _responsePort?.close();
      _ackPort?.close();
      _responsePort = null;
      _ackPort = null;
      _requestPort = null;

      // Increase retry count
      _isolateInitRetryCount++;

      // Mark initialization process as completed
      _isInitializingIsolate = false;

      // If not exceeded maximum retry attempts, try again after a delay
      if (_isolateInitRetryCount < SendMessageConfig.maxIsolateInitRetries) {
        // Use exponential backoff to increase wait time between retries
        final delaySeconds =
            math.min(5 * math.pow(2, _isolateInitRetryCount - 1), 30).toInt();

        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_initializeIsolate',
          'Scheduling retry ${_isolateInitRetryCount}/${SendMessageConfig.maxIsolateInitRetries} in $delaySeconds seconds',
        );

        Future.delayed(Duration(seconds: delaySeconds), _ensureIsolateRunning);
      } else {
        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_initializeIsolate',
          'Maximum retry attempts reached (${SendMessageConfig.maxIsolateInitRetries}), giving up',
        );

        // Reset retry count to allow future retries
        _isolateInitRetryCount = 0;
      }
    }
  }

  void _sendTaskToWorker(TaskModel task) {
    // Add log to check state of _requestPort and task
    RILogger.printTaskDebug(
      'ResilientIsolate',
      '_sendTaskToWorker',
      task.id,
      'Sending task to worker: requestPort=${_requestPort != null ? "available" : "null"}, taskName=${task.name}, taskStatus=${task.status}, instanceId=${identityHashCode(this)}',
    );

    if (_requestPort == null) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_sendTaskToWorker',
        'Request port not available, worker isolate not ready. Task ${task.id} will not be sent.',
      );

      // Add log to check state of isolate
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_sendTaskToWorker',
        'Isolate state: workerIsolate=${_workerIsolate != null ? "active" : "null"}, isInitialized=$_isInitialized, isProcessingQueue=$_isProcessingQueue',
      );

      // Move task back to pending state
      Future.microtask(() async {
        RILogger.printTaskDebug(
          'ResilientIsolate',
          '_sendTaskToWorker',
          task.id,
          'Requeueing task for later processing',
        );

        // Check if task exists in storage before updating
        final existingTask = await SharedPreferencesStore.loadTask(task.id);
        if (existingTask == null) {
          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_sendTaskToWorker',
            task.id,
            'Task not found in storage before updating status to pending. This may cause issues later.',
          );
        } else {
          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_sendTaskToWorker',
            task.id,
            'Task found in storage before updating: status=${existingTask.status}, name=${existingTask.name}',
          );
        }

        final updatedTask = task.copyWith(status: TaskStatus.pending);
        final updateSuccess =
            await SharedPreferencesStore.updateTask(updatedTask);

        RILogger.printTaskDebug(
          'ResilientIsolate',
          '_sendTaskToWorker',
          task.id,
          'Updated task status to pending, result: $updateSuccess',
        );

        // Check again if task exists in storage after updating
        final updatedTaskInStorage =
            await SharedPreferencesStore.loadTask(task.id);
        if (updatedTaskInStorage == null) {
          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_sendTaskToWorker',
            task.id,
            'Task not found in storage after updating status to pending. This will cause issues later.',
          );
        } else {
          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_sendTaskToWorker',
            task.id,
            'Task found in storage after updating: status=${updatedTaskInStorage.status}, name=${updatedTaskInStorage.name}',
          );
        }

        // ROOT CAUSE: Don't add directly to _taskQueue when _requestPort is null
        // Instead, save task to a temporary list
        // and only process when _requestPort is ready

        // The _pendingIsolateReadyTasks list has been declared

        // Add to temporary list instead of _taskQueue
        if (!_pendingIsolateReadyTasks.any((t) => t.id == updatedTask.id)) {
          _pendingIsolateReadyTasks.add(updatedTask);

          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_sendTaskToWorker',
            task.id,
            'Added task to pending list until isolate is ready. Pending count: ${_pendingIsolateReadyTasks.length}',
          );
        }

        // Emit event for task error
        _eventListener.emitEvent(
          IsolateEvent(
            type: IsolateEventType.taskError,
            taskId: task.id,
            data: {
              'error': 'Request port not available',
              'name': task.name,
            },
          ),
        );

        // For message tasks, notify the UI that there was an error
        if (task.name.contains('Message') || task.name.contains('message')) {
          // Check if the task has a ref in inputData
          final ref = task.inputData['ref'] as String?;
          if (ref != null) {
            final sendPort = IsolateNameServer.lookupPortByName(ref);
            if (sendPort != null) {
              sendPort.send(
                SendMessageFailureResponse(
                  ref: ref,
                  errorMessage:
                      'Request port not available, task will be retried',
                ).toJson(),
              );
            } else {
              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_sendTaskToWorker',
                task.id,
                'SendPort not found for ref: $ref',
              );
            }
          }
        }

        // Try to restart isolate and schedule queue processing
        try {
          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_sendTaskToWorker',
            task.id,
            'Ensuring isolate is running before retrying queue processing',
          );

          await _ensureIsolateRunning();

          // Schedule queue processing after a delay
          if (_taskQueue.isNotEmpty) {
            Future.delayed(Duration(seconds: 2), _processQueue);
          }
        } catch (e) {
          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_sendTaskToWorker',
            task.id,
            'Error ensuring isolate is running: $e',
          );

          // Still schedule queue processing after a longer delay
          if (_taskQueue.isNotEmpty) {
            Future.delayed(Duration(seconds: 5), _processQueue);
          }
        }
      });
      return;
    }

    // Create timeout timer for task
    _createTaskTimeoutTimer(task);

    final message = {
      'taskId': task.id,
      'name': task.name,
      'inputData': task.inputData,
      'timeout': task.timeout,
    };

    _requestPort!.send(message);
    RILogger.printTaskDebug(
      'ResilientIsolate',
      '_sendTaskToWorker',
      task.id,
      'Task sent to worker',
    );

    // Emit event for task started
    _eventListener.emitEvent(
      IsolateEvent(
        type: IsolateEventType.taskStarted,
        taskId: task.id,
        data: {'name': task.name, 'inputData': task.inputData},
      ),
    );
  }

  // Lock to ensure only one task result processing at a time
  final _handleTaskResultLock = Lock();

  void _handleTaskResult(Map<String, dynamic> result) async {
    final taskId = result['taskId'] as String;
    final success = result['success'] as bool;
    final now = DateTime.now().millisecondsSinceEpoch;
    final taskName = result['name'] as String?;
    final inputData = result['inputData'] as Map<String, dynamic>?;
    final messageRef =
        inputData?['messageRef'] as String? ?? inputData?['ref'] as String?;
    final isNetworkError = result['isNetworkError'] as bool? ?? false;
    final isDuplicate = result['isDuplicate'] as bool? ?? false;
    final errorData = result['error'];
    final message = result['message'] as String?;

    // Add log to check state of _requestPort when receiving results
    RILogger.printTaskDebug(
      'ResilientIsolate',
      '_handleTaskResult',
      taskId,
      'Handling task result: requestPort=${_requestPort != null ? "available" : "null"}, success=$success, instanceId=${identityHashCode(this)}, taskName=$taskName, messageRef=$messageRef, isNetworkError=$isNetworkError, isDuplicate=$isDuplicate',
    );

    // Special handling for duplicate tasks
    if (isDuplicate) {
      RILogger.printTaskDebug(
        'ResilientIsolate',
        '_handleTaskResult',
        taskId,
        'Received duplicate task notification: $message',
      );

      // Send acknowledgment so worker isolate knows the result has been processed
      _sendAcknowledgment(taskId);

      // Emit event for task already in progress
      _eventListener.emitEvent(
        IsolateEvent(
          type: IsolateEventType.taskAlreadyInProgress,
          taskId: taskId,
          data: {
            'message': message ?? 'Task is already being processed',
            'messageRef': messageRef,
          },
        ),
      );

      return;
    }

    // Log details about network error if any
    if (isNetworkError) {
      RILogger.printTaskDebug(
        'ResilientIsolate',
        '_handleTaskResult',
        taskId,
        'NETWORK DEBUG: Network error detected in task result: $errorData',
      );
    }

    // Log entire result content for debugging
    RILogger.printTaskDebug(
      'ResilientIsolate',
      '_handleTaskResult',
      taskId,
      'Full result content: ${result.toString()}',
    );

    // Use lock to ensure only one task result processing at a time
    _handleTaskResultLock.synchronized(() async {
      try {
        RILogger.printTaskDebug(
          'ResilientIsolate',
          '_handleTaskResult',
          taskId,
          'Received result, success: $success',
        );

        // Cancel timeout timer for task
        _cancelTaskTimeoutTimer(taskId);

        // Store the result
        _taskResults[taskId] = success;

        // Add log before loading task for checking
        RILogger.printTaskDebug(
          'ResilientIsolate',
          '_handleTaskResult',
          taskId,
          'Before loading task from storage, runningCount=$_runningTaskCount, runningTasksByName=${_runningTasksByName.toString()}',
        );

        // Check if task exists in storage before loading
        final taskExists = await SharedPreferencesStore.taskExists(taskId);
        RILogger.printTaskDebug(
          'ResilientIsolate',
          '_handleTaskResult',
          taskId,
          'Task exists in storage before loading: $taskExists',
        );

        // Add log before loading task
        RILogger.printTaskDebug(
          'ResilientIsolate',
          '_handleTaskResult',
          taskId,
          'Attempting to load task from storage with ID: $taskId, name: ${result["name"]}',
        );

        // Load the task
        final task = await SharedPreferencesStore.loadTask(taskId);

        if (task == null) {
          // Log more details about task not found
          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_handleTaskResult',
            taskId,
            'Task not found in storage. Assuming task is already completed. TaskName: ${result["name"]}, MessageRef: $messageRef',
          );

          // Check if there are other tasks with the same messageRef
          if (messageRef != null) {
            final allTasks = await SharedPreferencesStore.loadAllTasks();
            final tasksWithSameMessageRef = allTasks.where((t) {
              final taskMessageRef = t.inputData['messageRef'] as String?;
              final taskRef = t.inputData['ref'] as String?;
              return (taskMessageRef == messageRef) || (taskRef == messageRef);
            }).toList();

            if (tasksWithSameMessageRef.isNotEmpty) {
              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_handleTaskResult',
                taskId,
                'Found ${tasksWithSameMessageRef.length} other tasks with the same messageRef: $messageRef',
              );

              for (final t in tasksWithSameMessageRef) {
                RILogger.printTaskDebug(
                  'ResilientIsolate',
                  '_handleTaskResult',
                  taskId,
                  'Related task: ID=${t.id}, name=${t.name}, status=${t.status}, createdAt=${DateTime.fromMillisecondsSinceEpoch(t.createdAt)}',
                );
              }
            } else {
              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_handleTaskResult',
                taskId,
                'No other tasks found with messageRef: $messageRef',
              );
            }
          }

          // Check if there are other tasks with the same name
          if (result["name"] != null) {
            final taskNameFromResult = result["name"] as String;
            final allTasks = await SharedPreferencesStore.loadAllTasks();
            final tasksWithSameName =
                allTasks.where((t) => t.name == taskNameFromResult).toList();

            if (tasksWithSameName.isNotEmpty) {
              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_handleTaskResult',
                taskId,
                'Found ${tasksWithSameName.length} other tasks with the same name: $taskNameFromResult',
              );

              for (final t in tasksWithSameName) {
                RILogger.printTaskDebug(
                  'ResilientIsolate',
                  '_handleTaskResult',
                  taskId,
                  'Similar task: ID=${t.id}, status=${t.status}, createdAt=${DateTime.fromMillisecondsSinceEpoch(t.createdAt)}',
                );
              }
            } else {
              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_handleTaskResult',
                taskId,
                'No other tasks found with name: $taskNameFromResult',
              );
            }
          }

          // Decrease running task count
          _runningTaskCount = math.max(0, _runningTaskCount - 1);

          // Find task name from result
          final taskName = result['name'] as String?;
          if (taskName != null && _runningTasksByName.containsKey(taskName)) {
            _runningTasksByName[taskName] =
                math.max(0, (_runningTasksByName[taskName] ?? 0) - 1);

            RILogger.printTaskDebug(
              'ResilientIsolate',
              '_handleTaskResult',
              taskId,
              'Decremented running count for completed task: $_runningTaskCount, $taskName running: ${_runningTasksByName[taskName]}',
            );
          } else {
            // If task name not found, decrease running count for all task types with count > 0
            for (final entry in _runningTasksByName.entries) {
              if (entry.value > 0) {
                _runningTasksByName[entry.key] = entry.value - 1;

                RILogger.printTaskDebug(
                  'ResilientIsolate',
                  '_handleTaskResult',
                  taskId,
                  'Task name not found, decremented running count for ${entry.key}: $_runningTaskCount, ${entry.key} running: ${_runningTasksByName[entry.key]}',
                );
                break;
              }
            }
          }

          // Special handling for compressAndUploadVideoMessage task
          if (taskName == TaskNameEnum.compressAndUploadVideoMessage.value) {
            // Check if there is a messageRef in inputData
            final messageRef = result['inputData']?['messageRef'] as String? ??
                result['inputData']?['ref'] as String?;

            if (messageRef != null) {
              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_handleTaskResult',
                taskId,
                'Found messageRef $messageRef in task result data',
              );

              // Remove messageRef from _runningSequentialTasksByMessageRef list
              if (_runningSequentialTasksByMessageRef.containsKey(messageRef)) {
                final taskName =
                    _runningSequentialTasksByMessageRef.remove(messageRef);
                RILogger.printTaskDebug(
                  'ResilientIsolate',
                  '_handleTaskResult',
                  taskId,
                  'Removed sequential task $taskName with messageRef $messageRef from running list',
                );
              }

              // Check if there are other tasks with the same messageRef
              final allTasks = await SharedPreferencesStore.loadAllTasks();
              final tasksWithSameMessageRef = allTasks.where((t) {
                final taskMessageRef = t.inputData['messageRef'] as String?;
                final taskRef = t.inputData['ref'] as String?;
                return (taskMessageRef == messageRef) ||
                    (taskRef == messageRef);
              }).toList();

              if (tasksWithSameMessageRef.isNotEmpty) {
                RILogger.printTaskDebug(
                  'ResilientIsolate',
                  '_handleTaskResult',
                  taskId,
                  'Found ${tasksWithSameMessageRef.length} other tasks with the same messageRef: $messageRef',
                );

                for (final t in tasksWithSameMessageRef) {
                  RILogger.printTaskDebug(
                    'ResilientIsolate',
                    '_handleTaskResult',
                    taskId,
                    'Related task: ID=${t.id}, name=${t.name}, status=${t.status}, createdAt=${DateTime.fromMillisecondsSinceEpoch(t.createdAt)}',
                  );
                }
              }
            }

            // Create completion event to ensure UI is updated
            _eventListener.emitEvent(
              IsolateEvent(
                type: IsolateEventType.taskCompleted,
                taskId: taskId,
                data: {'result': success},
              ),
            );

            RILogger.printTaskDebug(
              'ResilientIsolate',
              '_handleTaskResult',
              taskId,
              'Emitted completion event for missing compressAndUploadVideoMessage task to ensure UI update',
            );
          }

          // Remove task from waiting lists if any
          _pendingIsolateReadyTasks.removeWhere((t) => t.id == taskId);

          // Send acknowledgment so worker isolate knows the result has been processed
          _sendAcknowledgment(taskId);

          return;
        }

        // Log task completion time
        final taskDuration = now - task.createdAt;
        RILogger.printTaskDebug(
          'ResilientIsolate',
          '_handleTaskResult',
          taskId,
          'Task ${task.name} completed in ${taskDuration}ms, success: $success, retryCount: ${task.retryCount}',
        );

        // If task was canceled, just acknowledge but don't update status
        if (task.status == TaskStatus.canceled) {
          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_handleTaskResult',
            taskId,
            'Task was canceled',
          );
          _sendAcknowledgment(taskId);
          return;
        }

        // Check if task has completed or failed
        if (task.status == TaskStatus.completed ||
            task.status == TaskStatus.failed) {
          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_handleTaskResult',
            taskId,
            'Task already in final state: ${task.status}, ignoring duplicate result',
          );
          _sendAcknowledgment(taskId);
          return;
        }

        // Update task status with completion time
        final newStatus = success ? TaskStatus.completed : TaskStatus.failed;
        final updatedTask = task.copyWith(status: newStatus, completedAt: now);
        await SharedPreferencesStore.updateTask(updatedTask);

        // Emit event for task completion or failure
        if (success) {
          _eventListener.emitEvent(
            IsolateEvent(
              type: IsolateEventType.taskCompleted,
              taskId: taskId,
              data: {'result': success},
            ),
          );
        } else {
          // Add error information if available
          final errorData = result['error'];
          final isNetworkError = result['isNetworkError'] as bool? ??
              _isNetworkRelatedError(errorData);

          _eventListener.emitEvent(
            IsolateEvent(
              type: IsolateEventType.taskFailed,
              taskId: taskId,
              data: {
                'error': errorData ?? 'Task execution failed',
                'isNetworkError': isNetworkError,
              },
            ),
          );
        }

        // Special handling for sequential task when failed
        if (_sequentialTasks.contains(task.name)) {
          // Check if there is a messageRef in inputData
          final messageRef = task.inputData['messageRef'] as String? ??
              task.inputData['ref'] as String?;

          if (messageRef != null) {
            // Release the lock for sequential task
            _releaseSequentialTaskLock(messageRef);

            // Keep old handling for _runningSequentialTasksByMessageRef to ensure backward compatibility
            if (_runningSequentialTasksByMessageRef.containsKey(messageRef)) {
              final taskName =
                  _runningSequentialTasksByMessageRef.remove(messageRef);
              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_handleTaskResult',
                taskId,
                'Removed failed sequential task $taskName with messageRef $messageRef from running list',
              );
            }

            RILogger.printTaskDebug(
              'ResilientIsolate',
              '_handleTaskResult',
              taskId,
              'Released sequential task lock for failed task ${task.name} with messageRef $messageRef',
            );
          }
        }

        // Decrement running count
        _runningTaskCount = math.max(0, _runningTaskCount - 1);

        // Decrement running task count for this task name
        if (_runningTasksByName.containsKey(task.name)) {
          _runningTasksByName[task.name] =
              math.max(0, (_runningTasksByName[task.name] ?? 0) - 1);

          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_handleTaskResult',
            taskId,
            'Task completed, running count: $_runningTaskCount, ${task.name} running: ${_runningTasksByName[task.name]}',
          );
        }

        // Remove start time for this task
        _runningTasksStartTime.remove(taskId);

        RILogger.printTaskDebug(
          'ResilientIsolate',
          '_handleTaskResult',
          taskId,
          'Removed task from _runningTasksStartTime',
        );

        // If it's a video task, continue processing the next video task
        if (_sequentialTasks.contains(task.name)) {
          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_handleTaskResult',
            taskId,
            'Video task completed, scheduling next video task processing',
          );
          Future.microtask(() => _processVideoTasks());
        }

        // Already processed sequential task above, no need to process again here

        // Handle retry if needed
        if (!success &&
            task.retryCount < task.maxRetries &&
            task.status != TaskStatus.unrecoverable) {
          final retryTask = task.copyWith(
            status: TaskStatus.pending,
            retryCount: task.retryCount + 1,
          );

          await SharedPreferencesStore.updateTask(retryTask);

          // Check if this is a network-related failure
          final errorData = result['error'];
          final isNetworkError = result['isNetworkError'] as bool? ??
              _isNetworkRelatedError(errorData);

          // For network errors, add to network-pending queue
          if (isNetworkError && task.networkRequired) {
            RILogger.printTaskDebug(
              'ResilientIsolate',
              '_handleTaskResult',
              taskId,
              'Network-related failure detected. Adding to network-pending queue. Task: ${task.name}, MessageRef: $messageRef',
            );

            // Use RetryManager to retry task
            _taskQueue.add(retryTask);

            RILogger.printTaskDebug(
              'ResilientIsolate',
              '_handleTaskResult',
              taskId,
              'Network-related failure detected. Adding to task queue for retry: ${task.name}, MessageRef: $messageRef',
            );

            // Schedule queue processing
            _scheduleQueueProcessing(immediate: true);
          } else {
            // Calculate wait time based on retry count (exponential backoff)
            final retryDelay = math.min(
              task.retryDelay * math.pow(1.5, task.retryCount).toInt(),
              60000, // Maximum 60 seconds
            );

            // For non-network errors, add back to queue after delay
            Future.delayed(Duration(milliseconds: retryDelay), () {
              _taskQueue.add(retryTask);
              // Only call _scheduleQueueProcessing() when there are waiting tasks
              if (_taskQueue.isNotEmpty) {
                _scheduleQueueProcessing();
              }
            });
          }

          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_handleTaskResult',
            taskId,
            'Task scheduled for retry, attempt: ${retryTask.retryCount}',
          );
        } else {
          // Complete the future if it exists
          if (_callbackMap.containsKey(taskId)) {
            _callbackMap[taskId]!.complete(success);
            _callbackMap.remove(taskId);
          }

          // Task cleanup is now handled automatically through task expiration
        }

        // Send acknowledgment
        _sendAcknowledgment(taskId);

        // Process next task - ensure this is called to handle concurrent tasks
        // Use _scheduleQueueProcessing to avoid calling _processQueue too many times
        // Always call _scheduleQueueProcessing() after task completion to ensure new tasks are processed
        _scheduleQueueProcessing(immediate: true);

        // Log information about queue processing
        RILogger.printTaskDebug(
          'ResilientIsolate',
          '_handleTaskResult',
          taskId,
          'Scheduled queue processing after task completion, queue size: ${_taskQueue.length}',
        );
      } catch (e, stackTrace) {
        RILogger.printTaskDebug(
          'ResilientIsolate',
          '_handleTaskResult',
          taskId,
          'Error handling task result: $e\n$stackTrace',
        );

        // Still send acknowledgment to avoid hanging task
        _sendAcknowledgment(taskId);

        // Still continue processing queue if there are waiting tasks
        if (_taskQueue.isNotEmpty) {
          _scheduleQueueProcessing();
        }
      }
    });
  }

  void _sendAcknowledgment(String taskId) {
    if (_requestPort == null) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_sendAcknowledgment',
        'Request port not available for ack',
      );
      return;
    }

    _requestPort!.send({'ack': taskId});
  }

  /// Send clear task message to worker isolate
  /// Only clear tasks in RetryManager, don't delete tasks in SharedPreferencesStore
  void _sendClearTasksCommand() {
    if (_requestPort == null) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_sendClearTasksCommand',
        'Request port not available for clear tasks command',
      );
      return;
    }

    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      '_sendClearTasksCommand',
      'Sending clear tasks command to worker isolate',
    );

    // Send clear task message to worker isolate
    _requestPort!.send({'clearTasks': true});

    // Wait a short time to ensure the message is processed
    Future.delayed(Duration(milliseconds: 100), () {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_sendClearTasksCommand',
        'Sent clear tasks command to worker isolate',
      );
    });
  }

  /// Send initialization info to worker isolate
  Future<void> _sendInitializationData() async {
    if (_requestPort == null) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_sendInitializationData',
        'Request port not available for initialization',
      );
      return;
    }

    try {
      final token = await SharedPreferencesStore.getDecryptToken();
      final metadata = await SharedPreferencesStore.getDecryptMetadata();

      if (token == null || metadata == null) {
        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_sendInitializationData',
          'Token or metadata not available, skipping initialization',
        );
        return;
      }

      final initData = {
        'token': token,
        'metadata': metadata.toJson(),
      };

      _requestPort!.send({'init': initData});

      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_sendInitializationData',
        'Initialization info sent to worker isolate',
      );
    } catch (e, stackTrace) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_sendInitializationData',
        'Error sending initialization info: $e',
      );
      RILogger.printError('Error sending initialization info', e, stackTrace);
    }
  }

  void _handleAcknowledgment(String taskId) async {
    // Remove from unacknowledged results
    await AckStore.deleteResult(taskId);
  }

  /// Method to get lock for sequential task
  TaskLock? _getSequentialTaskLock(String messageRef) {
    return _sequentialTaskLocks[messageRef];
  }

  /// Method to create lock for sequential task
  TaskLock _createSequentialTaskLock({
    required String messageRef,
    required String taskName,
    required String taskId,
  }) {
    final newLock = TaskLock(
      messageRef: messageRef,
      taskName: taskName,
      taskId: taskId,
    );
    _sequentialTaskLocks[messageRef] = newLock;
    return newLock;
  }

  /// Method to release lock for sequential task
  void _releaseSequentialTaskLock(String messageRef) {
    final lock = _sequentialTaskLocks[messageRef];
    if (lock != null) {
      lock.release();
      _sequentialTaskLocks.remove(messageRef);

      RILogger.printTaskDebug(
        'ResilientIsolate',
        '_releaseSequentialTaskLock',
        lock.taskId,
        'Released sequential task lock for messageRef: $messageRef, taskName: ${lock.taskName}',
      );
    }
  }

  /// Method to clean up expired locks
  void _cleanupExpiredTaskLocks() {
    final expiredMessageRefs = <String>[];
    final timeout = Duration(minutes: 10); // Timeout duration for lock

    for (final entry in _sequentialTaskLocks.entries) {
      if (entry.value.isExpired(timeout)) {
        expiredMessageRefs.add(entry.key);

        RILogger.printTaskDebug(
          'ResilientIsolate',
          '_cleanupExpiredTaskLocks',
          entry.value.taskId,
          'Removing expired task lock for messageRef: ${entry.key}, taskName: ${entry.value.taskName}',
        );
      }
    }

    for (final messageRef in expiredMessageRefs) {
      _sequentialTaskLocks.remove(messageRef);
    }
  }

  /// Recover pending tasks after isolate is initialized
  Future<void> _recoverPendingTasks() async {
    // Ensure isolate has been initialized before recovering tasks
    if (_requestPort == null) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_recoverPendingTasks',
        'Worker isolate not ready, cannot recover tasks yet',
      );
      return;
    }

    // Ensure taskRegistrationPort has been set up
    if (_taskRegistrationPort == null) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_recoverPendingTasks',
        'Task registration port not available, cannot recover tasks properly',
      );
      return;
    }

    // If no tasks to recover, try loading from storage
    if (_pendingRecoveryTasks.isEmpty) {
      // Load all tasks from storage
      final tasks = await SharedPreferencesStore.loadAllTasks();

      if (tasks.isEmpty) {
        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_recoverPendingTasks',
          'No pending tasks to recover from storage',
        );
        return;
      }

      // Filter tasks that need recovery (running or pending)
      // Also remove tasks that have timed out
      final now = DateTime.now();
      _pendingRecoveryTasks = tasks.where((task) {
        // Only process tasks that are running or pending
        if (task.status != TaskStatus.running &&
            task.status != TaskStatus.pending) {
          return false;
        }

        // Check if task has timed out
        if (task.inputData.containsKey('creationTime')) {
          try {
            final creationTimeStr = task.inputData['creationTime'] as String;
            final creationTime = DateTime.parse(creationTimeStr);
            final timeoutTime =
                creationTime.add(GlobalConfig.sendTimeoutDuration);

            // If task has timed out, delete it from SharedPreferences
            if (timeoutTime.isBefore(now)) {
              RILogger.printClassMethodDebug(
                'ResilientIsolate',
                '_recoverPendingTasks',
                'Task ${task.id} (${task.name}) has expired, deleting from storage',
              );

              // Delete expired task
              SharedPreferencesStore.deleteTask(task.id);
              return false;
            }
          } catch (e) {
            // If there's an error parsing creationTime, keep the task
            RILogger.printClassMethodDebug(
              'ResilientIsolate',
              '_recoverPendingTasks',
              'Error parsing creationTime for task ${task.id}: $e',
            );
          }
        }

        return true;
      }).toList();

      if (_pendingRecoveryTasks.isEmpty) {
        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_recoverPendingTasks',
          'No running or pending tasks to recover from ${tasks.length} total tasks',
        );
        return;
      }
    }

    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      '_recoverPendingTasks',
      'Recovering ${_pendingRecoveryTasks.length} pending tasks',
    );

    // Sort tasks by priority order
    _pendingRecoveryTasks.sort((a, b) {
      // Prioritize running tasks first
      if (a.status == TaskStatus.running && b.status != TaskStatus.running) {
        return -1;
      }
      if (a.status != TaskStatus.running && b.status == TaskStatus.running) {
        return 1;
      }

      // Prioritize tasks with higher priority
      if (a.priority.index != b.priority.index) {
        return a.priority.index - b.priority.index;
      }

      // Prioritize older tasks
      return a.createdAt - b.createdAt;
    });

    // Limit the number of tasks recovered simultaneously to avoid overload
    final batchSize = 2; // Reduced batch size from 5 to 2 to reduce load
    final totalTasks = _pendingRecoveryTasks.length;
    int processedCount = 0;
    int successCount = 0;
    int failureCount = 0;

    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      '_recoverPendingTasks',
      'Using smaller batch size ($batchSize) to reduce load during task recovery',
    );

    // Process in batches
    for (int i = 0; i < totalTasks; i += batchSize) {
      final endIndex = math.min(i + batchSize, totalTasks);
      final batch = _pendingRecoveryTasks.sublist(i, endIndex);

      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_recoverPendingTasks',
        'Processing batch ${i ~/ batchSize + 1}/${(totalTasks / batchSize).ceil()}: ${batch.length} tasks',
      );

      // Process each task in the batch
      for (final task in batch) {
        try {
          processedCount++;

          // Check current state of the task again
          final currentTask = await SharedPreferencesStore.loadTask(task.id);
          if (currentTask == null) {
            RILogger.printTaskDebug(
              'ResilientIsolate',
              '_recoverPendingTasks',
              task.id,
              'Task no longer exists in storage, skipping',
            );
            continue;
          }

          // If task is completed or canceled, skip
          if (currentTask.status == TaskStatus.completed ||
              currentTask.status == TaskStatus.failed ||
              currentTask.status == TaskStatus.canceled) {
            RILogger.printTaskDebug(
              'ResilientIsolate',
              '_recoverPendingTasks',
              task.id,
              'Task already in final state: ${currentTask.status}, skipping',
            );
            continue;
          }

          // Get messageRef and fileRef info for logging
          final messageRef = currentTask.inputData['messageRef'] as String? ??
              currentTask.inputData['ref'] as String?;
          final fileRef = currentTask.inputData['fileRef'] as String?;

          if (currentTask.status == TaskStatus.running) {
            // For uploadFile task, don't convert running to pending to match video flow
            if (currentTask.name == TaskNameEnum.uploadFile.value) {
              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_recoverPendingTasks',
                task.id,
                '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Skipping running uploadFile task to match video flow',
              );
              continue;
            }

            // Reset running tasks to pending
            final updatedTask =
                currentTask.copyWith(status: TaskStatus.pending);
            await SharedPreferencesStore.updateTask(updatedTask);

            RILogger.printTaskDebug(
              'ResilientIsolate',
              '_recoverPendingTasks',
              task.id,
              '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Converted running task to pending: ${task.name}',
            );

            // Register task again through worker isolate
            final success = await _registerTaskInWorker(updatedTask);
            if (success) {
              successCount++;
              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_recoverPendingTasks',
                task.id,
                '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Successfully registered task in worker isolate',
              );
            } else {
              failureCount++;
              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_recoverPendingTasks',
                task.id,
                '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Failed to register task in worker isolate',
              );
            }
          } else if (currentTask.status == TaskStatus.pending) {
            RILogger.printTaskDebug(
              'ResilientIsolate',
              '_recoverPendingTasks',
              task.id,
              '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Recovering pending task: ${task.name}',
            );

            // Register task again through worker isolate
            final success = await _registerTaskInWorker(currentTask);
            if (success) {
              successCount++;
              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_recoverPendingTasks',
                task.id,
                '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Successfully registered task in worker isolate',
              );
            } else {
              failureCount++;
              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_recoverPendingTasks',
                task.id,
                '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Failed to register task in worker isolate',
              );
            }
          } else if (currentTask.status == TaskStatus.unrecoverable) {
            // Skip unrecoverable tasks
            RILogger.printTaskDebug(
              'ResilientIsolate',
              '_recoverPendingTasks',
              task.id,
              '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Skipping unrecoverable task: ${task.name}',
            );

            // Clean up old unrecoverable tasks
            final now = DateTime.now().millisecondsSinceEpoch;
            final taskAge = now - currentTask.createdAt;
            if (taskAge > 24 * 60 * 60 * 1000) {
              // 24 hours
              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_recoverPendingTasks',
                task.id,
                '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Cleaning up old unrecoverable task (age: ${taskAge / (60 * 60 * 1000)} hours)',
              );
              await SharedPreferencesStore.deleteTask(task.id);
            }
          } else {
            RILogger.printTaskDebug(
              'ResilientIsolate',
              '_recoverPendingTasks',
              task.id,
              '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Task with unknown status: ${currentTask.status}, name: ${currentTask.name}',
            );
          }
        } catch (e, stackTrace) {
          failureCount++;
          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_recoverPendingTasks',
            task.id,
            'Error recovering task: $e',
          );
          RILogger.printError('Error recovering task', e, stackTrace);
        }
      }

      // Wait a longer time between batches to avoid overload
      if (endIndex < totalTasks) {
        // Increase wait time between batches from 100ms to 300ms
        await Future.delayed(Duration(milliseconds: 300));

        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_recoverPendingTasks',
          'Waiting 300ms between batches to ensure isolate stability',
        );
      }
    }

    // Clear pending recovery tasks after processing
    _pendingRecoveryTasks.clear();

    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      '_recoverPendingTasks',
      'Task recovery completed: $successCount succeeded, $failureCount failed, $processedCount total',
    );

    // Schedule queue processing after recovering tasks
    if (_taskQueue.isNotEmpty) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_recoverPendingTasks',
        'Processing ${_taskQueue.length} tasks in regular queue',
      );
      Future.microtask(() => _processQueue());
    }

    // Schedule video queue processing if there are waiting video tasks
    if (_videoTaskQueue.isNotEmpty) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_recoverPendingTasks',
        'Processing ${_videoTaskQueue.length} tasks in video queue',
      );
      Future.microtask(() => _processVideoTasks());
    }
  }

  /// Register a task in the worker isolate
  Future<bool> _registerTaskInWorker(TaskModel task) async {
    if (_requestPort == null) {
      RILogger.printTaskDebug(
        'ResilientIsolate',
        '_registerTaskInWorker',
        task.id,
        'Request port not available, cannot register task in worker',
      );
      return false;
    }

    try {
      RILogger.printTaskDebug(
        'ResilientIsolate',
        '_registerTaskInWorker',
        task.id,
        'Registering task in worker: ${task.name}',
      );

      // Update creationTime in inputData to avoid timeout
      final updatedInputData = Map<String, dynamic>.from(task.inputData);

      // Update creationTime to current time
      updatedInputData['creationTime'] = DateTime.now().toIso8601String();

      RILogger.printTaskDebug(
        'ResilientIsolate',
        '_registerTaskInWorker',
        task.id,
        'Updated creationTime to current time',
      );

      // Update task in SharedPreferences
      final updatedTask = task.copyWith(
        inputData: updatedInputData,
      );
      await SharedPreferencesStore.updateTask(updatedTask);

      // Send registration request to worker
      _requestPort!.send({
        'registerTask': {
          'taskId': task.id,
          'name': task.name,
          'inputData': updatedInputData,
          'priority': task.priority.index,
          'isReferenceTask': task.isReferenceTask,
          'networkRequired': task.networkRequired,
        },
      });

      // Add task to queue for processing
      if (_sequentialTasks.contains(task.name)) {
        // Add to video queue
        _videoTaskQueue.add(updatedTask);

        RILogger.printTaskDebug(
          'ResilientIsolate',
          '_registerTaskInWorker',
          task.id,
          'Added task to video queue for processing: ${task.name}',
        );

        // Process video tasks using push mechanism
        Future.microtask(() => _processVideoTasks());
      } else {
        // Add to regular queue
        _taskQueue.add(updatedTask);

        RILogger.printTaskDebug(
          'ResilientIsolate',
          '_registerTaskInWorker',
          task.id,
          'Added task to regular queue for processing: ${task.name}',
        );

        // Process regular queue
        _scheduleQueueProcessing(immediate: true);
      }

      return true;
    } catch (e) {
      RILogger.printTaskDebug(
        'ResilientIsolate',
        '_registerTaskInWorker',
        task.id,
        'Error registering task in worker: $e',
      );
      return false;
    }
  }

  /// Stops the worker isolate and cleans up resources
  ///
  /// This method stops the worker isolate without restarting it,
  /// which is useful when shutting down the application or logging out.
  /// It does not affect tasks in storage, only stops the running isolate.
  Future<void> stopWorkerIsolate() async {
    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      'stopWorkerIsolate',
      'Stopping worker isolate',
    );

    // Kill current isolate if it exists
    if (_workerIsolate != null) {
      _workerIsolate!.kill();
      _workerIsolate = null;
    }

    // Close current ports
    _responsePort?.close();
    _ackPort?.close();
    _responsePort = null;
    _ackPort = null;
    _requestPort = null;

    // Reset running task count
    _runningTaskCount = 0;

    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      'stopWorkerIsolate',
      'Worker isolate stopped successfully',
    );
  }

  /// Stop worker (cancel tasks registered with Workmanager)
  Future<void> _stopWorker() async {
    return;
    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      '_stopWorker',
      'Stopping worker tasks',
    );

    try {
      // Cancel all tasks registered with Workmanager
      await Workmanager().cancelAll();

      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_stopWorker',
        'All worker tasks cancelled successfully',
      );
    } catch (e, stackTrace) {
      RILogger.printError(
        'Error cancelling worker tasks',
        e,
        stackTrace,
      );
    }
  }

  /// Kill current isolate
  Future<void> _killIsolate() async {
    return;
    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      '_killIsolate',
      'Killing isolate',
    );

    // Cancel all timeout timers
    for (final timer in _taskTimeoutTimers.values) {
      timer.cancel();
    }
    _taskTimeoutTimers.clear();

    // Kill current isolate if it exists
    if (_workerIsolate != null) {
      try {
        _workerIsolate!.kill(priority: Isolate.immediate);
        _workerIsolate = null;

        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_killIsolate',
          'Killed existing isolate',
        );
      } catch (e, stackTrace) {
        RILogger.printError(
          'Error killing isolate',
          e,
          stackTrace,
        );
      }
    }

    // Close current ports
    _responsePort?.close();
    _ackPort?.close();

    // Reset isolate-related variables
    _requestPort = null;
    _responsePort = null;
    _ackPort = null;

    // Reset task state variables
    _resetInMemoryTaskState();

    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      '_killIsolate',
      'Isolate killed successfully',
    );
  }

  /// Reset all memory variables related to tasks
  void _resetInMemoryTaskState() {
    // Reset task-related variables in memory
    _runningTaskCount = 0;
    _runningTasksByName.clear();
    _runningSequentialTasksByMessageRef.clear();
    _taskTimeoutTimers.forEach((_, timer) => timer.cancel());
    _taskTimeoutTimers.clear();

    // Reset queues
    _taskQueue.clear();
    _videoTaskQueue.clear();

    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      '_resetInMemoryTaskState',
      'Reset all in-memory task state',
    );
  }

  /// Get list of waiting and running tasks from isolate
  ///
  /// Use ONLY SharedPreferencesStore to get the most accurate task list
  Future<List<TaskModel>> _getPendingAndRunningTasks() async {
    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      '_getPendingAndRunningTasks',
      'Getting pending and running tasks from SharedPreferencesStore',
    );

    final result = <TaskModel>[];

    try {
      // Get all tasks from SharedPreferencesStore
      final allTasks = await SharedPreferencesStore.loadAllTasks();
      if (allTasks.isNotEmpty) {
        // Filter tasks with pending or running status
        result.addAll(
          allTasks.where(
            (task) =>
                task.status == TaskStatus.pending ||
                task.status == TaskStatus.running,
          ),
        );
      }

      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_getPendingAndRunningTasks',
        'Found ${result.length} pending and running tasks from SharedPreferencesStore',
      );
    } catch (e, stackTrace) {
      RILogger.printError(
        'Error getting pending and running tasks from SharedPreferencesStore',
        e,
        stackTrace,
      );
    }

    return result;
  }

  /// Get list of waiting tasks from worker (via SharedPreferencesStore)
  Future<List<TaskModel>> _getPendingTasksFromWorker() async {
    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      '_getPendingTasksFromWorker',
      'Getting pending tasks from worker',
    );

    final result = <TaskModel>[];

    try {
      // Get all tasks from SharedPreferencesStore
      final allTasks = await SharedPreferencesStore.loadAllTasks();
      if (allTasks.isNotEmpty) {
        // Filter tasks with pending or running status
        result.addAll(
          allTasks.where(
            (task) =>
                task.status == TaskStatus.pending ||
                task.status == TaskStatus.running,
          ),
        );
      }

      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_getPendingTasksFromWorker',
        'Found ${result.length} pending tasks from worker',
      );
    } catch (e, stackTrace) {
      RILogger.printError(
        'Error getting pending tasks from worker',
        e,
        stackTrace,
      );
    }

    return result;
  }

  /// Clears all tasks from the in-memory queue and stops running tasks
  ///
  /// This method clears all pending tasks from the in-memory queue,
  /// cancels all running tasks, and resets the running task count.
  /// It does not stop the isolate itself, allowing it to be reused for future sessions.
  Future<void> clearTaskQueue() async {
    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      'clearTaskQueue',
      'Clearing task queue and stopping running tasks',
    );

    // Clear the task queue
    _taskQueue.clear();

    // Cancel all timeout timers
    for (final timerId in _taskTimeoutTimers.keys.toList()) {
      _cancelTaskTimeoutTimer(timerId);
    }

    // Reset running task count
    _runningTaskCount = 0;
    _runningTasksByName.clear();
    _runningSequentialTasksByMessageRef.clear();

    // Clear task tracking
    _taskTimeoutTimers.clear();
    _callbackMap.clear();
    _taskResults.clear();

    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      'clearTaskQueue',
      'Task queue cleared and running tasks stopped successfully',
    );
  }

  /// Check if an error is related to network connectivity
  bool _isNetworkRelatedError(dynamic error) {
    if (error == null) return false;

    final errorString = error.toString().toLowerCase();

    // Log detailed error for debugging
    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      '_isNetworkRelatedError',
      'Checking if error is network-related: $errorString',
    );

    // List of keywords related to network errors
    final networkErrorKeywords = [
      'network',
      'socket',
      'connection',
      'timeout',
      'unreachable',
      'internet',
      'dio',
      'host',
      'dns',
      'reset',
      'refused',
      'connect',
      'offline',
    ];

    // Check if error contains any keywords
    final isNetworkError =
        networkErrorKeywords.any((keyword) => errorString.contains(keyword));

    // Log result
    if (isNetworkError) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_isNetworkRelatedError',
        'NETWORK DEBUG: Error is network-related: $errorString',
      );
    }

    return isNetworkError;
  }

  /// Check and process hung tasks
  Future<void> _checkAndHandleStuckTasks() async {
    final now = DateTime.now().millisecondsSinceEpoch;
    final stuckTaskIds = <String>[];

    // Maximum allowed time for a task to run (10 minutes)
    final maxTaskRunTime = 10 * 60 * 1000; // 10 minutes

    // Check running tasks
    for (final entry in _runningTasksStartTime.entries) {
      final taskId = entry.key;
      final startTime = entry.value;
      final runTime = now - startTime;

      // If task has been running too long, mark as hung
      if (runTime > maxTaskRunTime) {
        stuckTaskIds.add(taskId);

        RILogger.printTaskDebug(
          'ResilientIsolate',
          '_checkAndHandleStuckTasks',
          taskId,
          'Task is stuck, running for ${runTime}ms (max allowed: ${maxTaskRunTime}ms)',
        );
      }
    }

    // Process hung tasks
    for (final taskId in stuckTaskIds) {
      try {
        // Get task information from SharedPreferencesStore
        final task = await SharedPreferencesStore.loadTask(taskId);
        if (task == null) {
          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_checkAndHandleStuckTasks',
            taskId,
            'Task not found in SharedPreferencesStore, removing from tracking',
          );

          // Remove from tracking maps
          _runningTasksStartTime.remove(taskId);
          continue;
        }

        RILogger.printTaskDebug(
          'ResilientIsolate',
          '_checkAndHandleStuckTasks',
          taskId,
          'Handling stuck task: ${task.name}, status: ${task.status}',
        );

        // Decrease running task count
        _runningTaskCount = math.max(0, _runningTaskCount - 1);

        // Decrease running task count by type
        if (_runningTasksByName.containsKey(task.name)) {
          _runningTasksByName[task.name] = math.max(
            0,
            (_runningTasksByName[task.name] ?? 0) - 1,
          );

          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_checkAndHandleStuckTasks',
            taskId,
            'Decremented running count for ${task.name}: ${_runningTasksByName[task.name]}',
          );
        }

        // Remove from start time tracking list
        _runningTasksStartTime.remove(taskId);

        // If sequential task, release lock
        if (_sequentialTasks.contains(task.name)) {
          // Check if there is a messageRef in inputData
          final messageRef = task.inputData['messageRef'] as String? ??
              task.inputData['ref'] as String?;

          if (messageRef != null) {
            // Release the lock for sequential task
            _releaseSequentialTaskLock(messageRef);

            // Keep old handling for _runningSequentialTasksByMessageRef to ensure backward compatibility
            if (_runningSequentialTasksByMessageRef.containsKey(messageRef)) {
              final taskName =
                  _runningSequentialTasksByMessageRef.remove(messageRef);

              RILogger.printTaskDebug(
                'ResilientIsolate',
                '_checkAndHandleStuckTasks',
                taskId,
                'Removed stuck sequential task $taskName with messageRef $messageRef from running list',
              );
            }
          }
        }

        // Mark task as failed
        final updatedTask = task.copyWith(
          status: TaskStatus.failed,
          completedAt: now,
        );

        await SharedPreferencesStore.updateTask(updatedTask);

        RILogger.printTaskDebug(
          'ResilientIsolate',
          '_checkAndHandleStuckTasks',
          taskId,
          'Updated stuck task status to failed',
        );

        // Add to queue for retry if needed
        if (task.retryCount < task.maxRetries) {
          final retryTask = task.copyWith(
            status: TaskStatus.pending,
            retryCount: task.retryCount + 1,
          );

          await SharedPreferencesStore.updateTask(retryTask);
          _taskQueue.add(retryTask);

          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_checkAndHandleStuckTasks',
            taskId,
            'Added stuck task to queue for retry, retryCount: ${retryTask.retryCount}',
          );
        }
      } catch (e, stackTrace) {
        RILogger.printError('Error handling stuck task', e, stackTrace);
      }
    }

    // If there are hung tasks that have been processed, schedule queue processing
    if (stuckTaskIds.isNotEmpty) {
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        '_checkAndHandleStuckTasks',
        'Processed ${stuckTaskIds.length} stuck tasks, scheduling queue processing',
      );

      Future.microtask(() => _processQueue());
    }
  }

  /// Process video tasks using push mechanism
  Future<void> _processVideoTasks() async {
    // Use lock to ensure only one video task processing at a time
    return _videoTaskLock.synchronized(() async {
      // If already processing video task, do nothing
      if (_isProcessingVideoTask) {
        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_processVideoTasks',
          'Already processing video task, skipping',
        );
        return;
      }

      // Check if there are any video tasks in the queue
      if (_videoTaskQueue.isEmpty) {
        RILogger.printClassMethodDebug(
          'ResilientIsolate',
          '_processVideoTasks',
          'No video tasks in queue',
        );
        return;
      }

      try {
        // Mark as processing video task
        _isProcessingVideoTask = true;

        // Check and process hung tasks before processing queue
        await _checkAndHandleStuckTasks();

        // Ensure isolate is running
        await _ensureIsolateRunning();

        // Get next task from video queue
        final nextTask = _videoTaskQueue.peekFirst();
        if (nextTask == null) {
          _isProcessingVideoTask = false;
          return;
        }

        // Check constraints before processing task
        final currentTask = await SharedPreferencesStore.loadTask(nextTask.id);
        if (currentTask == null) {
          // Task doesn't exist in storage, remove from queue
          _videoTaskQueue.removeFirst();
          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_processVideoTasks',
            nextTask.id,
            'Task not found in storage, removing from queue',
          );
          _isProcessingVideoTask = false;

          // Continue processing next task
          Future.microtask(() => _processVideoTasks());
          return;
        } else if (currentTask.status == TaskStatus.canceled) {
          // Task has been canceled, remove from queue
          _videoTaskQueue.removeFirst();
          RILogger.printTaskDebug(
            'ResilientIsolate',
            '_processVideoTasks',
            currentTask.id,
            'Task was canceled, removing from queue',
          );
          _isProcessingVideoTask = false;

          // Continue processing next task
          Future.microtask(() => _processVideoTasks());
          return;
        }

        // Remove task from queue
        _videoTaskQueue.removeFirst();

        RILogger.printTaskDebug(
          'ResilientIsolate',
          '_processVideoTasks',
          currentTask.id,
          'Processing video task: ${currentTask.name}',
        );

        // Send task to worker isolate
        _sendTaskToWorker(currentTask);

        // Increase running task count
        _runningTaskCount++;

        // Increase running task count by type
        _runningTasksByName[currentTask.name] =
            (_runningTasksByName[currentTask.name] ?? 0) + 1;

        // Record start time for this task
        _runningTasksStartTime[currentTask.id] =
            DateTime.now().millisecondsSinceEpoch;

        // Mark as no longer processing video task
        _isProcessingVideoTask = false;
      } catch (e, stackTrace) {
        RILogger.printError('Error processing video task', e, stackTrace);
        _isProcessingVideoTask = false;
      }
    });
  }

  Future<void> dispose() async {
    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      'dispose',
      'Disposing ResilientIsolate',
    );

    // Stop isolate health monitoring if active
    if (_enableHealthMonitoring) {
      IsolateHealthMonitor().stopMonitoring();
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        'dispose',
        'Stopped isolate health monitoring',
      );
    }

    // Task cleanup service has been removed

    // Cancel WebSocket resume subscription
    if (_webSocketResumeSubscription != null) {
      _webSocketResumeSubscription!.cancel();
      _webSocketResumeSubscription = null;

      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        'dispose',
        'Cancelled WebSocket resume subscription',
      );
    }

    // Close AppLifecycleState stream controller
    if (!_appLifecycleStateController.isClosed) {
      await _appLifecycleStateController.close();

      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        'dispose',
        'Closed AppLifecycleState stream controller',
      );
    }

    _responsePort?.close();
    _ackPort?.close();

    if (_workerIsolate != null) {
      _workerIsolate!.kill();
      _workerIsolate = null;
    }

    // Cancel all timeout timers
    for (final timer in _taskTimeoutTimers.values) {
      timer.cancel();
    }
    _taskTimeoutTimers.clear();

    _responsePort = null;
    _ackPort = null;
    _requestPort = null;
    _isInitialized = false;
    _runningTaskCount = 0;
    _taskQueue.clear();
    _callbackMap.clear();

    // Dispose event listener
    _eventListener.dispose();

    // Dispose SharedPreferencesStore resources
    try {
      await SharedPreferencesStore.dispose();
      RILogger.printClassMethodDebug(
        'ResilientIsolate',
        'dispose',
        'SharedPreferencesStore disposed successfully',
      );
    } catch (e, stackTrace) {
      RILogger.printError(
        'Error disposing SharedPreferencesStore',
        e,
        stackTrace,
      );
    }

    RILogger.printClassMethodDebug(
      'ResilientIsolate',
      'dispose',
      'ResilientIsolate disposed successfully',
    );
  }
}
