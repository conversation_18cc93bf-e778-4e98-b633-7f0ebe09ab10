import 'dart:convert';

import 'package:chat/chat.dart';
import 'package:shared/shared.dart';

class WorkerSendMessageResult {
  WorkerSendMessageResult({
    required this.messageRef,
    this.messageId,
    this.workspaceId,
    this.channelId,
    this.userId,
    this.sendMsgErrorReason,
    this.mapAttachmentStatusRaw,
  });

  final String messageRef;
  final String? messageId;
  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final String? sendMsgErrorReason;
  final String? mapAttachmentStatusRaw;

  Map<String, AttachmentStatusEnum>? get mapAttachmentStatus {
    if (StringUtils.isNullOrEmpty(mapAttachmentStatusRaw)) return null;
    try {
      final dynamic decoded = jsonDecode(mapAttachmentStatusRaw!);
      if (decoded is! Map<String, dynamic>) return null;
      final map = decoded.map<String, int>(
        (key, value) => MapEntry(key, value as int),
      );
      if (map.isEmpty) return null;
      return map.map(
        (key, value) =>
            MapEntry(key, AttachmentStatusEnum.getEnumByValue(value)),
      );
    } catch (e) {
      return null;
    }
  }

  /// Convert object to JSON
  Map<String, dynamic> toJson() {
    return {
      'messageRef': messageRef,
      'messageId': messageId,
      'workspaceId': workspaceId,
      'channelId': channelId,
      'userId': userId,
      'sendMsgErrorReason': sendMsgErrorReason,
      'mapAttachmentStatusRaw': mapAttachmentStatusRaw,
    };
  }

  /// Create object from JSON
  factory WorkerSendMessageResult.fromJson(Map<String, dynamic> json) {
    return WorkerSendMessageResult(
      messageRef: json['messageRef'] as String,
      messageId: json['messageId'] as String?,
      workspaceId: json['workspaceId'] as String?,
      channelId: json['channelId'] as String?,
      userId: json['userId'] as String?,
      sendMsgErrorReason: json['sendMsgErrorReason'] as String?,
      mapAttachmentStatusRaw: json['mapAttachmentStatusRaw'] as String?,
    );
  }

  /// Copy object with modified fields
  WorkerSendMessageResult copyWith({
    String? messageRef,
    String? messageId,
    String? workspaceId,
    String? channelId,
    String? userId,
    String? sendMsgErrorReason,
    String? mapAttachmentStatusRaw,
  }) {
    return WorkerSendMessageResult(
      messageRef: messageRef ?? this.messageRef,
      messageId: messageId ?? this.messageId,
      workspaceId: workspaceId ?? this.workspaceId,
      channelId: channelId ?? this.channelId,
      userId: userId ?? this.userId,
      sendMsgErrorReason: sendMsgErrorReason ?? this.sendMsgErrorReason,
      mapAttachmentStatusRaw:
          mapAttachmentStatusRaw ?? this.mapAttachmentStatusRaw,
    );
  }

  @override
  String toString() {
    return jsonEncode(this.toJson());
  }
}
