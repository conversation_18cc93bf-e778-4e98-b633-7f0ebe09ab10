import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

import 'worker_file_metadata.dart';

part 'worker_media_object.g.dart';

@JsonSerializable(explicitToJson: true)
class WorkerMediaObject {
  WorkerMediaObject({
    this.fileId,
    this.attachmentType,
    this.fileUrl,
    this.fileMetadataEmbed,
    this.thumbnailUrl,
    this.fileRef,
    this.attachmentId,
    this.channelId,
    this.workspaceId,
    this.userId,
    this.messageId,
  });

  String? fileId;
  int? attachmentType;
  String? fileUrl;
  String? fileMetadataEmbed;

  @JsonKey(includeFromJson: false, includeToJson: true)
  WorkerFileMetadata? get fileMetadata => fileMetadataEmbed == null
      ? null
      : WorkerFileMetadata.fromJson(jsonDecode(fileMetadataEmbed!));
  final String? thumbnailUrl;

  // final V3AudioMetadata? audioMetadata;
  final String? fileRef;
  final String? attachmentId;
  final String? channelId;
  final String? workspaceId;
  final String? userId;
  final String? messageId;

  factory WorkerMediaObject.fromJson(Map<String, dynamic> json) =>
      _$WorkerMediaObjectFromJson(json);

  Map<String, dynamic> toJson() => _$WorkerMediaObjectToJson(this);
  Map<String, dynamic> toRequest() {
    final map = _$WorkerMediaObjectToJson(this);
    map.remove('fileMetadataEmbed');
    return map;
  }

  WorkerMediaObject copyWith({
    String? fileId,
    int? attachmentType,
    String? fileUrl,
    String? fileMetadataEmbed,
    String? thumbnailUrl,
    String? fileRef,
    String? attachmentId,
    String? channelId,
    String? workspaceId,
    String? userId,
    String? messageId,
  }) {
    return WorkerMediaObject(
      fileId: fileId ?? this.fileId,
      attachmentType: attachmentType ?? this.attachmentType,
      fileUrl: fileUrl ?? this.fileUrl,
      fileMetadataEmbed: fileMetadataEmbed ?? this.fileMetadataEmbed,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      fileRef: fileRef ?? this.fileRef,
      attachmentId: attachmentId ?? this.attachmentId,
      channelId: channelId ?? this.channelId,
      workspaceId: workspaceId ?? this.workspaceId,
      userId: userId ?? this.userId,
      messageId: messageId ?? this.messageId,
    );
  }
}
