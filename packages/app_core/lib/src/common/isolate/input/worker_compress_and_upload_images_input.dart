import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

import '../models/worker_upload_file.dart';
import 'worker_send_message_base.dart';

part 'worker_compress_and_upload_images_input.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class WorkerCompressAndUploadImagesInput extends WorkerSendMessageInputBase {
  WorkerCompressAndUploadImagesInput({
    required this.uploadFilesEmbed,
    required super.workspaceId,
    required super.channelId,
    required super.userId,
    required super.ref,
    required super.taskName,
    required super.attachmentType,
    required super.creationTime,
    required this.messageRef,
    this.uploadConcurrency = 1,
    this.currentBash = 0,
    this.messageId,
    super.isRefTask,
  });

  final String uploadFilesEmbed;

  final String messageRef;
  final int uploadConcurrency;
  final int currentBash;
  final String? messageId;

  @JsonKey(includeFromJson: false, includeToJson: false)
  String get uploadKey => '$workspaceId-$channelId';

  @JsonKey(includeFromJson: false, includeToJson: false)
  List<WorkerUploadFile> get files =>
      (jsonDecode(uploadFilesEmbed) as List<dynamic>)
          .map((e) => WorkerUploadFile.fromJson(e as Map<String, dynamic>))
          .toList();

  @JsonKey(includeFromJson: false, includeToJson: false)
  WorkerUploadFile? get firstFileToCompress {
    try {
      return files
          .firstWhere((file) => file.status == UploadFileStatus.pending);
    } catch (_) {
      return null;
    }
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  List<WorkerUploadFile> get nextBashFilesToCompress {
    final pendingFiles =
        files.where((file) => file.status == UploadFileStatus.pending).toList();
    return pendingFiles.sublist(
      0,
      pendingFiles.length >= uploadConcurrency
          ? uploadConcurrency
          : pendingFiles.length,
    );
  }

  factory WorkerCompressAndUploadImagesInput.fromJson(
    Map<String, dynamic> json,
  ) =>
      _$WorkerCompressAndUploadImagesInputFromJson(json);

  Map<String, dynamic> toJson() =>
      _$WorkerCompressAndUploadImagesInputToJson(this);

  @override
  WorkerCompressAndUploadImagesInput copyWith({
    String? uploadFilesEmbed,
    String? workspaceId,
    String? channelId,
    String? userId,
    String? ref,
    String? taskName,
    int? attachmentType,
    DateTime? creationTime,
    String? messageRef,
    int? uploadConcurrency,
    int? currentBash,
    String? messageId,
    bool? isRefTask,
  }) {
    return WorkerCompressAndUploadImagesInput(
      uploadFilesEmbed: uploadFilesEmbed ?? this.uploadFilesEmbed,
      workspaceId: workspaceId ?? this.workspaceId,
      channelId: channelId ?? this.channelId,
      userId: userId ?? this.userId,
      ref: ref ?? this.ref,
      taskName: taskName ?? this.taskName,
      attachmentType: attachmentType ?? this.attachmentType,
      creationTime: creationTime ?? this.creationTime,
      messageRef: messageRef ?? this.messageRef,
      uploadConcurrency: uploadConcurrency ?? this.uploadConcurrency,
      currentBash: currentBash ?? this.currentBash,
      messageId: messageId ?? this.messageId,
      isRefTask: isRefTask ?? this.isRefTask,
    );
  }
}
