import 'package:json_annotation/json_annotation.dart';

import 'worker_send_message_base.dart';

part 'worker_send_text_input.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class WorkerSendTextInput extends WorkerSendMessageInputBase {
  final String content;

  WorkerSendTextInput({
    required this.content,
    required super.workspaceId,
    required super.channelId,
    required super.userId,
    required super.ref,
    required super.taskName,
    required super.attachmentType,
    required super.creationTime,
    super.isRefTask,
  });

  factory WorkerSendTextInput.fromJson(Map<String, dynamic> json) =>
      _$WorkerSendTextInputFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$WorkerSendTextInputToJson(this);

  Map<String, dynamic> toRequest() {
    return isDm()
        ? {
            'userId': userId,
            'content': content,
            'ref': ref,
          }
        : {
            'workspaceId': workspaceId,
            'channelId': channelId,
            'content': content,
            'ref': ref,
          };
  }

  @override
  WorkerSendTextInput copyWith({
    String? content,
    String? workspaceId,
    String? channelId,
    String? userId,
    String? ref,
    String? taskName,
    int? attachmentType,
    DateTime? creationTime,
    bool? isRefTask,
  }) {
    return WorkerSendTextInput(
      content: content ?? this.content,
      workspaceId: workspaceId ?? this.workspaceId,
      channelId: channelId ?? this.channelId,
      userId: userId ?? this.userId,
      ref: ref ?? this.ref,
      taskName: taskName ?? this.taskName,
      attachmentType: attachmentType ?? this.attachmentType,
      creationTime: creationTime ?? this.creationTime,
      isRefTask: isRefTask ?? this.isRefTask,
    );
  }
}
