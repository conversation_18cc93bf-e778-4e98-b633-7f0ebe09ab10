import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
import 'package:shared/shared.dart';

import '../models/worker_media_object.dart';
import '../models/worker_upload_file.dart';
import 'worker_send_message_base.dart';

part 'worker_send_media_input.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class WorkerSendMediaInput extends WorkerSendMessageInputBase {
  WorkerSendMediaInput({
    required this.mediaListEmbed,
    required super.workspaceId,
    required super.channelId,
    required super.userId,
    required super.ref,
    required super.taskName,
    required super.attachmentType,
    required super.creationTime,
    super.isRefTask,
    this.uploadFilesEmbed = '',
    this.uploadConcurrency = 1,
    this.currentBash = 0,
    this.messageId,
  });

  final String mediaListEmbed;

  // Use for attachment list
  final String uploadFilesEmbed;
  final int uploadConcurrency;
  final int currentBash;
  final String? messageId;

  @JsonKey(includeFromJson: false, includeToJson: false)
  List<WorkerMediaObject> get mediaList =>
      (jsonDecode(mediaListEmbed) as List<dynamic>)
          .map((e) => WorkerMediaObject.fromJson(e as Map<String, dynamic>))
          .toList();

  @JsonKey(includeFromJson: false, includeToJson: false)
  List<WorkerUploadFile>? get uploadFiles => uploadFilesEmbed.isEmpty
      ? null
      : (jsonDecode(uploadFilesEmbed) as List<dynamic>)
          .map((e) => WorkerUploadFile.fromJson(e as Map<String, dynamic>))
          .toList();

  factory WorkerSendMediaInput.fromJson(Map<String, dynamic> json) =>
      _$WorkerSendMediaInputFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$WorkerSendMediaInputToJson(this);

  Map<String, dynamic> toRequest() {
    final map = isDm()
        ? {
            'userId': userId,
            'ref': ref,
            'attachmentType': attachmentType,
            'mediaObjects': mediaList.map((e) => e.toRequest()).toList(),
          }
        : {
            'workspaceId': workspaceId,
            'channelId': channelId,
            'ref': ref,
            'attachmentType': attachmentType,
            'mediaObjects': mediaList.map((e) => e.toRequest()).toList(),
          };
    if (!StringUtils.isNullOrEmpty(messageId)) {
      map['messageId'] = messageId;
    }
    return map;
  }

  @override
  WorkerSendMediaInput copyWith({
    String? mediaListEmbed,
    String? workspaceId,
    String? channelId,
    String? userId,
    String? ref,
    String? taskName,
    int? attachmentType,
    DateTime? creationTime,
    String? uploadFilesEmbed,
    int? uploadConcurrency,
    int? currentBash,
    String? messageId,
    bool? isRefTask,
  }) {
    return WorkerSendMediaInput(
      mediaListEmbed: mediaListEmbed ?? this.mediaListEmbed,
      workspaceId: workspaceId ?? this.workspaceId,
      channelId: channelId ?? this.channelId,
      userId: userId ?? this.userId,
      ref: ref ?? this.ref,
      taskName: taskName ?? this.taskName,
      attachmentType: attachmentType ?? this.attachmentType,
      creationTime: creationTime ?? this.creationTime,
      uploadFilesEmbed: uploadFilesEmbed ?? this.uploadFilesEmbed,
      uploadConcurrency: uploadConcurrency ?? this.uploadConcurrency,
      currentBash: currentBash ?? this.currentBash,
      messageId: messageId ?? this.messageId,
      isRefTask: isRefTask ?? this.isRefTask,
    );
  }
}
