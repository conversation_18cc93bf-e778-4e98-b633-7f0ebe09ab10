import 'dart:ui';

import 'package:chat/chat.dart';

import '../models/attachment_error.dart';
import 'worker_upload_file_output.dart';

class CompressAndUploadImagesOutput {
  const CompressAndUploadImagesOutput({
    required this.attachmentStatus,
    this.uploadOutput,
    this.imageSize,
    this.error,
  });

  factory CompressAndUploadImagesOutput.success({
    required WorkerUploadFileOutput uploadOutput,
    required Size imageSize,
  }) {
    return CompressAndUploadImagesOutput(
      attachmentStatus: AttachmentStatusEnum.SUCCESS,
      imageSize: imageSize,
      uploadOutput: uploadOutput,
    );
  }

  factory CompressAndUploadImagesOutput.error({
    required AttachmentError error,
  }) {
    return CompressAndUploadImagesOutput(
      attachmentStatus: AttachmentStatusEnum.FAILURE,
      error: error,
    );
  }
  final WorkerUploadFileOutput? uploadOutput;
  final Size? imageSize;
  final AttachmentError? error;
  final AttachmentStatusEnum attachmentStatus;
}
