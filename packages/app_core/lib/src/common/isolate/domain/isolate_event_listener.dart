import 'dart:async';

import 'package:flutter/foundation.dart';

import '../core/utils/logger.dart';
import 'models/task_model.dart';

enum IsolateEventType {
  taskRegistered,
  taskStarted,
  taskCompleted,
  taskFailed,
  taskCanceled,
  taskRetrying,
  taskError,
  taskTimeout,
  taskAlreadyInProgress,
  isolateStarted,
  isolateStopped,
  isolateUnresponsive,
  isolateRestarted,
  queueProcessing,
}

class IsolateEvent {
  final IsolateEventType type;
  final String taskId;
  final Map<String, dynamic> data;

  IsolateEvent({
    required this.type,
    required this.taskId,
    this.data = const {},
  });

  @override
  String toString() {
    return 'IsolateEvent{type: $type, taskId: $taskId, data: $data}';
  }
}

class IsolateEventListener {
  static final IsolateEventListener _instance =
      IsolateEventListener._internal();
  factory IsolateEventListener() => _instance;

  IsolateEventListener._internal();

  final _eventController = StreamController<IsolateEvent>.broadcast();

  final _taskStatusController =
      StreamController<Map<String, TaskStatus>>.broadcast();

  Stream<IsolateEvent> get events => _eventController.stream;

  Stream<Map<String, TaskStatus>> get taskStatusStream =>
      _taskStatusController.stream;

  final Map<String, TaskStatus> _taskStatusMap = {};

  final Map<TaskStatus, List<VoidCallback>> _statusListeners = {
    TaskStatus.pending: [],
    TaskStatus.running: [],
    TaskStatus.completed: [],
    TaskStatus.failed: [],
    TaskStatus.canceled: [],
  };

  void addStatusListener(TaskStatus status, VoidCallback listener) {
    _statusListeners[status]?.add(listener);
  }

  void removeStatusListener(TaskStatus status, VoidCallback listener) {
    _statusListeners[status]?.remove(listener);
  }

  TaskStatus? getTaskStatus(String taskId) {
    return _taskStatusMap[taskId];
  }

  List<String> getTasksWithStatus(TaskStatus status) {
    return _taskStatusMap.entries
        .where((entry) => entry.value == status)
        .map((entry) => entry.key)
        .toList();
  }

  void emitEvent(IsolateEvent event) {
    RILogger.printDebug('[IsolateEventListener] Emitting event: $event');

    // Check if the stream is closed before adding an event
    if (!_eventController.isClosed) {
      _eventController.add(event);
    } else {
      RILogger.printDebug(
        '[IsolateEventListener] Cannot emit event: Stream is closed',
      );
    }

    switch (event.type) {
      case IsolateEventType.taskRegistered:
        _updateTaskStatus(event.taskId, TaskStatus.pending);
        break;
      case IsolateEventType.taskStarted:
        _updateTaskStatus(event.taskId, TaskStatus.running);
        break;
      case IsolateEventType.taskCompleted:
        _updateTaskStatus(event.taskId, TaskStatus.completed);
        break;
      case IsolateEventType.taskFailed:
        _updateTaskStatus(event.taskId, TaskStatus.failed);
        break;
      case IsolateEventType.taskCanceled:
        _updateTaskStatus(event.taskId, TaskStatus.canceled);
        break;
      case IsolateEventType.taskRetrying:
        _updateTaskStatus(event.taskId, TaskStatus.pending);
        break;
      case IsolateEventType.taskTimeout:
        _updateTaskStatus(event.taskId, TaskStatus.failed);
        break;
      case IsolateEventType.taskAlreadyInProgress:
        // Task is already in progress, so we don't need to update its status
        // Just log the event for debugging
        RILogger.printClassMethodDebug(
          'IsolateEventListener',
          'emitEvent',
          'Task ${event.taskId} is already in progress, messageRef: ${event.data['messageRef']}',
        );
        break;
      default:
        break;
    }
  }

  void _updateTaskStatus(String taskId, TaskStatus status) {
    final oldStatus = _taskStatusMap[taskId];
    _taskStatusMap[taskId] = status;

    for (final listener in _statusListeners[status] ?? []) {
      listener();
    }

    // Check if the stream is closed before adding an event
    if (!_taskStatusController.isClosed) {
      _taskStatusController.add(Map.from(_taskStatusMap));
    } else {
      RILogger.printDebug(
        '[IsolateEventListener] Cannot update task status: Stream is closed',
      );
    }

    RILogger.printClassMethodDebug(
      'IsolateEventListener',
      '_updateTaskStatus',
      'Task $taskId status updated: $oldStatus -> $status',
    );
  }

  void updateFromTaskList(List<TaskModel> tasks) {
    bool hasChanges = false;

    for (final task in tasks) {
      final oldStatus = _taskStatusMap[task.id];
      if (oldStatus != task.status) {
        _taskStatusMap[task.id] = task.status;
        hasChanges = true;
      }
    }

    if (hasChanges) {
      // Check if the stream is closed before adding an event
      if (!_taskStatusController.isClosed) {
        _taskStatusController.add(Map.from(_taskStatusMap));
        RILogger.printClassMethodDebug(
          'IsolateEventListener',
          'updateFromTaskList',
          'Task status map updated from task list',
        );
      } else {
        RILogger.printDebug(
          '[IsolateEventListener] Cannot update from task list: Stream is closed',
        );
      }
    }
  }

  /// Clears the task status map and notifies listeners
  ///
  /// This method is useful when logging out or resetting the application state.
  /// It removes all task statuses from the map and notifies all listeners.
  void clearTaskStatusMap() {
    if (_taskStatusMap.isEmpty) return;

    _taskStatusMap.clear();

    // Check if the stream is closed before adding an event
    if (!_taskStatusController.isClosed) {
      _taskStatusController.add({});
    } else {
      RILogger.printDebug(
        '[IsolateEventListener] Cannot clear task status map: Stream is closed',
      );
    }

    RILogger.printClassMethodDebug(
      'IsolateEventListener',
      'clearTaskStatusMap',
      'Task status map cleared',
    );
  }

  void dispose() {
    _eventController.close();
    _taskStatusController.close();
  }
}
