import 'dart:convert';

import 'package:chat/chat.dart' hide Config, LoadChatUserInput;
import 'package:message_api/message_api.dart';
import 'package:search/search.dart' hide Config;
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../core.dart';
import '../common/di/di.dart';

class CoreHandlerUtils {
  static String isGhost = "Ghost";
  static String botID = "01GNNA1J000000000000000000";
  List<UserItem> usersItemPrivateData = [];
  List<Search> searchUsersPrivateData = [];
  List<InvitableUser> invitableUserPrivateData = [];
  late TypeObject typeObject;
  List<UserPrivateData> usersPrivateData = [];

  void setupUserPrivateData(
    CoreLoadChatUserUseCase _coreLoadChatUserUseCase, {
    required TypeObject typeObject,
  }) async {
    this.typeObject = typeObject;
    usersPrivateData =
        getIt<StreamUsersPrivateData>().usersPrivateData.toSet().toList();
    usersItemPrivateData.clear();
    searchUsersPrivateData.clear();
    usersPrivateData.forEach((item) async {
      final getUser = await _coreLoadChatUserUseCase
          .execute(LoadChatUserInput(userId: item.userId));
      if (typeObject == TypeObject.userItem) {
        usersItemPrivateData.add(
          UserItem(
            id: item.userId,
            name: item.aliasName ?? '',
            url: UrlUtils.parseAvatar(getUser.user?.profile?.originalAvatar),
            userId: item.userId,
            workspaceId: null,
            type: V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_DM.name,
          ),
        );
      }
      if (typeObject == TypeObject.search) {
        searchUsersPrivateData.add(
          Search.user(
            sessionKey: Config.getInstance().activeSessionKey ?? '',
            userId: item.userId,
            embed: jsonEncode(
              {
                'avatar': getUser.user?.profile?.originalAvatar,
                'aliasName': item.aliasName,
                'displayName': getUser.user?.profile?.displayName,
                'username': getUser.user?.username,
              },
            ),
          ),
        );
      }
      if (typeObject == TypeObject.invitation) {
        invitableUserPrivateData.add(
          InvitableUser(
            userId: item.userId,
            username: getUser.user?.username ?? '',
            avatar: getUser.user?.profile?.originalAvatar,
            aliasName: item.aliasName,
            displayName: getUser.user?.profile?.displayName,
          ),
        );
      }
    });
  }

  List<UserItem>? searchUserItemAliasName({
    required String keyword,
    required List<UserItem> listItemFromApi,
  }) {
    var filterAliasName = usersItemPrivateData
        .where(
          (item) => item.name.toLowerCase().contains(keyword.toLowerCase()),
        )
        .toList();

    listItemFromApi.removeWhere(
      (api) =>
          filterAliasName.any((aliasname) => aliasname.userId == api.userId),
    );
    listItemFromApi.insertAll(0, filterAliasName);
    return listItemFromApi;
  }

  List<Search>? searchUserSearchAliasName({
    required String keyword,
    required List<Search> listItemFromApi,
  }) {
    var filterAliasName = searchUsersPrivateData
        .where(
          (item) =>
              item.aliasName.toLowerCase().contains(keyword.toLowerCase()),
        )
        .toList();
    List<Search> listSearch = listItemFromApi
        .where(
          (api) =>
              filterAliasName.indexWhere((aliasname) {
                return aliasname.userId == api.userId;
              }) ==
              -1,
        )
        .toList();

    listSearch.insertAll(0, filterAliasName);
    return listSearch;
  }

  List<InvitableUser>? searchUserInvitationAliasName({
    required String keyword,
    required List<InvitableUser> listItemFromApi,
  }) {
    var filterAliasName = invitableUserPrivateData
        .where(
          (item) =>
              item.aliasName!.toLowerCase().contains(keyword.toLowerCase()),
        )
        .toList();
    /// userid will remove
    final excludedIds = {
      ...filterAliasName.map((e) => e.userId),
      GlobalConfig.ZIICHAT_USER_ID,
    };

    final listSearch = listItemFromApi
        .where((api) => !excludedIds.contains(api.userId))
        .toList();

    listSearch.insertAll(0, filterAliasName);
    return listSearch;
  }
}

enum TypeObject {
  userItem,
  search,
  invitation,
}
