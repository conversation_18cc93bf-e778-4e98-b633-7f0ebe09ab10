import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Class to manage queue of websocket events that haven't been sent
@lazySingleton
class WebSocketEventQueue {
  static const String PENDING_EVENTS_KEY = 'websocket_pending_events';

  final SharedPreferences _sharedPreferences;

  WebSocketEventQueue(this._sharedPreferences);

  /// Add an event to the queue
  Future<bool> enqueueEvent(Map<String, dynamic> event) async {
    try {
      // Get current list of events
      final List<String> pendingEvents = _getPendingEvents();

      // Add new event to the list
      pendingEvents.add(jsonEncode(event));

      // Save the list
      return await _sharedPreferences.setStringList(
        PENDING_EVENTS_KEY,
        pendingEvents,
      );
    } catch (e) {
      Log.e(name: 'WebSocketEventQueue', 'Error enqueueing event: $e');
      return false;
    }
  }

  /// Get all pending events
  List<Map<String, dynamic>> getAllPendingEvents() {
    try {
      final List<String> pendingEvents = _getPendingEvents();
      return pendingEvents
          .map((eventString) => jsonDecode(eventString) as Map<String, dynamic>)
          .toList();
    } catch (e) {
      Log.e(name: 'WebSocketEventQueue', 'Error getting pending events: $e');
      return [];
    }
  }

  /// Clear all pending events
  Future<bool> clearAllPendingEvents() async {
    try {
      return await _sharedPreferences.setStringList(PENDING_EVENTS_KEY, []);
    } catch (e) {
      Log.e(name: 'WebSocketEventQueue', 'Error clearing pending events: $e');
      return false;
    }
  }

  /// Get the list of pending events from SharedPreferences
  List<String> _getPendingEvents() {
    return _sharedPreferences.getStringList(PENDING_EVENTS_KEY) ?? [];
  }

  /// Check if there are any pending events
  bool hasPendingEvents() {
    return _getPendingEvents().isNotEmpty;
  }
}
