import 'dart:async';
import 'dart:convert';

import 'package:chat/chat.dart';
import 'package:chat/src/data/repositories/database/enums/chat_friend_status.dart';
import 'package:chat/src/domain/usecase/chat_user/upsert_chat_friends_use_case.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/src/domain/usecase/local_update_user_cover_use_case.dart';
import 'package:user_manager/user_manager.dart' hide Config;
import 'package:web_socket_channel/io.dart';

import '../../../core.dart' hide Config, LoadChatUserInput;
import '../../common/di/di.dart';
import '../../data/event/message_created_from_me.dart';
import '../../data/interface/sync_key_enum.dart';
import '../../serializer/includes_serializer.dart';
import '../usecase/check_user_use_case.dart';
import '../usecase/visited_profile/local_clear_visited_profile_use_case.dart';
import '../usecase/visited_profile/upsert_visited_profile_use_case.dart';

class WebSocketListener {
  static String TAG = 'WebSocketListener';
  final WebSocketManager _manager;
  late IOWebSocketChannel _channel;
  StreamSubscription? _subscription;
  late String _me = '';
  late String _meUserId = '';
  late String _source = '';
  late bool _resumed = false;
  late StreamSubscription _resumeSubscription;
  late WebSocketEventQueue _eventQueue;
  late List<EventType> _listTypeException = [
    EventType.PRIVATE_DATA_SYNC,
    EventType.UNBLOCK_USER,
    EventType.BLOCK_USER,
    EventType.USER_MESSAGE_DELETED,
    EventType.CLEAR_VISITED_PROFILE_NOTIFICATIONS,
    EventType.VISITED_PROFILE_DELETE,
    EventType.PIN_MESSAGE,
    EventType.UNPIN_MESSAGE,
    EventType.AVATAR_UPDATED,
  ];

  WebSocketListener(this._manager) {
    _channel = _manager.getChannel()!;
    _eventQueue = GetIt.instance.get<WebSocketEventQueue>();
    _listenToMessages();
  }

  void _listenToMessages() {
    try {
      _subscription = _channel.stream.listen(
        (message) {
          if (message is String) {
            handleTextMessage(message);
          }
        },
        onError: (error) {
          Log.ws(name: TAG, "Error received: $error");
          _manager.disconnect();
          dispose();
        },
        onDone: () {
          Log.ws(name: TAG, "WebSocket connection closed by server.");
          _manager.disconnect();
          dispose();
        },
      );
    } catch (e) {
      Log.ws(name: TAG, "Error setting up WebSocket listener: $e");
      _manager.disconnect();
    }
  }

  void handleTextMessage(String message) {
    CloudEvent cloudEvent = CloudEvent.fromJson(jsonDecode(message));

    Log.ws(
      name: TAG + ' - handleTextMessage: ',
      cloudEvent.toJson(),
    );

    _checkConnectedState(cloudEvent);

    _saveEventId(cloudEvent);
    if (cloudEvent.type == EventType.WEBSOCKET_RESUME_END) {
      _resumed = true;

      // After resume is complete, check and resend missed events
      _checkAndSendPendingEvents();

      // Publish event to notify that WebSocket resume is completed
      AppEventBus.publish(WebSocketResumeCompletedEvent());
    }

    if (_sourceFromMe(cloudEvent.source)) {
      // Allow processing of includesData of own messageCreated event to save created DM channel
      if (cloudEvent.type == EventType.MESSAGE_CREATED ||
          cloudEvent.type == EventType.MESSAGE_UPDATED) {
        _handleIncludesData(cloudEvent);
        AppEventBus.publish(MessageCreatedEvent(data: cloudEvent.toJson()));

        _handleMessageCreatedFromMe(cloudEvent);
      }
      if (_listTypeException.contains(cloudEvent.type)) {
        _handleCloudEvent(cloudEvent);
      }

      if (cloudEvent.type == EventType.INCOMING_FRIEND_REQUEST_ACCEPTED) {
        final data = WSResponse.fromJson(cloudEvent.toJson());
        final requestedFromUserId =
            data.data.friendRequest?.requestedFromUserId;
        GetIt.instance.get<UpdateDMStatusUseCase>().execute(
              UpdateDMStatusInput(userId: requestedFromUserId!),
            );
      }

      return;
    }

    _handleCloudEvent(cloudEvent);
  }

  Future<void> _handleMessageCreatedFromMe(CloudEvent cloudEvent) async {
    final WSResponse wsResponse = WSResponse.fromJson(cloudEvent.toJson());

    final Message? message = MessageSerializer.serializeFromWSResponse(
      wsResponse: wsResponse,
    );

    AppEventBus.publish(
      MessageCreatedFromMeEvent(
        data: {
          'messageId': message?.messageId,
          'ref': message?.ref,
        },
      ),
    );
  }

  void handleBinaryMessage(List<int> message) {
    Log.ws(name: TAG, "Binary message received: $message");
  }

  void dispose() {
    _subscription?.cancel();
  }

  void _checkConnectedState(CloudEvent cloudEvent) {
    if (cloudEvent.type != EventType.GATEWAY_CONNECTED) return;
    _source = cloudEvent.source;
    _me = _source.substring(_source.indexOf("?userId") + 1);
    _meUserId = Uri.parse(_source).queryParameters['userId'] ?? '';
    final deviceId = Uri.parse(_source).queryParameters['deviceId'] ?? '';

    _manager.onConnected();

    _manager.source = _source;
    _manager.myUserId = _meUserId;
    _manager.deviceId = deviceId;
    _manager.me = _me;

    _sendEventConnected();
    _manager.sendPendingEvents();

    _listenResumeWebsocketEvent();
  }

  bool _sourceFromMe(String source) {
    return _me.contains(source.substring(source.indexOf("?userId") + 1));
  }

  void _saveEventId(CloudEvent cloudEvent) {
    if (!ULIDUtils.isValidUlid(cloudEvent.id)) return;
    AppEventBus.publish(
      ResumeIdUpdated(
        id: cloudEvent.id,
        source: cloudEvent.source,
      ),
    );
  }

  void _sendEventConnected() {
    AppEventBus.publish(OnWebsocketConnected(data: {}));
  }

  void _listenResumeWebsocketEvent() {
    _resumeSubscription =
        GetIt.instance.get<AppEventBus>().on<ResumeWebsocket>().listen((event) {
      if (event.data == null) {
        _resumed = true;
        _resumeSubscription.cancel();
        return;
      }

      _manager.sendMessage(
        CloudEvent.create(
          _source,
          EventType.WEBSOCKET_RESUME,
          {
            'token': event.data as String,
          },
        ).toJson(),
      );
      _resumed = false;
      _resumeSubscription.cancel();
    });
  }

  void _handleCloudEvent(CloudEvent cloudEvent) {
    _handleIncludesData(cloudEvent);
    switch (cloudEvent.type) {
      case EventType.MESSAGE_CREATED:
        AppEventBus.publish(
          MessageCreatedEvent(
            data: cloudEvent.toJson(),
          ),
        );
        break;
      case EventType.MESSAGE_UPDATED:
        _handleUpdateMessage(cloudEvent);
        break;
      case EventType.PIN_MESSAGE:
        _handlePinUnpinUpdateMessage(cloudEvent);
        break;
      case EventType.UNPIN_MESSAGE:
        _handlePinUnpinUpdateMessage(cloudEvent);
        break;
      case EventType.CHANNEL_CREATED:
        AppEventBus.publish(
          ChannelCreatedEvent(
            data: cloudEvent.toJson(),
          ),
        );
        break;
      case EventType.CHANNEL_UPDATED:
        AppEventBus.publish(
          ChannelUpdatedEvent(
            data: cloudEvent.toJson(),
          ),
        );
        break;
      case EventType.CHANNEL_DELETED:
        AppEventBus.publish(
          ChannelDeletedEvent(
            data: cloudEvent.toJson(),
          ),
        );
        break;
      case EventType.INCOMING_MESSAGE_REQUEST_ACCEPTED:
        _handleMessageRequestAccept(cloudEvent);
        break;
      case EventType.MESSAGE_REQUEST_REJECTED:
        AppEventBus.publish(
          ChannelDeletedEvent(
            data: cloudEvent.toJson(),
          ),
        );
        break;
      case EventType.ALL_USER_MESSAGE_DELETED:
        _handleDeleteAllMessage(cloudEvent);
        break;
      case EventType.UNREAD_MESSAGE_UPDATED:
        _handleUnreadMessagesUpdated(cloudEvent);
        break;
      case EventType.PRIVATE_DATA_SYNC:
        _handleSyncPrivateData(cloudEvent);
        break;
      case EventType.MESSAGES_DELETE_ALL:
        _handleDeleteAllMessage(cloudEvent);
        break;
      case EventType.MESSAGE_REACTION_UPDATED:
        _handleMessageReactionUpdated(cloudEvent);
        break;
      case EventType.MESSAGE_USER_REACTION_UPDATED:
        _handleMessageUserReactionUpdated(cloudEvent);
        break;
      case EventType.FRIEND_UNFRIENDED:
        _handleUnfriendEvent(cloudEvent);
        break;
      case EventType.OUTGOING_FRIEND_REQUEST_ACCEPTED:
        _handleFriendRequestAcceptedEvent(cloudEvent);
        break;
      case EventType.INCOMING_FRIEND_REQUEST_CANCELED:
        AppEventBus.publish(FriendRequestCancelEvent());
        _handleFriendRequestCanceledEvent(cloudEvent);
        break;
      case EventType.INCOMING_FRIEND_REQUEST_CREATED:
        _handleUpsertFriendRequest(cloudEvent);
      case EventType.OUTGOING_FRIEND_REQUEST_CREATED:
        _handleUpsertFriendRequest(cloudEvent);
      case EventType.OUTGOING_FRIEND_REQUEST_CANCELED:
        _handleUpsertFriendRequest(cloudEvent);
      case EventType.INCOMING_FRIEND_REQUEST_ACCEPTED:
        _handleUpsertFriendRequest(cloudEvent);
        AppEventBus.publish(FriendRequestAcceptEvent());
      case EventType.INCOMING_FRIEND_REQUEST_DELETED:
        _handleUpsertFriendRequest(cloudEvent);
      case EventType.AVATAR_UPDATED:
      case EventType.AVATAR_DELETED:
        _handleAvatarUpdated(cloudEvent);
        break;
      case EventType.USER_COVER_CREATED:
      case EventType.USER_COVER_UPDATED:
      case EventType.USER_COVER_DELETED:
        _handleCoverUpdated(cloudEvent);
        break;
      case EventType.MEMBER_ROLE_UPDATED:
        _handleUpdateMemberRole(cloudEvent);
        break;
      case EventType.MEMBER_ROLE_REVOKED:
        _handleUpdateMemberRole(cloudEvent, isUpdate: false);
        break;
      case EventType.MEMBER_NICKNAME_UPDATED:
        _handleUpdateMemberNickName(cloudEvent);
        break;
      case EventType.MEMBER_REMOVED:
        _handleRemoveFromChannel(cloudEvent);
        break;
      case EventType.MEMBER_LEFT:
        _handleMemberLeftChannel(cloudEvent);
        break;
      case EventType.BLOCK_USER:
        _blockEvent(cloudEvent);
        break;
      case EventType.UNBLOCK_USER:
        _unBlockEvent(cloudEvent);
        break;
      case EventType.USER_MESSAGE_DELETED:
        _handleDeleteMessage(cloudEvent);
        break;
      case EventType.MESSAGE_DELETED:
        _handleDeleteMessage(cloudEvent);
        break;
      case EventType.VISITED_PROFILE:
        _handleVisitedProfile(cloudEvent);
        break;
      case EventType.VISITED_PROFILE_DELETE:
        _handleDeleteVisitedProfile(cloudEvent);
        break;
      case EventType.CLEAR_VISITED_PROFILE_NOTIFICATIONS:
        _handleClearVisitedProfileNotification(cloudEvent);
        break;
      // Handle User status events
      case EventType.USER_STATUS_CREATED ||
            EventType.USER_STATUS_UPDATED ||
            EventType.USER_STATUS_DELETED:
        _handleUpsertUserStatus(cloudEvent);
        break;
      case EventType.CHANNEL_TYPING:
        _handleChannelTyping(cloudEvent);
        break;
      case EventType.DISPLAY_NAME_UPDATED:
        _handleDisplayNameUpdated(cloudEvent);
        break;
      case EventType.MEETING_ROOM_STARTED:
        _handleMeetingRoomStarted(cloudEvent);
        break;
      case EventType.MEETING_ROOM_ENDED:
        _handleMeetingRoomEnded(cloudEvent);
        break;
      case EventType.DELETED_USER:
        AppEventBus.publish(OnTokenInvalid());
        break;
      default:
        AppEventBus.publish(cloudEvent);
    }
  }

  Future<void> _handleAvatarUpdated(CloudEvent cloudEvent) async {
    var data = WSResponse.fromJson(cloudEvent.toJson());
    final actorId = data.data.actorId;
    final avatar = data.data.avatar;
    await GetIt.instance.get<CoreLocalUpdateUserAvatarUseCase>().execute(
          CoreLocalUpdateUserAvatarInput(
            userId: actorId!,
            avatarPath: avatar ?? '',
          ),
        );
  }

  Future<void> _handleCoverUpdated(CloudEvent cloudEvent) async {
    var data = WSResponse.fromJson(cloudEvent.toJson());
    final userId = data.data.userId;
    final cover = data.data.cover;
    await GetIt.instance.get<LocalUpdateUserCoverUseCase>().execute(
          LocalUpdateUserCoverInput(userId: userId!, coverPath: cover ?? ''),
        );
  }

  Future<
      (
        List<User>,
        List<Channel>,
        List<Message>,
        List<Member>,
        List<Friend>
      )?> _handleIncludesData(
    CloudEvent cloudEvent,
  ) async {
    if (cloudEvent.type == EventType.WEBSOCKET_RESUME_END) return null;

    final data = WSResponse.fromJson(cloudEvent.toJson());

    if (!data.data.hasIncludes) return null;

    final output = await GetIt.instance
        .get<HandleIncludesDataUseCase>()
        .execute(HandleIncludesDataInput(includes: data.data.includes!));
    return output.all;
  }

  Future<void> _handleDeleteAllMessage(CloudEvent cloudEvent) async {
    final data = WSResponse.fromJson(cloudEvent.toJson());
    final channelId = data.data.channelId!;
    final workspaceId = data.data.workspaceId!;
    await GetIt.instance.get<DeleteAllMessagesUseCase>().execute(
          DeleteAllMessagesInput(
            channelId: channelId,
            workspaceId: workspaceId,
          ),
        );
    AppEventBus.publish(
      ClearMessageEvent(
        workspaceId: workspaceId,
        channelId: channelId,
      ),
    );
  }

  Future<void> _handleUnreadMessagesUpdated(CloudEvent cloudEvent) async {
    final data = WSResponse.fromJson(cloudEvent.toJson());
    final channelId = data.data.channelId!;
    final workspaceId = data.data.workspaceId!;
    final lastSeenMessageId = data.data.lastSeenMessageId!;
    await GetIt.instance.get<UpdateLastSeenMessageUseCase>().execute(
          UpdateLastSeenMessageInput(
            channelId: channelId,
            workspaceId: workspaceId,
            lastSeenMessageId: lastSeenMessageId,
          ),
        );
  }

  void _handleSyncPrivateData(CloudEvent cloudEvent) async {
    if (cloudEvent.data["ok"] == false) {
      if (cloudEvent.data['data']['key'].toString() == SyncKeyEnum.users.key) {
        AppEventBus.publish(
          RetrySetAliasNameEvent(
            data: cloudEvent.data["data"]["value"] ?? {},
          ),
        );
      } else {
        AppEventBus.publish(
          RetryPinChannelEvent(
            data: cloudEvent.data["data"]["value"] ?? {},
          ),
        );
      }
    } else {
      final dataKey = cloudEvent.data['key'].toString();
      if (dataKey == SyncKeyEnum.users.key) {
        if (cloudEvent.data["value"] != null) {
          AppEventBus.publish(
            InsertAliasNameEvent(
              data: cloudEvent.data["value"] ?? {},
            ),
          );
        }
        AppEventBus.publish(SetAliasNameSuccessEvent(success: true));
      }
      if (dataKey == SyncKeyEnum.channels.key) {
        if (cloudEvent.data["value"] != null) {
          AppEventBus.publish(
            UpdatePinChannelEvent(
              data: cloudEvent.data["value"] ?? {},
            ),
          );
        }
        AppEventBus.publish(AddPinChannelSuccessEvent(success: true));
      }
      if (dataKey == SyncKeyEnum.callLogs.key) {
        _handleSyncCallLog(cloudEvent);
      }
    }
  }

  void _handleSyncCallLog(CloudEvent cloudEvent) {
    if (cloudEvent.data["value"] == null) return;
    cloudEvent.data["value"]["sessionKey"] = _meUserId;
    final callLog = CallLogPrivateData.fromJson(cloudEvent.data["value"]);
    if (callLog.version < 0) {
      GetIt.instance
          .get<DeleteCallLogUseCase>()
          .execute(DeleteCallLogInput(callId: callLog.callId));
      return;
    }
    GetIt.instance
        .get<CreateCallLogUseCase>()
        .execute(CreateCallLogInput(callLog: callLog));
  }

  void _handleUpdateMemberRole(CloudEvent cloudEvent, {bool isUpdate = true}) {
    final data = WSResponse.fromJson(cloudEvent.toJson());
    final role = data.data.role!;
    final workspaceId = data.data.workspaceId!;
    final channelId = data.data.channelId!;
    final userId = data.data.targetUserId!;
    GetIt.instance.get<UpdateMemberRoleUseCase>().execute(
          UpdateMemberRoleInput(
            channelId: channelId,
            workspaceId: workspaceId,
            userId: userId,
            role: role,
            type: isUpdate ? UpdateType.add : UpdateType.revoke,
          ),
        );
  }

  void _handleUpdateMemberNickName(CloudEvent cloudEvent) {
    final data = WSResponse.fromJson(cloudEvent.toJson());
    final workspaceId = data.data.workspaceId!;
    final channelId = data.data.channelId!;
    final userId = data.data.targetUserId!;
    final nickname = data.data.nickname!;
    GetIt.instance.get<UpdateLocalNickNameUseCase>().execute(
          UpdateLocalNickNameInput(
            workspaceId: workspaceId,
            channelId: channelId,
            userId: userId,
            nickname: nickname,
          ),
        );
  }

  void _handleRemoveFromChannel(CloudEvent cloudEvent) {
    final data = WSResponse.fromJson(cloudEvent.toJson());
    final workspaceId = data.data.workspaceId!;
    final channelId = data.data.channelId!;
    final userId = data.data.targetUserId!;
    if (userId == _meUserId) {
      GetIt.instance.get<RemoveChannelUseCase>().execute(
            RemoveChannelInput(workspaceId: workspaceId, channelId: channelId),
          );
      AppEventBus.publish(
        ChannelDeletedEvent(
          data: cloudEvent.toJson(),
        ),
      );
    } else {
      GetIt.instance.get<RemoveMemberLocalFromChannelUseCase>().execute(
            RemoveMemberLocalFromChannelInput(
              workspaceId: workspaceId,
              channelId: channelId,
              userId: userId,
            ),
          );
    }
  }

  void _handleMemberLeftChannel(CloudEvent cloudEvent) {
    final data = WSResponse.fromJson(cloudEvent.toJson());
    final workspaceId = data.data.workspaceId!;
    final channelId = data.data.channelId!;
    final userId = data.data.userId!;
    if (userId == _meUserId) {
      GetIt.instance.get<RemoveChannelUseCase>().execute(
            RemoveChannelInput(workspaceId: workspaceId, channelId: channelId),
          );
      AppEventBus.publish(
        ChannelDeletedEvent(
          data: cloudEvent.toJson(),
        ),
      );
    } else {
      GetIt.instance.get<RemoveMemberLocalFromChannelUseCase>().execute(
            RemoveMemberLocalFromChannelInput(
              workspaceId: workspaceId,
              channelId: channelId,
              userId: userId,
            ),
          );
    }
  }

  Future<void> _handleFriendRequestEvent(
    CloudEvent cloudEvent,
    String userIdKey,
  ) async {
    final data = WSResponse.fromJson(cloudEvent.toJson());
    String? targetUserId = userIdKey == 'requestedFromUserId'
        ? data.data.friendRequest?.requestedFromUserId
        : data.data.friendRequest?.requestedToUserId;
    if (_resumed == false) {
      final actorOutput =
          await GetIt.instance.get<GetChatUserUseCase>().execute(
                GetChatUserInput(userId: targetUserId!),
              );
      _updateChatUser(actorOutput.user);
      _updateUserFromChatUser(actorOutput.user);
      return;
    } else {
      List<ChatUser> users =
          IncludesSerializer(data.data.includes!).getChatUsers();
      final friend =
          ChatFriendSerializer.serializeFromWSResponse(wsResponse: data);

      ChatUser? user = users.firstWhere(
        (userInclude) => userInclude.userId == targetUserId,
      );
      user
        ..chatFriendDataRaw = jsonEncode(
          ChatFriendData(
            status: ChatFriendStatusEnumExtension.getEnumByValue(
              data.data.friendRequest?.status,
            ),
            createTime: data.data.friendRequest?.createTime,
          ).toJson(),
        );

      _updateChatUser(user);
      _updateUserFromChatUser(user);
      if (ChatFriendStatusEnumExtension.getEnumByValue(friend?.status) !=
          ChatFriendStatusEnum.NOT_FRIEND) {
        _updateChatFriend(friend);
      } else {
        _deleteChatFriend(data.data.friendRequest!.requestedFromUserId!);
      }

      if (userIdKey == 'requestedToUserId') {
        GetIt.instance.get<UpdateDMStatusUseCase>().execute(
              UpdateDMStatusInput(userId: targetUserId!),
            );
      }
    }
  }

  Future<void> _handleFriendRequestCanceledEvent(CloudEvent cloudEvent) async {
    await _handleFriendRequestEvent(cloudEvent, 'requestedFromUserId');
  }

  Future<void> _handleFriendRequestAcceptedEvent(CloudEvent cloudEvent) async {
    await _handleFriendRequestEvent(cloudEvent, 'requestedToUserId');
  }

  Future<void> _handleUnfriendEvent(CloudEvent cloudEvent) async {
    final data = WSResponse.fromJson(cloudEvent.toJson());

    final actorId = data.data.actorId;
    final targetUserId = data.data.targetUserId;
    var isMe = actorId != Config.getInstance().activeSessionKey;

    AppEventBus.publish(UnFriendEvent(userId: isMe ? actorId! : targetUserId!));

    var actorOutput;
    var targetOutput;
    if (_resumed == false) {
      actorOutput = await GetIt.instance.get<GetChatUserUseCase>().execute(
            GetChatUserInput(userId: actorId!),
          );

      targetOutput = await GetIt.instance.get<GetChatUserUseCase>().execute(
            GetChatUserInput(userId: targetUserId!),
          );
    } else {
      actorOutput = await GetIt.instance.get<LoadChatUserUseCase>().execute(
            LoadChatUserInput(userId: actorId!),
          );

      targetOutput = await GetIt.instance.get<LoadChatUserUseCase>().execute(
            LoadChatUserInput(userId: targetUserId!),
          );
      actorOutput.user
        ?..chatFriendDataRaw = jsonEncode(
          ChatFriendData(
            status: ChatFriendStatusEnum.NOT_FRIEND,
          ).toJson(),
        );

      targetOutput.user
        ?..chatFriendDataRaw = jsonEncode(
          ChatFriendData(
            status: ChatFriendStatusEnum.NOT_FRIEND,
          ).toJson(),
        );
    }

    final actor = actorOutput.user;
    final target = targetOutput.user;
    await _updateChatUser(isMe ? actor : target);
    await _updateUserFromChatUser(isMe ? actor : target);
    if (actor?.friendData?.status == ChatFriendStatusEnum.NOT_FRIEND ||
        target?.friendData?.status == ChatFriendStatusEnum.NOT_FRIEND) {
      await _deleteChatFriend(isMe ? actorId : targetUserId);
    }
  }

  Future<void> _updateChatUser(ChatUser? user) async {
    await GetIt.instance.get<UpsertChatUserUseCase>().execute(
          UpsertChatUserInput(user: user!),
        );
  }

  Future<void> _updateUserFromChatUser(ChatUser? chatUser) async {
    await GetIt.instance.get<ConvertChatUserAndUpsertUserUseCase>().execute(
          ConvertChatUserAndUpsertUserInput(chatUser: chatUser!),
        );
  }

  Future<void> _updateChatFriend(ChatFriend? friend) async {
    await GetIt.instance.get<UpsertChatFriendsUseCase>().execute(
          UpsertChatFriendsInput(
            friends: [friend!],
          ),
        );
  }

  Future<void> _deleteChatFriend(String userId) async {
    await GetIt.instance.get<DeleteChatFriendByUserId>().execute(
          DeleteChatFriendByUserIdInput(
            userId: userId,
          ),
        );
  }

  Future<void> _handleMessageUserReactionUpdated(CloudEvent cloudEvent) async {
    final data = WSResponse.fromJson(cloudEvent.toJson());
    final reactionsRaw = jsonEncode(data.data.reactions);
    final channelId = data.data.channelId!;
    final workspaceId = data.data.workspaceId!;
    final messageId = data.data.messageId!;

    final output =
        await GetIt.instance.get<UpdateUserReactionUseCase>().execute(
              UpdateUserReactionInput(
                workspaceId: workspaceId,
                channelId: channelId,
                messageId: messageId,
                reactionsRaw: reactionsRaw,
              ),
            );
    if (output.message != null) {
      AppEventBus.publish(UpdateMessageEvent(message: output.message!));
    }
  }

  Future<void> _handleMessageReactionUpdated(CloudEvent cloudEvent) async {
    final data = WSResponse.fromJson(cloudEvent.toJson());
    final channelId = data.data.channelId!;
    final workspaceId = data.data.workspaceId!;
    final messageId = data.data.messageId!;

    final output = await GetIt.instance.get<UpdateReactionUseCase>().execute(
          UpdateReactionInput(
            workspaceId: workspaceId,
            channelId: channelId,
            messageId: messageId,
            reactions: (data.data.reactions as List<dynamic>?)
                    ?.map((e) => e as Map<String, dynamic>)
                    .toList() ??
                [],
          ),
        );
    if (output.message != null) {
      AppEventBus.publish(UpdateMessageEvent(message: output.message!));
    }
  }

  Future<void> _blockEvent(CloudEvent cloudEvent) async {
    var getUser = await GetIt.instance.get<CheckUserUseCase>().execute(
          CheckUserInput(
            userId: cloudEvent.data['targetUserId'] ==
                    Config.getInstance().activeSessionKey
                ? cloudEvent.data['actorId']
                : cloudEvent.data['targetUserId'],
          ),
        );
    await GetIt.instance.get<LocalBlockUpdateUseCase>().execute(
          LocalBlockUpdateInput(userId: getUser.user!.userId, isBlocked: true),
        );

    AppEventBus.publish(
      BlockEvent(user: getUser.user?.toJson() ?? {}),
    );
  }

  Future<void> _unBlockEvent(CloudEvent cloudEvent) async {
    await GetIt.instance.get<LocalBlockUpdateUseCase>().execute(
          LocalBlockUpdateInput(
            userId: cloudEvent.data['targetUserId'] ==
                    Config.getInstance().activeSessionKey
                ? cloudEvent.data['actorId']
                : cloudEvent.data['targetUserId'],
            isBlocked: false,
          ),
        );
    AppEventBus.publish(
      UnBlockEvent(userId: cloudEvent.data['targetUserId']),
    );
  }

  Future<void> _handleDeleteMessage(CloudEvent cloudEvent) async {
    final data = WSResponse.fromJson(cloudEvent.toJson());
    final channelId = data.data.channelId!;
    final workspaceId = data.data.workspaceId!;
    final List<String> messageIds = data.data.messageIds!;
    var output = await GetIt.instance.get<LocalDeleteMessagesUseCase>().execute(
          LocalDeleteMessagesInput(
            channelId: channelId,
            workspaceId: workspaceId,
            messageIds: messageIds,
          ),
        );

    _handelDeleteOriginal(
      workspaceId: workspaceId,
      channelId: channelId,
      messageIds: messageIds,
    );
    if (output.ok == true) {
      AppEventBus.publish(
        DeleteMessageEvent(
          workspaceId: workspaceId,
          channelId: channelId,
          messageIds: messageIds,
        ),
      );
      AppEventBus.publish(
        ClosePopupQuoteEvent(messageIds: messageIds),
      );
    }
  }

  Future<void> _handelDeleteOriginal({
    required String workspaceId,
    required String channelId,
    required List<String> messageIds,
  }) async {
    for (final messageId in messageIds) {
      final output =
          await GetIt.instance.get<DeleteOriginalMessageUseCase>().execute(
                DeleteOriginalMessageInput(
                  workspaceId: workspaceId,
                  channelId: channelId,
                  messageId: messageId,
                ),
              );
      for (final message in output.messages) {
        AppEventBus.publish(UpdateMessageEvent(message: message));
      }
    }
  }

  Future<void> _handleVisitedProfile(CloudEvent cloudEvent) async {
    final data = WSResponse.fromJson(cloudEvent.toJson());
    var userDataJson = data.data.userData?.toJson();
    userDataJson?['sessionKey'] = Config.getInstance().activeSessionKey ?? '';
    var user = User.fromJson(userDataJson!);
    GetIt.instance
        .get<RenderUserUseCase>()
        .execute(RenderUserInput(userId: user.userId));
    var visitedProfile = VisitedProfile(
      sessionKey: user.sessionKey,
      userId: user.userId,
      isRead: false,
      createTime: data.data.createTime,
      updateTime: data.data.updateTime,
    );
    await GetIt.instance
        .get<UpsertVisitedProfileUseCase>()
        .execute(UpsertVisitedProfileUseCaseInput(visitedProfile));
    AppEventBus.publish(HasNotificationEvent(hasNotification: true));
  }

  Future<void> _handleClearVisitedProfileNotification(
    CloudEvent cloudEvent,
  ) async {
    await GetIt.instance
        .get<LocalClearVisitedProfileUseCase>()
        .execute(LocalClearVisitedProfileUseCaseInput(isAllRead: true));
    AppEventBus.publish(HasNotificationEvent(hasNotification: false));
  }

  Future<void> _handleDeleteVisitedProfile(CloudEvent cloudEvent) async {
    final data = WSResponse.fromJson(cloudEvent.toJson());
    await GetIt.instance
        .get<LocalDeleteVisitedProfileUseCase>()
        .execute(LocalDeleteVisitedProfileUseCaseInput(data.data.userId!));
  }

  Future<void> _handleUpsertUserStatus(CloudEvent cloudEvent) async {
    final data = WSResponse.fromJson(cloudEvent.toJson());
    final userStatus = data.data.statusData == null
        ? null
        : UserStatus.fromJson(data.data.statusData!.toJson());

    getIt<UserStatusHandler>().saveUserStatus(
      content: userStatus?.content,
      emoji: userStatus?.status,
      keepStatusDuration: userStatus?.expireAfterTime?.value,
      createTime: userStatus?.createTime,
      updateTime: userStatus?.updateTime,
      endTime: userStatus?.endTime,
    );
  }

  Future<void> _handleUpsertFriendRequest(CloudEvent cloudEvent) async {
    final data = WSResponse.fromJson(cloudEvent.toJson());
    List<ChatUser> users =
        IncludesSerializer(data.data.includes!).getChatUsers();
    final friend =
        ChatFriendSerializer.serializeFromWSResponse(wsResponse: data);
    ChatUser? user = users.firstWhere(
      (userInclude) =>
          userInclude.userId != Config.getInstance().activeSessionKey,
    );
    if (_resumed == false) {
      final actorOutput =
          await GetIt.instance.get<GetChatUserUseCase>().execute(
                GetChatUserInput(userId: user.userId),
              );
      await _updateChatUser(actorOutput.user);
      await _updateUserFromChatUser(actorOutput.user);
      return;
    } else {
      user
        ..chatFriendDataRaw = jsonEncode(
          ChatFriendData(
            status: ChatFriendStatusEnumExtension.getEnumByValue(
              data.data.friendRequest?.status,
            ),
            createTime: data.data.friendRequest?.createTime,
          ).toJson(),
        );
      await _updateChatUser(user);
      await _updateUserFromChatUser(user);
      await _updateChatFriend(friend);

      if (cloudEvent.type == EventType.INCOMING_FRIEND_REQUEST_ACCEPTED) {
        _handleUpdateDMStatus(user.userId);
      }
    }
  }

  void _handleUpdateDMStatus(String userId) {
    GetIt.instance.get<UpdateDMStatusUseCase>().execute(
          UpdateDMStatusInput(userId: userId),
        );
  }

  Future<void> _handleUpdateMessage(CloudEvent cloudEvent) async {
    AppEventBus.publish(
      MessageCreatedEvent(
        data: cloudEvent.toJson(),
      ),
    );
    final data = WSResponse.fromJson(cloudEvent.toJson());
    Message message = MessageSerializer.serializeFromWSResponse(
      wsResponse: data,
    )!;
    await GetIt.instance.get<UpdateMessagesUseCase>().execute(
          UpdateMessagesInput(messages: [message]),
        );
  }

  Future<void> _handlePinUnpinUpdateMessage(CloudEvent cloudEvent) async {
    final data = WSResponse.fromJson(cloudEvent.toJson());
    Message message = MessageSerializer.serializeFromWSResponse(
      wsResponse: data,
    )!;
    AppEventBus.publish(
      PinUnPinMessageUpdateEvent(
        workspaceId: message.workspaceId,
        channelId: message.channelId,
        messageId: message.messageId,
        isPinned: message.isPinned,
        pinTime: message.pinTime,
      ),
    );
    await GetIt.instance.get<UpsertPinUnPinMessageUseCase>().execute(
          UpsertPinUnPinMessageInput(
            workspaceId: message.workspaceId,
            channelId: message.channelId,
            messageId: message.messageId,
            status: message.isPinned,
            pinTime: message.pinTime,
          ),
        );
  }

  void _handleChannelTyping(CloudEvent cloudEvent) {
    GetIt.instance.get<TypingHandler>().handleTypingEvent(cloudEvent.data);
  }

  void _handleDisplayNameUpdated(CloudEvent cloudEvent) {
    final data = WSResponse.fromJson(cloudEvent.toJson());
    var userId = data.data.actorId;
    if (userId == null || userId.isEmpty) return;
    userId == Config.getInstance().activeSessionKey
        ? GetIt.instance.get<RenderMeUseCase>().execute(RenderMeInput())
        : GetIt.instance
            .get<RenderUserUseCase>()
            .execute(RenderUserInput(userId: userId));
  }

  /// Check and resend pending events in the queue
  Future<void> _checkAndSendPendingEvents() async {
    if (!_eventQueue.hasPendingEvents()) return;

    // Wait a short time to ensure the connection is stable
    await Future.delayed(const Duration(milliseconds: 500));

    final pendingEvents = _eventQueue.getAllPendingEvents();
    await _eventQueue.clearAllPendingEvents();

    // Resend each event
    for (final event in pendingEvents) {
      if (_manager.isConnected) {
        _manager.sendMessage(event);
        // Add a small delay between sends to avoid overload
        await Future.delayed(const Duration(milliseconds: 100));
      } else {
        // If connection is lost during resending, save the remaining events
        await _eventQueue.enqueueEvent(event);
      }
    }
  }

  void _handleMessageRequestAccept(CloudEvent cloudEvent) {
    final data = WSResponse.fromJson(cloudEvent.toJson());
    if (data.data.channel == null) return;

    final channel = ChannelSerializer.serializeFromJson(
      data: data.data.channel!.toJson(),
      metadata: data.data.includes?.channel?.channelMetadata?.toJson(),
      includes: data.data.includes?.toJson(),
    );
    if (channel == null) return;
    GetIt.instance
        .get<InsertChannelUseCase>()
        .execute(InsertChannelInput(channel: channel));
  }

  /// Handle meeting room started event
  Future<void> _handleMeetingRoomStarted(CloudEvent cloudEvent) async {
    final data = WSResponse.fromJson(cloudEvent.toJson());
    final channelId = data.data.channelId!;
    final workspaceId = data.data.workspaceId!;
    final output = await getIt<GetMeetingRoomUseCase>().execute(
      GetMeetingRoomInput(
        workspaceId: workspaceId,
        channelId: channelId,
      ),
    );
    if (output.hasRoom) {
      AppEventBus.publish(
        ShowMinimizedCallGroupEvent(
          channelId: channelId,
          workspaceId: workspaceId,
          numberParticipants: output.numParticipants,
        ),
      );
    }
  }

  /// Handle meeting room ended event
  void _handleMeetingRoomEnded(CloudEvent cloudEvent) {
    final data = WSResponse.fromJson(cloudEvent.toJson());
    AppEventBus.publish(
      HideMinimizedCallGroupEvent(
        channelId: data.data.channelId!,
        workspaceId: data.data.workspaceId!,
      ),
    );
  }
}
