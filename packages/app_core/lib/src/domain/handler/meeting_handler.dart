import 'package:call/call.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:livekit_client/livekit_client.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

class InOtherRoomException implements Exception {}

class MeetingRoomInfo {
  MeetingRoomInfo({
    required this.workspaceId,
    required this.numberParticipants,
    required this.channelId,
    required this.channelName,
    this.duration = Duration.zero,
  });

  final String channelId;
  final String workspaceId;
  final String channelName;
  final int numberParticipants;
  final Duration duration;

  MeetingRoomInfo copyWith({
    String? channelId,
    String? workspaceId,
    String? channelName,
    int? numberParticipant,
    Duration? duration,
  }) {
    return MeetingRoomInfo(
      workspaceId: workspaceId ?? this.workspaceId,
      numberParticipants: numberParticipant ?? this.numberParticipants,
      channelId: channelId ?? this.channelId,
      channelName: channelName ?? this.channelName,
      duration: duration ?? this.duration,
    );
  }
}

@LazySingleton()
class MeetingHandler {
  MeetingHandler(
    this._roomBloc,
    this._connectRoomUseCase,
    this._createLiveKitTokenUseCase,
  );

  final RoomBloc _roomBloc;
  final ConnectRoomUseCase _connectRoomUseCase;
  final CreateMeetTokenUseCase _createLiveKitTokenUseCase;

  /// Checks if the user has already joined a meeting room and shows a warning if true.
  bool hasJoinedMeetingRoom({
    BuildContext? context,
    bool isShowSnackBar = true,
  }) {
    if (_roomBloc.hasJoinedMeetingRoom) {
      if (isShowSnackBar) {
        final appLocalizations = AppLocalizations.of(context!)!;
        SnackBarOverlayHelper().showSnackBar(
          widgetBuilder: (_) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: ui.SnackBarUtilV2.showFloatingSnackBar(
                context: context,
                content: appLocalizations.itLooksLikeYouAreInAZiiChatCall,
                snackBarType: ui.SnackBarType.warning,
              ),
            );
          },
        );
      }
      return true;
    }
    return false;
  }

  /// Retrieves the current room if the user is in the specified channel.
  /// Throws an exception if the user is in a different room.
  Room? getCurrentRoom({
    required String channelId,
    required String workspaceId,
  }) {
    if (!_roomBloc.hasJoinedMeetingRoom) return null;

    if (_roomBloc.isSameRoom(channelId: channelId, workspaceId: workspaceId)) {
      return _roomBloc.currentRoom!;
    }

    throw InOtherRoomException();
  }

  /// Adds a listener to monitor changes in the meeting room.
  void addRoomListener(
    Function(MeetingRoomInfo?) callback, {
    bool triggerImmediately = false,
  }) {
    _roomBloc.addRoomListener(callback, triggerImmediately: triggerImmediately);
  }

  /// Removes a previously added listener for meeting room changes.
  void removeRoomListener(Function(MeetingRoomInfo?) callback) {
    _roomBloc.removeRoomListener(callback);
  }

  /// Fetches a token for joining a meeting room and invokes the success or error callback.
  Future<void> getRoomToken({
    required String channelId,
    required String workspaceId,
    required void Function(String token, String host) onSuccess,
    void Function(Object? error)? onError,
  }) async {
    final output = await _createLiveKitTokenUseCase.execute(
      CreateMeetTokenInput(
        workspaceId: workspaceId,
        channelId: channelId,
      ),
    );
    if (output.ok) {
      onSuccess(output.token!, output.host!);
      return;
    }
    onError?.call(output.error);
  }

  /// Connects to a meeting room using the provided token and host, and invokes the appropriate callback.
  Future<void> connectToRoom({
    required String token,
    required String host,
    required void Function(Room room) onConnected,
    void Function(Object? error)? onError,
  }) async {
    final output = await _connectRoomUseCase.execute(
      ConnectRoomInput(
        url: host,
        token: token,
      ),
    );
    if (output.ok) {
      onConnected(output.room!);
    } else {
      onError?.call(output.error);
    }
  }
}
