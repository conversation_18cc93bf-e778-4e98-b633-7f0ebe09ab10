import 'dart:async';

import 'package:chat/chat.dart';
import 'package:shared/shared.dart';

import '../../../core.dart' as core;
import '../../common/di/di.dart';

class PresenceHandler {
  static final PresenceHandler _instance = PresenceHandler._internal();

  factory PresenceHandler() {
    return _instance;
  }

  PresenceHandler._internal();

  Future<int?> sendPresenceEvent({bool isOnline = false}) async {
    final unreadChannelIds = getIt<ChannelRepository>().getUnreadChannelIds();
    final unreadFriendRequestIds = getIt<ChatFriendRepository>()
        .getFriendRequests()
        .map((friend) => friend.friendId)
        .toList();
    final presenceData = PresenceData(
      isOnline: isOnline,
      userId: core.Config.getInstance().activeSessionKey,
      badgeValueArgument: BadgeValueArgument(
        unreadChannelIds: unreadChannelIds,
        unreadFriendRequestIds: unreadFriendRequestIds,
      ),
    );

    getIt<AppEventBus>()
        .fire(SendCloudEvent.createPresenceUpdatedEvent(presenceData.toJson()));
    return presenceData.count;
  }
}
