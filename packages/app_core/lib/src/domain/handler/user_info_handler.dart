import 'dart:async';

import 'package:chat/chat.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../common/di/di.dart';
import '../message/send_message_handler.dart';

@LazySingleton()
class UserInfoHandler {
  UserInfoHandler();

  late StreamSubscription? _userInfoSubscription;
  late MessagesBloc _messagesBloc = getIt<MessagesBloc>();

  void setMessageBloc(MessagesBloc messagesBloc) {
    _messagesBloc = messagesBloc;
  }

  MessagesBloc get messagesBloc {
    if (_messagesBloc.isClosed) {
      _messagesBloc = getIt<MessagesBloc>();
    }
    return _messagesBloc;
  }

  void setupUserInfoHandler() {
    _userInfoSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<UserInfoEvent>()
        .listen(_onReceivedFromUserInfoEvent);
  }

  void dispose() {
    _userInfoSubscription?.cancel();
  }

  void _onReceivedFromUserInfoEvent(event) async {
    if (event is PokeMessageEvent) {
      final handler = SendMessageHandler(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        userId: event.userId,
      )..messagesBloc = messagesBloc;
      handler.sendPokeMessage();
    }
  }
}
