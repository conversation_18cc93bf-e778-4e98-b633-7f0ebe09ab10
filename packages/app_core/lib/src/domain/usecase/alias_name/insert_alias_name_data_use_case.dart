import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../core.dart';

part 'insert_alias_name_data_use_case.freezed.dart';

@Injectable()
class InsertAliasNameUseCase
    extends BaseFutureUseCase<InsertAliasNameInput, InsertAliasNameOutput> {
  const InsertAliasNameUseCase(this._repository);

  final PrivateDataRepository _repository;

  @protected
  @override
  Future<InsertAliasNameOutput> buildUseCase(
    InsertAliasNameInput input,
  ) async {
    try {
      _repository.upsert(input.userPrivateData);
      return InsertAliasNameOutput(success: true);
    } on Exception catch (_) {
      return InsertAliasNameOutput(success: false);
    }
  }
}

@freezed
sealed class InsertAliasNameInput extends BaseInput
    with _$InsertAliasNameInput {
  const InsertAliasNameInput._();
  factory InsertAliasNameInput({
    required UserPrivateData userPrivateData,
  }) = _InsertAliasNameInput;
}

@freezed
sealed class InsertAliasNameOutput extends BaseOutput
    with _$InsertAliasNameOutput {
  const InsertAliasNameOutput._();
  factory InsertAliasNameOutput({
    required bool success,
  }) = _InsertAliasNameOutput;
}
