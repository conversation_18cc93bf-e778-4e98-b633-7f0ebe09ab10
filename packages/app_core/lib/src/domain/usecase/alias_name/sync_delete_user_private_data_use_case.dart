import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../core.dart';

part 'sync_delete_user_private_data_use_case.freezed.dart';

@Injectable()
class SyncDeleteUserPrivateDataUseCase extends BaseFutureUseCase<
    SyncDeleteUserPrivateDataUseCaseInput,
    SyncDeleteUserPrivateDataUseCaseOutput> {
  SyncDeleteUserPrivateDataUseCase(this._repository);

  final PrivateDataRepository _repository;

  @protected
  @override
  Future<SyncDeleteUserPrivateDataUseCaseOutput> buildUseCase(
    SyncDeleteUserPrivateDataUseCaseInput input,
  ) async {
    var getUsers = _repository.getUsers();
    var deletedAliasName = input.deletedUsers
        .where(
          (api) =>
              getUsers.indexWhere((aliasName) {
                return aliasName.userId == api.userId;
              }) ==
              -1,
        )
        .toList();
    deletedAliasName.forEach((item) {
      AppEventBus.publish(
        SetAliasNameEvent(userId: item.userId, aliasName: ''),
      );
    });

    try {
      return SyncDeleteUserPrivateDataUseCaseOutput(ok: false);
    } on Exception catch (_) {
      return SyncDeleteUserPrivateDataUseCaseOutput(ok: false);
    }
  }
}

@freezed
sealed class SyncDeleteUserPrivateDataUseCaseInput extends BaseInput
    with _$SyncDeleteUserPrivateDataUseCaseInput {
  const SyncDeleteUserPrivateDataUseCaseInput._();
  factory SyncDeleteUserPrivateDataUseCaseInput({
    @Default([]) List<DeletedUser> deletedUsers,
  }) = _SyncDeleteUserPrivateDataUseCaseInput;
}

@freezed
sealed class SyncDeleteUserPrivateDataUseCaseOutput extends BaseOutput
    with _$SyncDeleteUserPrivateDataUseCaseOutput {
  const SyncDeleteUserPrivateDataUseCaseOutput._();
  factory SyncDeleteUserPrivateDataUseCaseOutput({
    @Default(false) bool ok,
  }) = _SyncDeleteUserPrivateDataUseCaseOutput;
}
