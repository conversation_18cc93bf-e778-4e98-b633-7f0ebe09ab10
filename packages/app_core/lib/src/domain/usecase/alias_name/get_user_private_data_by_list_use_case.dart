import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../core.dart';

part 'get_user_private_data_by_list_use_case.freezed.dart';

@Injectable()
class GetUserPrivateDataByListUserUseCase extends BaseFutureUseCase<
    GetUserPrivateDataByListUserUseCaseInput,
    GetUserPrivateDataByListUserUseCaseOutput> {
  const GetUserPrivateDataByListUserUseCase(this._repository);

  final PrivateDataRepository _repository;

  @protected
  @override
  Future<GetUserPrivateDataByListUserUseCaseOutput> buildUseCase(
    GetUserPrivateDataByListUserUseCaseInput input,
  ) async {
    try {
      final listUserPrivateData =
          _repository.getUsersByListUserId(input.listUserId);

      if (listUserPrivateData != null) {
        return GetUserPrivateDataByListUserUseCaseOutput(
          data: listUserPrivateData,
        );
      }
      return GetUserPrivateDataByListUserUseCaseOutput(data: null);
    } on Exception catch (_) {
      return GetUserPrivateDataByListUserUseCaseOutput(data: null);
    }
  }
}

@freezed
sealed class GetUserPrivateDataByListUserUseCaseInput extends BaseInput
    with _$GetUserPrivateDataByListUserUseCaseInput {
  const GetUserPrivateDataByListUserUseCaseInput._();
  factory GetUserPrivateDataByListUserUseCaseInput({
    @Default([]) List<String> listUserId,
  }) = _GetUserPrivateDataByListUserUseCaseInput;
}

@freezed
sealed class GetUserPrivateDataByListUserUseCaseOutput extends BaseOutput
    with _$GetUserPrivateDataByListUserUseCaseOutput {
  const GetUserPrivateDataByListUserUseCaseOutput._();
  factory GetUserPrivateDataByListUserUseCaseOutput({
    @Default(null) List<UserPrivateData>? data,
  }) = _GetUserPrivateDataByListUserUseCaseOutput;
}
