import 'dart:convert';

import 'package:chat/chat.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_view_api/user_view_api.dart';

import '../../../data/repositories/database/entities/private_data.dart';
import '../../../serializer/private_data_serializer.dart';

part 'load_user_private_data_use_case.freezed.dart';

@Injectable()
class LoadUserPrivateDataUseCase extends BaseFutureUseCase<
    LoadUserPrivateDataUseCaseInput, LoadUserPrivateDataUseCaseOutput> {
  const LoadUserPrivateDataUseCase();

  @protected
  @override
  Future<LoadUserPrivateDataUseCaseOutput> buildUseCase(
    LoadUserPrivateDataUseCaseInput input,
  ) async {
    try {
      final result = await UserViewClient().instance.getPrivateData();
      if (result.data?.ok ?? false) {
        final json = jsonDecode(
          standardSerializers.toJson(
            V3PrivateData.serializer,
            result.data!.data,
          ),
        );

        final newPrivateData =
            PrivateDataSerializer.serializeFromJson(json: json);

        if (newPrivateData != null) {
          return LoadUserPrivateDataUseCaseOutput(data: newPrivateData);
        }
      }
      return LoadUserPrivateDataUseCaseOutput(data: null);
    } on Exception catch (_) {
      return LoadUserPrivateDataUseCaseOutput(data: null);
    }
  }
}

@freezed
sealed class LoadUserPrivateDataUseCaseInput extends BaseInput
    with _$LoadUserPrivateDataUseCaseInput {
  const LoadUserPrivateDataUseCaseInput._();
  factory LoadUserPrivateDataUseCaseInput() = _LoadUserPrivateDataUseCaseInput;
}

@freezed
sealed class LoadUserPrivateDataUseCaseOutput extends BaseOutput
    with _$LoadUserPrivateDataUseCaseOutput {
  const LoadUserPrivateDataUseCaseOutput._();
  factory LoadUserPrivateDataUseCaseOutput({
    @Default(null) PrivateData? data,
  }) = _LoadUserPrivateDataUseCaseOutput;
}
