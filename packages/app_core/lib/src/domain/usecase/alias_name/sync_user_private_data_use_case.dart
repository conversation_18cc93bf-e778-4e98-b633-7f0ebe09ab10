import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:intl/intl.dart';
import 'package:shared/shared.dart';

import '../../../../core.dart';
import '../../../data/interface/private_data_comparator.dart';
import '../../../data/interface/private_data_sync.dart';
import '../../../data/interface/sync_key_enum.dart';
import 'get_user_private_data_use_case.dart';
import 'load_user_private_data_use_case.dart';

part 'sync_user_private_data_use_case.freezed.dart';

@Injectable()
class SyncUserPrivateDataUseCase extends BaseFutureUseCase<
    SyncUserPrivateDataUseCaseInput, SyncUserPrivateDataUseCaseOutput> {
  const SyncUserPrivateDataUseCase(
    this._repository,
    this._loadUseCase,
    this._getUseCase,
    this._webSocketManager,
  );

  final LoadUserPrivateDataUseCase _loadUseCase;
  final GetUserPrivateDataUseCase _getUseCase;
  final PrivateDataRepository _repository;
  final WebSocketManager _webSocketManager;

  @protected
  @override
  Future<SyncUserPrivateDataUseCaseOutput> buildUseCase(
    SyncUserPrivateDataUseCaseInput input,
  ) async {
    var getOutput = await _getUseCase.execute(GetUserPrivateDataUseCaseInput());

    var oldPrivateData = getOutput.data;

    try {
      final loadOutput =
          await _loadUseCase.execute(LoadUserPrivateDataUseCaseInput());
      var newPrivateData = loadOutput.data;

      if (newPrivateData != null) {
        final duplicateResult =
            _checkAndResolveDuplicateSorts(newPrivateData.channels);
        if (duplicateResult.hasDuplicates) {
          for (final updatedChannel in duplicateResult.updatedChannels) {
            var cloudEvent = CloudEvent.createPrivateDataSync(
              PrivateDataSync(
                key: SyncKeyEnum.channels.name,
                value: updatedChannel.toJson(),
              ).toJson(),
            );

            _webSocketManager.sendMessage(cloudEvent.toJson());

            await Future.delayed(DurationUtils.ms1000);
          }
          return await buildUseCase(input);
        }
      }

      if (newPrivateData != null) {
        if (_shouldInsert(oldPrivateData, newPrivateData)) {
          _updatePrivateData(oldPrivateData, newPrivateData);
          return SyncUserPrivateDataUseCaseOutput(ok: true);
        }
      }

      return SyncUserPrivateDataUseCaseOutput(ok: false);
    } on Exception catch (_) {
      return SyncUserPrivateDataUseCaseOutput(ok: false);
    }
  }

  bool _shouldInsert(PrivateData? oldData, PrivateData newData) {
    if (oldData == null) return true;
    if (oldData.updateTime != newData.updateTime) return true;
    return false;
  }

  /// Check and resolve duplicate sorts in the channel list
  /// Returns a result including:
  /// - hasDuplicates: whether duplicate sorts were found
  /// - updatedChannels: list of channels that were updated (only includes channels that actually changed)
  _DuplicateSortResult _checkAndResolveDuplicateSorts(
    List<ChannelPrivateData> channels,
  ) {
    if (channels.isEmpty) {
      return _DuplicateSortResult(
        hasDuplicates: false,
        updatedChannels: channels,
      );
    }

    // Only process pinned channels (sort > 0)
    // Skip channels with sort = 0 and pinned = false (unpinned channels)
    final pinnedChannels = channels
        .where((c) => c.pinned == true && c.sort != null && c.sort! > 0)
        .toList();

    // Count the number of unpinned channels that were skipped
    final unpinnedCount =
        channels.where((c) => c.sort == 0 && c.pinned == false).length;

    if (pinnedChannels.isEmpty) {
      return _DuplicateSortResult(hasDuplicates: false, updatedChannels: []);
    }

    // Sort by sort value in ascending order for processing
    pinnedChannels.sort((a, b) => (a.sort ?? 0).compareTo(b.sort ?? 0));

    // Check if there are duplicate sorts
    bool hasDuplicates = false;
    final sortValues = <int, List<ChannelPrivateData>>{};

    // Group channels with the same sort value
    for (final channel in pinnedChannels) {
      if (channel.sort != null) {
        sortValues.putIfAbsent(channel.sort!, () => []);
        sortValues[channel.sort!]!.add(channel);

        // If there's more than 1 channel with the same sort, mark as having duplicates
        if (sortValues[channel.sort!]!.length > 1) {
          hasDuplicates = true;
        }
      }
    }

    // If no duplicates found, return an empty list as no channels need updating
    if (!hasDuplicates) {
      return _DuplicateSortResult(hasDuplicates: false, updatedChannels: []);
    }

    // List of updated channels (only includes channels that actually changed)
    final updatedChannels = <ChannelPrivateData>[];

    // List of sort values already in use (to avoid duplicates)
    final usedSorts = <int>{};
    for (final channel in channels) {
      if (channel.sort != null && channel.pinned == true) {
        usedSorts.add(channel.sort!);
      }
    }

    // Process each group of channels with the same sort value
    for (final entry in sortValues.entries) {
      final sort = entry.key;
      final channelsWithSameSort = entry.value;

      // If there's only 1 channel with this sort value, no need to process
      if (channelsWithSameSort.length <= 1) continue;

      // Sort channels with the same sort value by channelId to ensure stable ordering
      channelsWithSameSort.sort((a, b) => a.channelId.compareTo(b.channelId));

      // Keep the first channel unchanged, update the remaining channels
      for (int i = 1; i < channelsWithSameSort.length; i++) {
        final channel = channelsWithSameSort[i];
        int newSort = sort;

        // Find a new sort value that doesn't conflict with existing ones
        while (usedSorts.contains(newSort)) {
          newSort++;
        }

        // Update sort value and increment version
        final oldSort = channel.sort;
        channel.sort = newSort;
        channel.version += 1;
        usedSorts.add(newSort);

        // Add the updated channel to the result list
        updatedChannels.add(channel);
      }
    }

    return _DuplicateSortResult(
      hasDuplicates: true,
      updatedChannels: updatedChannels,
    );
  }

  void _updatePrivateData(
    PrivateData? oldPrivateData,
    PrivateData newPrivateData,
  ) {
    if (oldPrivateData == null) {
      DateFormat dateFormat = DateFormat("yyyy-MM-dd");
      oldPrivateData = PrivateData(
        sessionKey: Config.getInstance().activeSessionKey ?? '',
        userId: Config.getInstance().activeSessionKey ?? '',
        createTime: dateFormat.format(DateTime(2000, 01, 01)),
        updateTime: dateFormat.format(DateTime(2000, 01, 02)),
      );
    }

    var c = PrivateDataComparator();

    c.processUpdates<ChannelPrivateData>(
      oldList: oldPrivateData.channels,
      newList: newPrivateData.channels,
      onInsert: _repository.insertChannel,
      onUpdate: _repository.updateChannel,
      onDelete: _repository.deleteChannel,
    );

    c.processUpdates<UserPrivateData>(
      oldList: oldPrivateData.users,
      newList: newPrivateData.users,
      onInsert: _repository.insertUser,
      onUpdate: _repository.updateUser,
      onDelete: _repository.deleteUser,
    );

    c.processUpdates<CallLogPrivateData>(
      oldList: oldPrivateData.callLogs,
      newList: newPrivateData.callLogs,
      onInsert: _repository.insertCallLog,
      onUpdate: _repository.updateCallLog,
      onDelete: _repository.deleteCallLog,
    );
  }
}

@freezed
sealed class SyncUserPrivateDataUseCaseInput extends BaseInput
    with _$SyncUserPrivateDataUseCaseInput {
  const SyncUserPrivateDataUseCaseInput._();

  factory SyncUserPrivateDataUseCaseInput() = _SyncUserPrivateDataUseCaseInput;
}

@freezed
sealed class SyncUserPrivateDataUseCaseOutput extends BaseOutput
    with _$SyncUserPrivateDataUseCaseOutput {
  const SyncUserPrivateDataUseCaseOutput._();

  factory SyncUserPrivateDataUseCaseOutput({
    @Default(false) bool ok,
  }) = _SyncUserPrivateDataUseCaseOutput;
}

/// Result of checking and resolving duplicate sorts
class _DuplicateSortResult {
  final bool hasDuplicates;
  final List<ChannelPrivateData> updatedChannels;

  _DuplicateSortResult({
    required this.hasDuplicates,
    required this.updatedChannels,
  });
}
