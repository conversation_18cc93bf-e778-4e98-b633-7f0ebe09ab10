import 'package:chat/chat.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';

@Injectable()
class LocalBlockUpdateUseCase
    extends BaseFutureUseCase<LocalBlockUpdateInput, LocalBlockUpdateOutput> {
  LocalBlockUpdateUseCase(this._chatUserRepository, this._userRepository);

  final ChatUserRepository _chatUserRepository;
  final UserRepository _userRepository;

  @override
  Future<LocalBlockUpdateOutput> buildUseCase(
    LocalBlockUpdateInput input,
  ) async {
    try {
      final chatUser = _chatUserRepository.getUser(input.userId);
      chatUser?.blocked = input.isBlocked;
      _chatUserRepository.forceInsert(chatUser!);

      final user = _userRepository.getUser(input.userId);
      user?.blocked = input.isBlocked;
      _userRepository.forceInsert(user!);
      return LocalBlockUpdateOutput(ok: true, error: null);
    } catch (error) {
      return LocalBlockUpdateOutput(ok: false, error: error.toString());
    }
  }
}

class LocalBlockUpdateInput extends BaseInput {
  LocalBlockUpdateInput({
    required this.userId,
    required this.isBlocked,
  });

  final String userId;
  final bool isBlocked;
}

class LocalBlockUpdateOutput extends BaseOutput {
  LocalBlockUpdateOutput({
    required this.ok,
    this.error,
  });

  final bool ok;
  final String? error;
}
