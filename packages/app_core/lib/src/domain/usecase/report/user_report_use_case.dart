import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_report_api/user_report_api.dart';

import '../../../data/source/api/client/report_client.dart';

@Injectable()
class UserReportUseCase
    extends BaseFutureUseCase<UserReportInput, UserReportOutput> {
  UserReportUseCase();

  @override
  Future<UserReportOutput> buildUseCase(
    UserReportInput input,
  ) async {
    final requestBody = V3ReportUserRequestBuilder()
      ..userId = input.userId
      ..reportCategory = input.reportCategory
      ..pretendingTo = input.pretendingTo
      ..reportReason = input.reason;
    final response =
        await UserReportClient().instance.reportUser(body: requestBody.build());
    if (response.data?.ok ?? false) {
      return UserReportOutput(ok: true);
    }
    return UserReportOutput(ok: false, error: response.data?.error);
  }
}

class UserReportInput extends BaseInput {
  UserReportInput({
    required this.userId,
    this.pretendingTo,
    this.reportCategory,
    this.reason,
  });

  final String userId;
  final V3PretendingTo? pretendingTo;
  final V3ReportCategory? reportCategory;
  final String? reason;
}

class UserReportOutput extends BaseOutput {
  UserReportOutput({
    required this.ok,
    this.error,
  });

  final bool ok;
  final V3Error? error;
}
