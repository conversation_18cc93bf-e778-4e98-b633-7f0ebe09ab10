import 'package:chat/chat.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';

@Injectable()
class ConvertChatUserAndUpsertUserUseCase extends BaseFutureUseCase<
    ConvertChatUserAndUpsertUserInput, ConvertChatUserAndUpsertUserOutput> {
  final UserRepository _userRepository;

  ConvertChatUserAndUpsertUserUseCase(
    this._userRepository,
  );

  @protected
  @override
  Future<ConvertChatUserAndUpsertUserOutput> buildUseCase(
    ConvertChatUserAndUpsertUserInput input,
  ) async {
    User user = User.fromJson(input.chatUser.toJson());
    if (input.chatUser.friendData != null) {
      user.friendData =
          FriendData.fromJson(input.chatUser.friendData!.toJson());
    }
    await _userRepository.forceInsert(user);

    return ConvertChatUserAndUpsertUserOutput();
  }
}

class ConvertChatUserAndUpsertUserInput extends BaseInput {
  final ChatUser chatUser;

  ConvertChatUserAndUpsertUserInput({required this.chatUser});
}

class ConvertChatUserAndUpsertUserOutput extends BaseOutput {}
