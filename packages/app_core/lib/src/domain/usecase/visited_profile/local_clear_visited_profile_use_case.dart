import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';

part 'local_clear_visited_profile_use_case.freezed.dart';

@Injectable()
class LocalClearVisitedProfileUseCase extends BaseFutureUseCase<
    LocalClearVisitedProfileUseCaseInput,
    LocalClearVisitedProfileUseCaseOutput> {
  const LocalClearVisitedProfileUseCase(this._visitedProfileRepository);

  final VisitedProfileRepository _visitedProfileRepository;

  @protected
  @override
  Future<LocalClearVisitedProfileUseCaseOutput> buildUseCase(
    LocalClearVisitedProfileUseCaseInput input,
  ) async {
    try {
      final result = await _visitedProfileRepository
          .updateAllIsReadVisitedProfile(input.isAllRead ?? true);

      return LocalClearVisitedProfileUseCaseOutput(
        response: result.length < 0 ? false : true,
      );
    } on Exception catch (_) {
      return LocalClearVisitedProfileUseCaseOutput(response: false);
    }
  }
}

@freezed
sealed class LocalClearVisitedProfileUseCaseInput extends BaseInput
    with _$LocalClearVisitedProfileUseCaseInput {
  const LocalClearVisitedProfileUseCaseInput._();
  factory LocalClearVisitedProfileUseCaseInput({bool? isAllRead}) =
      _LocalClearVisitedProfileUseCaseInput;
}

@freezed
sealed class LocalClearVisitedProfileUseCaseOutput extends BaseOutput
    with _$LocalClearVisitedProfileUseCaseOutput {
  const LocalClearVisitedProfileUseCaseOutput._();
  factory LocalClearVisitedProfileUseCaseOutput({bool? response}) =
      _LocalClearVisitedProfileUseCaseOutput;
}
