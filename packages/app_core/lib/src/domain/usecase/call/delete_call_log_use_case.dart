import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../core.dart';
import '../../../data/interface/private_data_sync.dart';
import '../../../data/interface/sync_key_enum.dart';

@Injectable()
class DeleteCallLogUseCase
    extends BaseFutureUseCase<DeleteCallLogInput, DeleteCallLogOutput> {
  const DeleteCallLogUseCase(this._repository);

  final PrivateDataRepository _repository;

  @protected
  @override
  Future<DeleteCallLogOutput> buildUseCase(
    DeleteCallLogInput input,
  ) async {
    try {
      final privateCallLogData = _repository.getCallLog(input.callId);
      if (privateCallLogData != null) {
        privateCallLogData.version = -1;
        _repository.deleteCallLog(input.callId);
        final cloudEvent = CloudEvent.createPrivateDataSync(
          PrivateDataSync(
            key: SyncKeyEnum.callLogs.name,
            value: privateCallLogData.toJson(),
          ).toJson(),
        );
        return DeleteCallLogOutput(cloudEvent: cloudEvent.toJson());
      }
      return DeleteCallLogOutput();
    } on Exception catch (_) {
      return DeleteCallLogOutput();
    }
  }
}

class DeleteCallLogInput extends BaseInput {
  const DeleteCallLogInput({required this.callId});

  final String callId;
}

class DeleteCallLogOutput extends BaseOutput {
  const DeleteCallLogOutput({this.cloudEvent});

  final Map<String, dynamic>? cloudEvent;
}
