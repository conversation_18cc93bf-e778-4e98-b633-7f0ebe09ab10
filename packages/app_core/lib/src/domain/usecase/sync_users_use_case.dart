import 'dart:convert';

import 'package:auth/auth.dart';
import 'package:built_collection/built_collection.dart';
import 'package:chat/chat.dart' as chat;
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';
import 'package:user_view_api/user_view_api.dart' as user_api;

import '../../common/di/di.dart';
import 'alias_name/sync_delete_user_private_data_use_case.dart';

@Injectable()
class SyncUsersUseCase
    extends BaseFutureUseCase<SyncUsersInput, SyncUsersOutput> {
  SyncUsersUseCase(
    this._chatUserRepository,
    this._userRepository,
    this._sessionRepository,
  );

  final chat.ChatUserRepository _chatUserRepository;
  final UserRepository _userRepository;
  final SessionRepository _sessionRepository;

  @override
  Future<SyncUsersOutput> buildUseCase(SyncUsersInput input) async {
    // No user logged in
    if (chat.Config.getInstance().activeSessionKey == null)
      return SyncUsersOutput();

    final chatUsers = _chatUserRepository.getUsers();
    final users = _userRepository.getUsers();
    final userIds = {
      ...chatUsers.map((u) => u.userId),
      ...users.map((u) => u.userId),
    };

    var metadata = _sessionRepository.getSessionMetadata();
    String userUpdateTimeAfter = metadata?.userUpdateTimeAfter ?? '';

    if (userUpdateTimeAfter.isEmpty &&
        (chatUsers.isNotEmpty || users.isNotEmpty)) {
      final maxChatUpdateTime = chatUsers.isNotEmpty
          ? chatUsers.map((u) => DateTime.parse(u.updateTime!)).fold<DateTime>(
                DateTime.fromMillisecondsSinceEpoch(0),
                (a, b) => a.isAfter(b) ? a : b,
              )
          : null;

      final maxUserUpdateTime = users.isNotEmpty
          ? users.map((u) => DateTime.parse(u.updateTime!)).fold<DateTime>(
                DateTime.fromMillisecondsSinceEpoch(0),
                (a, b) => a.isAfter(b) ? a : b,
              )
          : null;

      userUpdateTimeAfter = [maxChatUpdateTime, maxUserUpdateTime]
          .whereType<DateTime>()
          .reduce((a, b) => a.isAfter(b) ? a : b)
          .toIso8601String();
    }

    final result = await _getSyncUser(userIds.toList(), userUpdateTimeAfter);

    if (result != null) {
      final (
        updatedUser,
        updatedChatUser,
        deletedUsersList,
        blockedUsers,
        syncTime
      ) = result;

      if (updatedUser != null) {
        await _userRepository.insertAll(updatedUser);
        await _chatUserRepository.insertAll(updatedChatUser);
      }

      for (final user in [...deletedUsersList, ...blockedUsers]) {
        await _userRepository.deleteUser(user.userId);
        await _chatUserRepository.deleteUser(user.userId);
      }

      if (metadata != null) {
        _sessionRepository.updateUserUpdateTimeAfter(syncTime);
      }
    }

    return SyncUsersOutput();
  }

  Future<
      (
        List<User>?,
        List<chat.ChatUser>,
        List<DeletedUser>,
        List<DeletedUser>,
        String,
      )?> _getSyncUser(List<String> userIds, String updateTimeAfter) async {
    try {
      userIds.removeWhere(
        (userid) => userid == chat.Config.getInstance().activeSessionKey,
      );
      if (userIds.isEmpty) return await _syncUsersOnce([], updateTimeAfter);

      final chunkedUserIds = [
        for (var i = 0; i < userIds.length; i += 50)
          userIds.sublist(i, i + 50 > userIds.length ? userIds.length : i + 50),
      ];
      final results = await Future.wait(
        chunkedUserIds.map((chunk) => _syncUsersOnce(chunk, updateTimeAfter)),
      );

      final totalUpdatedUsers = <User>[];
      final totalUpdatedChatUsers = <chat.ChatUser>[];
      final totalDeletedUsers = <DeletedUser>[];
      final totalBlockedUsers = <DeletedUser>[];
      String finalSyncTime = '';

      for (final result in results) {
        final (
          updatedUser,
          updatedChatUser,
          deletedUsersList,
          blockedUsers,
          syncTime
        ) = result;
        totalUpdatedUsers.addAll(updatedUser);
        totalUpdatedChatUsers.addAll(updatedChatUser);
        totalDeletedUsers.addAll(deletedUsersList);
        totalBlockedUsers.addAll(blockedUsers);
        if (syncTime.compareTo(finalSyncTime) > 0) finalSyncTime = syncTime;
      }

      return (
        totalUpdatedUsers,
        totalUpdatedChatUsers,
        totalDeletedUsers,
        totalBlockedUsers,
        finalSyncTime
      );
    } catch (e) {
      return null;
    }
  }

  Future<
      (
        List<User>,
        List<chat.ChatUser>,
        List<DeletedUser>,
        List<DeletedUser>,
        String,
      )> _syncUsersOnce(List<String> userIds, String updateTimeAfter) async {
    final result = await UserViewClient().instance.syncUsers(
          updateTimeAfter: updateTimeAfter,
          userIds: BuiltList<String>(userIds),
        );
    final users = result.data!.data?.toList() ?? [];
    final deletedUsers = result.data!.userDeleted?.toList() ?? [];

    final updatedUser = users.map((user) {
      final json = jsonDecode(
        user_api.standardSerializers.toJson(
          user_api.V3UserView.serializer,
          user,
        ),
      ) as Map<String, dynamic>?;
      if (json == null) throw Exception("Invalid user JSON structure");
      json['sessionKey'] = chat.Config.getInstance().activeSessionKey;
      return User.fromJson(json);
    }).toList();

    final updatedChatUser = users.map((user) {
      final json = jsonDecode(
        user_api.standardSerializers.toJson(
          user_api.V3UserView.serializer,
          user,
        ),
      ) as Map<String, dynamic>?;
      if (json == null) throw Exception("Invalid chat user JSON structure");
      json['sessionKey'] = chat.Config.getInstance().activeSessionKey;
      return chat.ChatUser.fromJson(json);
    }).toList();

    final blockedUsers = deletedUsers
        .where((u) => u.type == user_api.V3UserDeletedTypeEnum.USER_BLOCKED)
        .map((u) => DeletedUser(u.userId!, u.username!))
        .toList();
    final deletedUsersList = deletedUsers
        .where((u) => u.type == user_api.V3UserDeletedTypeEnum.USER_DELETED)
        .map((u) => DeletedUser(u.userId!, u.username!))
        .toList();
    if (deletedUsersList.isNotEmpty) {
      List<String> usersId =
          deletedUsersList.map((user) => user.userId).toList();
      await GetIt.instance
          .get<chat.DeleteChatFriends>()
          .execute(chat.DeleteChatFriendsInput(usersId: usersId));
    }

    await getIt<SyncDeleteUserPrivateDataUseCase>().execute(
      SyncDeleteUserPrivateDataUseCaseInput(deletedUsers: deletedUsersList),
    );

    return (
      updatedUser,
      updatedChatUser,
      deletedUsersList,
      blockedUsers,
      result.data!.syncTime!
    );
  }
}

class SyncUsersInput extends BaseInput {
  SyncUsersInput({this.userId, this.userName});

  final String? userId;
  final String? userName;
}

class SyncUsersOutput extends BaseOutput {
  SyncUsersOutput({this.user});

  final User? user;
}

class DeletedUser {
  DeletedUser(this.userId, this.username);

  final String userId;
  final String username;
}
