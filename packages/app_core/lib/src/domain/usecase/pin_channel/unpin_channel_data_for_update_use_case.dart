import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../core.dart';
import '../../../data/interface/private_data_sync.dart';
import '../../../data/interface/sync_key_enum.dart';

part 'unpin_channel_data_for_update_use_case.freezed.dart';

@Injectable()
class UnPinChannelDataForUpdateUseCase extends BaseFutureUseCase<
    UnPinChannelDataForUpdateInput, UnPinChannelDataForUpdateOutput> {
  const UnPinChannelDataForUpdateUseCase(this._repository);

  final PrivateDataRepository _repository;

  @protected
  @override
  Future<UnPinChannelDataForUpdateOutput> buildUseCase(
    UnPinChannelDataForUpdateInput input,
  ) async {
    try {
      var privateChannelData = _repository.getChannel(input.channelId);

      if (privateChannelData == null) {
        privateChannelData = ChannelPrivateData(
          sessionKey: Config.getInstance().activeSessionKey ?? '',
          channelId: input.channelId,
          version: 0,
          sort: 0,
          pinned: false,
          source: '',
          lastSeenMessageId: '',
          unreadCount: 0,
        );
      }
      privateChannelData..version += 1;
      privateChannelData..pinned = false;
      privateChannelData..sort = 0;
      _repository.updateChannel(privateChannelData);

      var cloudEvent = CloudEvent.createPrivateDataSync(
        PrivateDataSync(
          key: SyncKeyEnum.channels.name,
          value: privateChannelData.toJson(),
        ).toJson(),
      );

      return UnPinChannelDataForUpdateOutput(
        cloudEvent: cloudEvent.toJson(),
      );
    } on Exception catch (_) {
      return UnPinChannelDataForUpdateOutput(cloudEvent: null);
    }
  }
}

@freezed
sealed class UnPinChannelDataForUpdateInput extends BaseInput
    with _$UnPinChannelDataForUpdateInput {
  const UnPinChannelDataForUpdateInput._();
  factory UnPinChannelDataForUpdateInput({
    required String channelId,
  }) = _UnPinChannelDataForUpdateInput;
}

@freezed
sealed class UnPinChannelDataForUpdateOutput extends BaseOutput
    with _$UnPinChannelDataForUpdateOutput {
  const UnPinChannelDataForUpdateOutput._();
  factory UnPinChannelDataForUpdateOutput({
    required Map<String, dynamic>? cloudEvent,
  }) = _UnPinChannelDataForUpdateOutput;
}
