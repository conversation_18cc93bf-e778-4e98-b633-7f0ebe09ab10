import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../core.dart';
import '../../../data/interface/private_data_sync.dart';
import '../../../data/interface/sync_key_enum.dart';

part 'retry_pin_channel_data_for_update_use_case.freezed.dart';

@Injectable()
class RetryPinChannelDataForUpdateUseCase extends BaseFutureUseCase<
    RetryPinChannelDataForUpdateInput, RetryPinChannelDataForUpdateOutput> {
  const RetryPinChannelDataForUpdateUseCase(this._repository);

  final PrivateDataRepository _repository;

  @protected
  @override
  Future<RetryPinChannelDataForUpdateOutput> buildUseCase(
    RetryPinChannelDataForUpdateInput input,
  ) async {
    try {
      var privateChannelData =
          _repository.getChannel(input.channelPrivateData.channelId);

      if (privateChannelData == null) {
        privateChannelData = ChannelPrivateData(
          sessionKey: Config.getInstance().activeSessionKey ?? '',
          channelId: input.channelPrivateData.channelId,
          version: input.channelPrivateData.version,
          sort: input.channelPrivateData.sort ?? 0,
          pinned: input.channelPrivateData.pinned,
        );
      }
      privateChannelData..version = input.channelPrivateData.version;
      privateChannelData..pinned = input.channelPrivateData.pinned;
      privateChannelData..sort = input.channelPrivateData.sort ?? 0 + 1;
      _repository.updateChannel(privateChannelData);

      var cloudEvent = CloudEvent.createPrivateDataSync(
        PrivateDataSync(
          key: SyncKeyEnum.channels.name,
          value: privateChannelData.toJson(),
        ).toJson(),
      );

      return RetryPinChannelDataForUpdateOutput(
        cloudEvent: cloudEvent.toJson(),
      );
    } on Exception catch (_) {
      return RetryPinChannelDataForUpdateOutput(cloudEvent: null);
    }
  }
}

@freezed
sealed class RetryPinChannelDataForUpdateInput extends BaseInput
    with _$RetryPinChannelDataForUpdateInput {
  const RetryPinChannelDataForUpdateInput._();
  factory RetryPinChannelDataForUpdateInput({
    required ChannelPrivateData channelPrivateData,
  }) = _RetryPinChannelDataForUpdateInput;
}

@freezed
sealed class RetryPinChannelDataForUpdateOutput extends BaseOutput
    with _$RetryPinChannelDataForUpdateOutput {
  const RetryPinChannelDataForUpdateOutput._();
  factory RetryPinChannelDataForUpdateOutput({
    required Map<String, dynamic>? cloudEvent,
  }) = _RetryPinChannelDataForUpdateOutput;
}
