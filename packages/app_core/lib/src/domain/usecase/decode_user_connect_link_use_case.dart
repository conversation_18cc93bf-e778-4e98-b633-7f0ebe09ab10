import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_connect_api/user_connect_api.dart';
import 'package:user_manager/user_manager.dart';

@Injectable()
class DecodeUserConnectLinkUseCase extends BaseFutureUseCase<
    DecodeUserConnectLinkInput, DecodeUserConnectLinkOutput> {
  const DecodeUserConnectLinkUseCase();

  @override
  Future<DecodeUserConnectLinkOutput> buildUseCase(
    DecodeUserConnectLinkInput input,
  ) async {
    try {
      final bodyBuilder = V3DecodeUserConnectLinkRequestBuilder();
      bodyBuilder.link = input.userConnectLink;
      final result = await UserConnectClient()
          .instance
          .decodeUserConnectLink(body: bodyBuilder.build());
      return DecodeUserConnectLinkOutput(result.data?.data?.userId);
    } on Exception catch (_) {
      return DecodeUserConnectLinkOutput(null);
    }
  }
}

class DecodeUserConnectLinkInput extends BaseInput {
  const DecodeUserConnectLinkInput(this.userConnectLink);

  final String userConnectLink;
}

class DecodeUserConnectLinkOutput extends BaseOutput {
  const DecodeUserConnectLinkOutput(this.userId);

  final String? userId;
}
