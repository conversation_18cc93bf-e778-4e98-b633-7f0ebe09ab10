import 'package:injectable/injectable.dart';
import 'package:notification_api/notification_api.dart';
import 'package:shared/shared.dart';

import '../../../core.dart';
import '../../data/source/api/client/notification_client.dart';

@Injectable()
class RegisterNotificationUseCase extends BaseFutureUseCase<
    RegisterNotificationInput, RegisterNotificationOutput> {
  RegisterNotificationUseCase();

  @override
  Future<RegisterNotificationOutput> buildUseCase(
    RegisterNotificationInput input,
  ) async {
    try {
      var request = V3SubscribeAllRequestBuilder();
      request.deviceToken = Config.getInstance().notificationToken;
      request.appId = EnvConfig.getBundleId;

      final result = await NotificationClient()
          .instance
          .subscribeAll(body: request.build());
      if (result.data?.ok ?? false) {
        return RegisterNotificationOutput(ok: true);
      }
    } on Exception catch (_) {
      return RegisterNotificationOutput(ok: false);
    }
    return RegisterNotificationOutput(ok: false);
  }
}

class RegisterNotificationInput extends BaseInput {
  RegisterNotificationInput();
}

class RegisterNotificationOutput extends BaseOutput {
  RegisterNotificationOutput({this.ok});

  final bool? ok;
}
