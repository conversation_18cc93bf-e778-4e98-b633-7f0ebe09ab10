import 'dart:io';
import 'dart:ui';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_compressor/image_compressor.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_picker_android/image_picker_android.dart';
import 'package:image_picker_platform_interface/image_picker_platform_interface.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

class PickerUtils {
  static Future<void> pickImageFromGalleryForUpload(
    BuildContext context, {
    void Function(XFile)? onPicked,
    void Function(Exception)? onError,
  }) async {
    final imagePickerImplementation = ImagePickerPlatform.instance;
    if (imagePickerImplementation is ImagePickerAndroid) {
      imagePickerImplementation.useAndroidPhotoPicker = true;
    }
    final image = await ImagePicker().pickImage(
      source: ImageSource.gallery,
      imageQuality: FileUploadConfig.imageQuality,
    );
    if (image != null) {
      var fileSize = await image.length();
      if (fileSize.toMB() > FileUploadConfig.maximumCoverFileSizeMB) {
        DialogUtils.showBigFileDialog(
          context,
          onFirstAction: (ctx) {
            ctx.maybePop();
          },
          maximumFileSize: FileUploadConfig.maximumCoverFileSizeMB,
        );
      } else if (!FileUtils.isValidImageType(
        FileUtils.getMimeType(image.path) ?? '',
      )) {
        DialogUtils.showUnsupportedImageFileDialog(
          context,
          onFirstAction: (ctx) {
            ctx.maybePop();
          },
        );
      } else {
        var imageCompress;
        if (FileUtils.isSVG(FileUtils.getMimeType(image.path) ?? '')) {
          Uint8List? bytes = await svgToUint8List(image.path, context: context);
          if (bytes != null) {
            imageCompress = await compressUint8List(data: bytes);
          }
        } else {
          imageCompress = await compressFile(pathFile: image.path);
        }
        if (imageCompress == null) {
          Log.e('Compress image failed', name: 'ImageCompress');
          onError?.call(Exception("ImageCompress file is null"));
          return;
        }
        onPicked?.call(imageCompress);
      }
    }
  }

  static Future<Uint8List?> svgToUint8List(
    String svgString, {
    BuildContext? context,
  }) async {
    final svgDrawableRoot =
        await vg.loadPicture(SvgFileLoader(File(svgString)), context);
    final image = await svgDrawableRoot.picture.toImage(
      svgDrawableRoot.size.width.toInt(),
      svgDrawableRoot.size.height.toInt(),
    );
    ByteData? byteData = await image.toByteData(format: ImageByteFormat.png);
    if (byteData == null) {
      return null;
    }
    final pngBytes = byteData.buffer.asUint8List();
    // final tempDir = await getTemporaryDirectory();
    // final file = await File('${tempDir.path}/svg.png').create();
    // await file.writeAsBytes(pngBytes);
    return pngBytes;
  }

  static Future<XFile?> compressFile({required String pathFile}) async {
    final tempDir = await getTemporaryDirectory();
    final imageCompress = await ImageCompressor.compressFileAndGetFile(
      pathFile,
      quality: FileUploadConfig.imageQuality,
      '${tempDir.path}/compressedImage.jpg',
    );
    return imageCompress;
  }

  static Future<XFile?> compressUint8List({required Uint8List data}) async {
    final tempDir = await getTemporaryDirectory();
    final imageCompress = await ImageCompressor.compressBytesAndGetFile(
      data,
      quality: FileUploadConfig.imageQuality,
      '${tempDir.path}/compressedImage.jpg',
    );
    return imageCompress;
  }
}
