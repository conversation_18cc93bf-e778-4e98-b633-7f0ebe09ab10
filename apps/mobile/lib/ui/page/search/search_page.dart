import 'package:auto_route/auto_route.dart';
import 'package:chat/chat.dart' as chat;
import 'package:flutter/material.dart';
import 'package:search/search.dart' as search;
import 'package:shared/shared.dart';

import '../../../navigation/routes/app_router.gr.dart';

@RoutePage()
class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage>
    implements search.SearchPageInterface {
  @override
  Widget build(BuildContext context) {
    return search.SearchPage(
      interface: this,
    );
  }

  @override
  void onTapChannel(String channelId, String workspaceId, String? avatar) {
    context.pushRoute(
      ChannelViewRoute(
        workspaceId: workspaceId,
        channelId: channelId,
      ),
    );
  }

  @override
  void onTapUser(String userId, String? avatar, String? displayName) {
    context.pushRoute(
      ChannelViewRoute(
        userId: userId,
        initUserData:
            chat.UserViewData(avatar: avatar, displayName: displayName),
      ),
    );
  }

  @override
  Future<void> onTapScanQR() async {
    final isGranted = await PermissionUtils.requestCameraPermission(context);
    if (isGranted) {
      context.pushRoute(const QrScannerRoute());
    }
  }
}
