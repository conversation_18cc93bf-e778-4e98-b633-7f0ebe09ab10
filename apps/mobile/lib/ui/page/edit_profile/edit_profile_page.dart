import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:user_manager/user_manager.dart' as user;
import 'package:shared/shared.dart';
import '../../../navigation/routes/app_router.gr.dart';
import '../../utils/picker_utils.dart';
import '../crop_avatar_page/crop_avatar_page.dart';
import '../take_photo/take_photo_page.dart';

@RoutePage()
class EditProfilePage extends StatefulWidget {
  const EditProfilePage({
    required this.me,
    super.key,
  });

  final user.User me;

  @override
  State<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends State<EditProfilePage>
    implements user.EditProfileInterface {
  @override
  Widget build(BuildContext context) {
    return user.EditProfilePage(
      interface: this,
      user: widget.me,
    );
  }

  @override
  Future<void> onOpenGalleryAvatar(user.User user) async {
    await _openGalleryPicker();
  }

  Future<void> _openGalleryPicker() async {
    final currentRouteName = context.router.current.name;
    await PickerUtils.pickImageFromGalleryForUpload(
      context,
      onPicked: (file) async {
        final result = await context.pushRoute<CropAvatarPopResult>(
          CropAvatarRoute(
            photo: file,
            previousRouteName: currentRouteName,
            avatarType: AvatarType.me,
          ),
        );

        if (result == null) {
          _openGalleryPicker();
        } else if (result.result == false &&
            result.action == CropAvatarPopResult.onSavedAvatar) {
          _openGalleryPicker();
        }
      },
    );
  }

  @override
  void onClickTakeAvatarPhoto(user.User user) {
    context.pushRoute(
      TakePhotoRoute(outputType: OutputType.avatar, avatarType: AvatarType.me),
    );
  }

  @override
  void onGoToViewImagePage(String? avatar) {
    context.pushRoute(
      ImageViewRoute(
        imageUrl: avatar,
      ),
    );
  }

  @override
  void onClickDeleteAccount() {
    context.pushRoute(const DeleteAccountRoute());
  }
}
