import 'package:app_core/core.dart' as core;
import 'package:auth/auth.dart' as auth;
import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../common/di/di.dart';
import '../../../domain/handler/app_initializer.dart';
import '../../../navigation/routes/app_router.dart';
import '../../../navigation/routes/app_router.gr.dart';

@RoutePage()
class AccountSelectionPage extends StatefulWidget {
  const AccountSelectionPage({super.key, required this.sessions});

  final List<auth.Session> sessions;

  @override
  AccountSelectionPageState createState() => AccountSelectionPageState();
}

class AccountSelectionPageState extends core
    .BasePageState<AccountSelectionPage, auth.InitialUserKeyAuthBloc> {
  late final core.UserSelectionBloc _userSelectionBloc;

  @override
  void initState() {
    super.initState();
    _initializeBlocs();
    _loadUserSessions();
  }

  void _initializeBlocs() {
    _userSelectionBloc = getIt<core.UserSelectionBloc>();
  }

  void _loadUserSessions() {
    _userSelectionBloc.add(
      core.LoadUsersEvent(widget.sessions.map((s) => s.sessionKey).toList()),
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return MultiBlocProvider(
      providers: _buildBlocProviders(),
      child: BlocListener<auth.InitialUserKeyAuthBloc,
          auth.InitialUserKeyAuthState>(
        listener: _authBlocListener,
        child: BlocBuilder<core.UserSelectionBloc, core.UserSelectionState>(
          builder: (context, state) => _buildUserSelectionState(state),
        ),
      ),
    );
  }

  List<BlocProvider> _buildBlocProviders() {
    return [
      BlocProvider<core.UserSelectionBloc>.value(value: _userSelectionBloc),
      BlocProvider<auth.InitialUserKeyAuthBloc>.value(value: bloc),
    ];
  }

  void _authBlocListener(
    BuildContext context,
    auth.InitialUserKeyAuthState state,
  ) {
    state.maybeWhen(
      goToAuthProgressPage: (username, challenge, req, hashCash) {
        getIt<AppRouter>().push(
          AuthProgressRoute(
            username: username,
            reqChallenge: challenge,
            request: req,
            hashCash: hashCash,
            backStack: AccountSelectionRoute.name,
          ),
        );
      },
      authSuccess: (String sessionKey, String authToken) {
        _onAuthSuccess(sessionKey, authToken);
      },
      orElse: () {},
    );
  }

  Widget _buildUserSelectionState(core.UserSelectionState state) {
    return state.when(
      initial: () => const SizedBox.shrink(),
      loaded: (users) => _buildLoadedState(users),
      error: (error) => Center(child: Text(error)),
    );
  }

  Widget _buildLoadedState(List<User> users) {
    if (users.isEmpty) {
      getIt<AppRouter>().replace(AuthRoute());
      return SizedBox.shrink();
    }

    final userMap = {for (var user in users) user.userId: user};
    final sortedSessions = _getSortedSessions();
    final _users = _mapSessionsToUsers(sortedSessions, userMap);

    return auth.AccountSelectionPage(
      accounts: _users,
      onUseAnotherAccount: () => getIt<AppRouter>().replace(const AuthRoute()),
      onDelete: (userId) =>
          _userSelectionBloc.add(core.DeleteUserEvent(userId)),
    );
  }

  List<auth.Session> _getSortedSessions() {
    return List.from(widget.sessions)
      ..sort((a, b) {
        if (a.logoutTime == null && b.logoutTime == null) return 0;
        if (a.logoutTime == null) return -1;
        if (b.logoutTime == null) return 1;
        return b.logoutTime!.compareTo(a.logoutTime!);
      });
  }

  List<ItemAccountAccountSelection> _mapSessionsToUsers(
    List<auth.Session> sortedSessions,
    Map<String, User> userMap,
  ) {
    return sortedSessions
        .map((session) {
          final user = userMap[session.sessionKey];
          return user != null
              ? ItemAccountAccountSelection(
                  id: user.userId,
                  name: user.username!,
                  url: user.profile != null
                      ? UrlUtils.parseAvatar(user.profile!.avatar)
                      : null,
                )
              : null;
        })
        .whereType<ItemAccountAccountSelection>()
        .toList();
  }

  void _onAuthSuccess(String sessionKey, String token) {
    getIt<AppInitializer>().initModules(sessionKey, token);
    getIt<InitialUserBloc>()
        .add(InitialUser(userId: sessionKey, token: sessionKey));
    _goToHomePage();
  }

  Future<void> _goToHomePage() async {
    getIt<AppRouter>().replaceAll([HomeRoute()]);
  }
}
