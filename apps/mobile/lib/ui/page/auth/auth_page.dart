import 'package:app_core/core.dart' as core;
import 'package:auth/auth.dart' as auth;
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart' as user;

import '../../../common/di/di.dart';
import '../../../domain/handler/app_initializer.dart';
import '../../../navigation/routes/app_router.dart';
import '../../../navigation/routes/app_router.gr.dart';

@RoutePage()
class AuthPage extends StatefulWidget {
  const AuthPage({super.key});

  @override
  AuthPageState createState() {
    return AuthPageState();
  }
}

class AuthPageState
    extends core.BasePageState<AuthPage, auth.InitialUserKeyAuthBloc> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocListener<auth.InitialUserKeyAuthBloc,
        auth.InitialUserKeyAuthState>(
      listener: (context, state) {
        state.maybeWhen(
          goToAuthProgressPage: (username, challenge, req, hashCash) {
            getIt<AppRouter>().push(
              AuthProgressRoute(
                username: username,
                reqChallenge: challenge,
                request: req,
                hashCash: hashCash,
                backStack: AuthRoute.name,
              ),
            );
          },
          authSuccess: (String sessionKey, String authToken) {
            _onAuthSuccess(sessionKey, authToken);
          },
          orElse: () {
            LoadingOverlayHelper.hideLoading(context);
          },
        );
      },
      child: auth.AuthPage(
        onPop: _onPop,
        onPopBack: _onPopBack,
        onHideDialog: _onHideDialog,
      ),
    );
  }

  void _onPop() {
    GetIt.instance.get<AppRouter>().popUntilRouteWithName(AuthRoute.name);
  }

  void _onPopBack() {
    GetIt.instance.get<AppRouter>().popUtilOrPush(WelcomeLastRoute.name);
  }

  void _onHideDialog() {
    GetIt.instance.get<AppRouter>().maybePop();
  }

  void _onAuthSuccess(String sessionKey, String token) {
    FocusScope.of(context).requestFocus(FocusNode());
    getIt<AppInitializer>().initModules(sessionKey, token);
    getIt<user.InitialUserBloc>()
        .add(user.InitialUser(userId: sessionKey, token: sessionKey));
    _goToHomePage();
  }

  Future<void> _goToHomePage() async {
    getIt<AppRouter>().replaceAll([HomeRoute()]);
  }
}
