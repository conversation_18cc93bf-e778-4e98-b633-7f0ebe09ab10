import 'dart:async';
import 'dart:io';

import 'package:apns_notification/apns_notification.dart';
import 'package:app_core/core.dart' as core;
import 'package:auth/auth.dart' as auth;
import 'package:auto_route/auto_route.dart';
import 'package:call/call.dart' as call;
import 'package:chat/chat.dart'
    show
        UpdateAllPendingMessagesToFailedUseCase,
        UpdateAllPendingMessagesToFailedInput;
import 'package:chat/chat.dart' as chat;
import 'package:fcm_notification/fcm_notification.dart';
import 'package:flutter/material.dart';
import 'package:search/search.dart' as search;
import 'package:share_to/share_to.dart' as shareTo;
import 'package:shared/shared.dart';
import 'package:sticker/sticker.dart' as sticker;
import 'package:upload_manager/upload_manager.dart' as upload;
import 'package:user_manager/user_manager.dart' as user;
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../common/di/di.dart';
import '../../../domain/handler/app_initializer.dart';
import '../../../domain/handler/upload_avatar_handler.dart';
import '../../../navigation/routes/app_router.dart';
import '../../../navigation/routes/app_router.gr.dart';
import '../../utils/picker_utils.dart';
import '../crop_avatar_page/crop_avatar_page.dart';
import '../crop_cover_page/crop_cover_page.dart';
import '../take_photo/take_photo_page.dart';

@RoutePage()
class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage>
    implements user.MeProfileInterface {
  ValueNotifier<bool> _isLoadingNewAvatar = ValueNotifier(false);

  @override
  Widget build(BuildContext context) {
    return user.MeProfilePage(
      interface: this,
      isLoadingNewAvatar: _isLoadingNewAvatar,
      updateAvatarUser: handleUpdateAvatarUser,
    );
  }

  @override
  void onClickAppearance() {
    context.pushRoute(const AppearanceRoute());
  }

  @override
  void onClickLanguage() {
    context.pushRoute(const LanguageRoute());
  }

  @override
  void onClickPrivacyAndSecurity(user.User user) {
    context.pushRoute(PrivacyAndSecurityRoute(user: user));
  }

  void handleUpdateAvatarUser(upload.UploadFile file) async {
    await getIt<UploadAvatarHandler>().onUpdateAvatar(
      file,
      userId: auth.Config.getInstance().activeSessionKey,
      onSuccessUpdate: (bool result) {
        _isLoadingNewAvatar.value = false;
      },
    );
  }

  @override
  Future<void> onLogoutSuccess() async {
    updatePendingMessage();
    chat.Config.getInstance().authData = null;
    user.Config.getInstance().authData = null;
    auth.Config.getInstance().authData = null;
    search.Config.getInstance().authData = null;
    sticker.Config.getInstance().authData = null;
    core.Config.getInstance().authData = null;
    shareTo.Config.getInstance().authData = null;
    upload.Config.getInstance().authData = null;
    call.Config.getInstance().authData = null;
    upload.Config.getInstance().authData = null;
    getIt<shareTo.ShareTo>().close();
    getIt<shareTo.DeleteSuggestion>().deleteSuggestion();
    getIt<core.DisconnectWebsocketUseCase>()
        .execute(core.DisconnectWebsocketInput());

    if (Platform.isIOS) {
      ApnsNotification()
        ..setBadgeNotification(0)
        ..clearAllNotification();
    } else {
      FCMNotification().clearAllNotification();
    }
    getIt<AppRouter>().replaceAll(
      [WelcomeLastRoute(invalidToken: false)],
      updateExistingRoutes: false,
    );
    getIt<AppInitializer>().dispose();
  }

  Future<void> updatePendingMessage() async {
    unawaited(core.IsolateTaskService().clean());
    unawaited(
      getIt<UpdateAllPendingMessagesToFailedUseCase>()
          .execute(UpdateAllPendingMessagesToFailedInput()),
    );
  }

  @override
  void onClickEditProfile(user.User user) {
    context.pushRoute(
      EditProfileRoute(
        me: user,
      ),
    );
  }

  @override
  void onClickNotification() {
    context.pushRoute(NotificationRoute());
  }

  @override
  void onClickShareProfile(user.User me) {
    ui.BottomSheetUtil.showDefaultBottomSheet(
      context: context,
      child: user.MyQrBottomSheet(),
    );
  }

  @override
  Future<void> onClickTapOpenGallery(user.User me) async {
    _openGalleryPickerCropCover(me);
  }

  Future<void> _openGalleryPickerCropCover(user.User me) async {
    await PickerUtils.pickImageFromGalleryForUpload(
      context,
      onPicked: (file) async {
        final result = await context.pushRoute<CropCoverPopResult>(
          CropCoverRoute(photo: file, user: me, avatarType: AvatarType.me),
        );

        if (result == null) {
          _openGalleryPickerCropCover(me);
        } else if (result.result == false &&
            result.action == CropCoverPopResult.onSavedCover) {
          _openGalleryPickerCropCover(me);
        }
      },
    );
  }

  Future<void> _openGalleryPickerCropAvatar() async {
    final currentRouteName = context.router.current.name;
    await PickerUtils.pickImageFromGalleryForUpload(
      context,
      onPicked: (file) async {
        final result = await context.pushRoute<CropAvatarPopResult>(
          CropAvatarRoute(
            photo: file,
            previousRouteName: currentRouteName,
            avatarType: AvatarType.me,
          ),
        );

        if (result == null) {
          _openGalleryPickerCropAvatar();
        } else if (result.result == false &&
            result.action == CropAvatarPopResult.onSavedAvatar) {
          _openGalleryPickerCropAvatar();
        }
      },
    );
  }

  @override
  void onClickTakePhoto(user.User me) {
    context.pushRoute(
      TakePhotoRoute(
        outputType: OutputType.cover,
        user: me,
        avatarType: AvatarType.me,
      ),
    );
  }

  @override
  void onClickTakeAvatarPhoto(user.User me) {
    context.pushRoute(TakePhotoRoute(user: me, avatarType: AvatarType.me));
  }

  @override
  Future<void> onOpenGalleryAvatar(user.User me) async {
    await _openGalleryPickerCropAvatar();
  }

  @override
  void onClickListBlockedUser() {
    context.pushRoute(BlockUsersRoute());
  }

  @override
  void onTapFullScreenImage(String imagePath) {
    context.pushRoute(ImageViewRoute(imageUrl: imagePath));
  }

  @override
  void onAddStatus({
    void Function(String content, String emoji, int statusDuration)?
        onAddStatusSuccessful,
  }) {
    getIt<core.UserStatusHandler>().showAddStatusBottomSheet(
      context,
      onCancel: context.maybePop,
      onAddStatusSuccessful: (content, emoji, statusDuration) async {
        await context.maybePop();
        onAddStatusSuccessful?.call(content, emoji, statusDuration);
      },
    );
  }

  @override
  void onUpdateStatus({
    String content = '',
    String emoji = '',
    int keepStatusDuration = 0,
    void Function(String content, String emoji, int statusDuration)?
        onUpdateStatusSuccessful,
  }) {
    getIt<core.UserStatusHandler>().showUpdateStatusBottomSheet(
      context,
      initContent: content,
      initEmoji: emoji,
      statusDuration: keepStatusDuration,
      onCancel: context.maybePop,
      onUpdateStatusSuccessful: (content, emoji, statusDuration) async {
        AppEventBus.publish(OnGoToHomeEvent(tab: HomeTab.profile));
        onUpdateStatusSuccessful?.call(content, emoji, statusDuration);
      },
    );
  }

  @override
  void onDeleteStatus({void Function()? onDeleteStatusSuccessful}) {
    getIt<core.UserStatusHandler>().showDeleteStatusActionSheet(
      context,
      onCancel: context.maybePop,
      onDeleteStatusSuccessful: (_, __, ___) async {
        AppEventBus.publish(OnGoToHomeEvent(tab: HomeTab.profile));
        onDeleteStatusSuccessful?.call();
      },
    );
  }
}
