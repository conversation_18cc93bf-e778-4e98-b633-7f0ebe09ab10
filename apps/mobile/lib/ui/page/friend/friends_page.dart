import 'package:auto_route/auto_route.dart';
import 'package:chat/chat.dart' as chat;
import 'package:flutter/material.dart';
import 'package:ziichat_ui/src/common/classes/friend_item.dart';

import '../../../navigation/routes/app_router.gr.dart';

@RoutePage()
class FriendsPage extends StatefulWidget {
  const FriendsPage({super.key});

  @override
  State<FriendsPage> createState() => _FriendsPageState();
}

class _FriendsPageState extends State<FriendsPage>
    implements chat.FriendListInterface {
  @override
  Widget build(BuildContext context) {
    return chat.FriendListPage(
      friendListInterface: this,
    );
  }

  @override
  void onClickFriendItem(FriendItem friendItem) {
    context.pushRoute(ChannelViewRoute(userId: friendItem.userId));
  }

  @override
  void onClickFriendRequest() {
    context.pushRoute(const FriendRequestRoute());
  }
}
