import 'dart:typed_data';

import 'package:auto_route/auto_route.dart';
import 'package:download_manager/download_manager.dart';
import 'package:flutter/material.dart';
import 'package:saver_gallery/saver_gallery.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../common/di/di.dart';
import '../../../domain/handler/download/download_handler.dart';

@RoutePage()
class ImageViewPage extends StatefulWidget {
  const ImageViewPage({
    super.key,
    this.imageData,
    this.imageUrl,
  }) : assert(imageData != null || imageUrl != null);

  final Uint8List? imageData;
  final String? imageUrl;

  @override
  State<ImageViewPage> createState() => _ImageViewPageState();
}

class _ImageViewPageState extends State<ImageViewPage> {
  @override
  Widget build(BuildContext context) {
    return ui.ImageViewPage(
      imageUrl: widget.imageUrl,
      imageData: widget.imageData,
      viewAvatarAppbarInterface: ViewAvatarAppbarWidgetImplementation(
        context: context,
        onSaveToGallery: _onSaveAvatarToGallery,
      ),
    );
  }

  Future<void> _onSaveAvatarToGallery() async {
    final isGranted =
        await PermissionUtils.requestSaveToGalleryPermission(context);
    if (isGranted) {
      if (widget.imageData != null) {
        await SaverGallery.saveImage(
          widget.imageData!,
          fileName: 'IMG_${DateTime.now().millisecondsSinceEpoch}.png',
          androidRelativePath: "Pictures/",
          skipIfExists: false,
        );
      }
      if (widget.imageUrl != null) {
        await getIt<DownloadHandler>()
            .download(widget.imageUrl!, type: DownloadSharedStorage.images);
      }
    }
  }
}

class ViewAvatarAppbarWidgetImplementation
    extends ui.ViewAvatarAppbarWidgetInterface {
  ViewAvatarAppbarWidgetImplementation({
    required this.context,
    required this.onSaveToGallery,
  });

  final BuildContext context;
  final VoidCallback onSaveToGallery;

  @override
  void onBackButtonClicked() {
    context.back();
  }

  @override
  void onDownloadButtonClicked() {
    onSaveToGallery();
  }
}
