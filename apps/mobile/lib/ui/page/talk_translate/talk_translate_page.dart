import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:sticker/sticker.dart';
import 'package:talk_translate/talk_translate.dart' as tat;

@RoutePage()
class TalkTranslatePage extends StatefulWidget {
  const TalkTranslatePage({super.key});

  @override
  State<TalkTranslatePage> createState() => _TalkTranslatePageState();
}

class _TalkTranslatePageState extends State<TalkTranslatePage>
    implements tat.TalkTranslateInterface {
  @override
  Widget build(BuildContext context) {
    return tat.TalkTranslatePage(
      interface: this,
      myLocale: Locale(AppLocalizations.of(context)!.localeName),
    );
  }

  @override
  Widget lottieWidget() {
    return Padding(
      padding: EdgeInsets.only(bottom: 250.h),
      child: StickerWidget.fromAsset(
        assetName: 'assets/lottie_files/Liquid.json',
        size: StickerSize.x512,
      ),
    );
  }
}
