import 'package:auto_route/auto_route.dart';
import 'package:chat/chat.dart' as chat;
import 'package:chat/chat.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

import '../../../common/di/di.dart';
import '../../../navigation/routes/app_router.dart';
import '../../../navigation/routes/app_router.gr.dart';

@RoutePage()
class FullscreenViewPage extends StatefulWidget {
  const FullscreenViewPage({
    super.key,
    this.workspaceId,
    this.channelId,
    this.userId,
    this.attachmentIndex,
    this.messageId,
  });

  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final String? messageId;
  final int? attachmentIndex;

  @override
  State<FullscreenViewPage> createState() => _FullscreenViewPageState();
}

class _FullscreenViewPageState extends State<FullscreenViewPage> {
  final BuildContext globalContext =
      getIt<AppRouter>().navigatorKey.currentContext!;
  @override
  void initState() {
    super.initState();
  }

  var isNavigatingToOther = false;

  @override
  Widget build(BuildContext context) {
    return chat.FullscreenMessageListView(
      workspaceId: widget.workspaceId,
      channelId: widget.channelId,
      userId: widget.userId,
      goToDMMessage: goToDMMessage,
      goToViewAvatar: goToViewAvatar,
      goToMessageList: onBackButtonClicked,
      messageId: widget.messageId,
      attachmentIndex: widget.attachmentIndex,
      goChannelInfo: goChannelInfo,
      globalContext: globalContext,
    );
  }

  void onBackButtonClicked() {
    context.maybePop();
  }

  void goToDMMessage(String userId) {
    context.pushRoute(ChannelViewRoute(userId: userId));
  }

  void goToViewAvatar(String avatarUrl) {
    context.pushRoute(ImageViewRoute(imageUrl: avatarUrl));
  }

  void goChannelInfo(Channel? channel) {
    if (widget.userId != null) {
      _navigateToOther(
        () => GetIt.instance
            .get<AppRouter>()
            .navigatorKey
            .currentContext!
            .replaceRoute(
              UserProfileRoute(
                workspaceId: widget.workspaceId,
                channelId: widget.channelId,
                userId: widget.userId!,
              ),
            ),
      );
    } else {
      if (channel != null) {
        _navigateToOther(
          () => GetIt.instance
              .get<AppRouter>()
              .navigatorKey
              .currentContext!
              .replaceRoute(
                ChannelInfoRoute(channel: channel),
              ),
        );
      }
    }
  }

  Future<void> _navigateToOther(Future<void> Function() function) async {
    if (isNavigatingToOther) return;
    isNavigatingToOther = true;
    await function();
    isNavigatingToOther = false;
  }
}
