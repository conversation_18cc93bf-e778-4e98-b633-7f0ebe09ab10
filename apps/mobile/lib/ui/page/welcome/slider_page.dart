import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../navigation/routes/app_router.gr.dart';
import '../../widgets/app_pop_scope.dart';

@RoutePage()
class SliderPage extends StatelessWidget {
  const SliderPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppPopScope(
      child: ui.SliderPage(
        onFinalSlide: () {
          AutoRouter.of(context).push(WelcomeLastRoute(invalidToken: false));
        },
      ),
    );
  }
}
