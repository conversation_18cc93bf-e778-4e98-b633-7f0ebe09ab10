import 'dart:async';
import 'dart:io';

import 'package:apns_notification/apns_notification.dart';
import 'package:app_core/core.dart';
import 'package:auth/auth.dart' as auth;
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:share_to/share_to.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../common/di/di.dart';
import '../../../domain/handler/app_initializer.dart';
import '../../../domain/handler/deep_link_handler.dart';
import '../../../domain/handler/download/download_handler.dart';
import '../../../domain/handler/update_handler.dart';
import '../../../navigation/routes/app_router.dart';
import '../../../navigation/routes/app_router.gr.dart';
import '../../../navigation/routes/app_tabs_router.dart';
import '../../widgets/app_pop_scope.dart';
import '../share_to/share_to_page.dart';
import '../withTransition.dart';

@RoutePage()
class HomePage extends StatefulWidget implements PageWithTransition {
  static int CURRENT_INDEX = 1;
  static const int CHANNEL_LIST_INDEX = 1;
  final int initialPage;
  final bool onOpenApp;
  @override
  final bool withTransition;

  const HomePage({
    Key? key,
    this.initialPage = 1,
    this.onOpenApp = false,
    this.withTransition = true,
  }) : super(key: key);

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with WidgetsBindingObserver {
  final ValueNotifier<bool> hasNotification = ValueNotifier(false);
  final ValueNotifier<int> friendsCountNew = ValueNotifier(0);
  final ValueNotifier<int> messagesCountNew = ValueNotifier(0);
  final ValueNotifier<String?> avatarNotifier = ValueNotifier<String?>(null);
  StreamSubscription<User?>? _userStreamSubscription;
  StreamSubscription? _hasNotificationSubscription;
  StreamSubscription? _quantityFriendRequestSubscription;
  StreamSubscription? _quantityUnreadMessageSubscription;
  StreamSubscription? _goToMeProfileSubscription;
  late bool _onOpenApp;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    HomePage.CURRENT_INDEX = widget.initialPage;
    _onOpenApp = widget.onOpenApp;

    _setupGoToMeProfileSubscription();
    _setupVisitedProfile();
    _setupDownloadHandler();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      getIt<AppInitializer>().initialize();

      /// add show bottom sheet share to when logout after login
      _pendingReceivedFromShareTo();

      /// add show bottom sheet share to when logout after login
      _pendingReceivedFromSendTo();

      _listenReceiveFromShareTo();
      _listenToMeStream();
      _setupNotificationListeners();
      _requestNotificationPermission();
    });

    unawaited(UpdateHandler.checkForUpdate());
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      PresenceHandler().sendPresenceEvent();
    }

    if (state == AppLifecycleState.inactive) {
      PresenceHandler().sendPresenceEvent().then((count) {
        if (Platform.isIOS && count != null) {
          ApnsNotification().setBadgeNotification(count);
        }
      });
    }
  }

  void _setupVisitedProfile() {
    getIt<MeProfileBloc>().add(CheckDifferentVisitedProfileEvent());
    _hasNotificationSubscription = getIt<AppEventBus>()
        .on<HasNotificationEvent>()
        .listen(_onReceivedHasNotificationEvent);
  }

  void _onReceivedHasNotificationEvent(event) async {
    if (event is HasNotificationEvent) {
      hasNotification.value = event.hasNotification;
    }
  }

  void _setupGoToMeProfileSubscription() {
    _goToMeProfileSubscription =
        getIt<AppEventBus>().on<OnGoToHomeEvent>().listen((event) {
      if (!mounted) return;
      if (event.tab != null) {
        goToTab(
          switch (event.tab!) {
            HomeTab.search => 0,
            HomeTab.calls => 4, // Hidden for current period
            HomeTab.chats => 1,
            HomeTab.friends => 2,
            HomeTab.profile => 3,
          },
        );
      }
      context.router.popUntil((route) => route.settings.name == HomeRoute.name);
    });
  }

  void _pendingReceivedFromShareTo() {
    ShareToFile? shareToFile = getIt<PendingIntentHandler>().shareToFile;
    if (shareToFile == null || shareToFile.listMediaFile.isEmpty) return;

    if (auth.Config.getInstance().activeSessionKey == null) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        getIt<AppRouter>().replaceAll([
          WelcomeLastRoute(invalidToken: false),
          const AuthRoute(),
        ]);
      });
      return;
    }

    if (Platform.isAndroid && shareToFile.isLinkOnly()) {
      GetIt.instance
          .get<AppTabsRouter>()
          .setActiveIndex(HomePage.CHANNEL_LIST_INDEX);
      HomePage.CURRENT_INDEX = HomePage.CHANNEL_LIST_INDEX;
      final handler = getIt<DeepLinkHandler>();
      handler.handleDeepLink(Uri.parse(shareToFile.getUrl()!));
      return;
    }

    getIt<AppRouter>().popUntilRouteWithName(HomeRoute.name);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!getContext().mounted) return;
      ui.BottomSheetUtil.showDefaultBottomSheet(
        context: getContext(),
        child: ShareToPage(sharedFiles: shareToFile),
      );
      GetIt.instance
          .get<AppTabsRouter>()
          .setActiveIndex(HomePage.CHANNEL_LIST_INDEX);
      HomePage.CURRENT_INDEX = HomePage.CHANNEL_LIST_INDEX;
    });
  }

  BuildContext getContext() => getIt<AppRouter>().navigatorKey.currentContext!;

  void _pendingReceivedFromSendTo() {
    Uri? uri = getIt<PendingIntentHandler>().uri;
    if (Platform.isIOS && uri != null) {
      final handler = getIt<DeepLinkHandler>();
      handler.handleDeepLink(uri);
    }
  }

  void _listenReceiveFromShareTo() {
    getIt<ShareTo>().listen(_onReceivedFromShareTo);
  }

  void _onReceivedFromShareTo(ShareToFile value) {
    if (value.listMediaFile.isEmpty) return;

    if (auth.Config.getInstance().activeSessionKey == null) {
      getIt<AppRouter>().replaceAll([
        WelcomeLastRoute(invalidToken: false),
        const AuthRoute(),
      ]);
      return;
    }

    if (Platform.isAndroid && value.isLinkOnly()) {
      GetIt.instance
          .get<AppTabsRouter>()
          .setActiveIndex(HomePage.CHANNEL_LIST_INDEX);
      HomePage.CURRENT_INDEX = HomePage.CHANNEL_LIST_INDEX;
      final handler = getIt<DeepLinkHandler>();
      handler.handleDeepLink(Uri.parse(value.getUrl()!));
      return;
    }

    getIt<AppRouter>().popUntilRouteWithName(HomeRoute.name);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!getContext().mounted) return;
      ui.BottomSheetUtil.showDefaultBottomSheet(
        context: getContext(),
        child: ShareToPage(sharedFiles: value),
      );
    });
  }

  void goToTab(int index) {
    getIt<AppTabsRouter>().setActiveIndex(index);
    HomePage.CURRENT_INDEX = index;
  }

  void _requestNotificationPermission() {
    PermissionUtils.requestNotificationPermission(context);
  }

  void _listenToMeStream() {
    final getMeOutput =
        getIt<GetMeStreamUseCase>().execute(const GetMeStreamInput());

    _userStreamSubscription = getMeOutput.userStream.listen((user) {
      final newAvatarUrl = UrlUtils.parseAvatar(user?.profile?.avatar);
      if (newAvatarUrl != avatarNotifier.value) {
        avatarNotifier.value = newAvatarUrl;
      }
    });
  }

  void _setupNotificationListeners() {
    _hasNotificationSubscription =
        getIt<AppEventBus>().on<HasNotificationEvent>().listen((event) {
      hasNotification.value = event.hasNotification;
    });

    _quantityFriendRequestSubscription =
        getIt<AppEventBus>().on<QuantityFriendRequestEvent>().listen((event) {
      friendsCountNew.value = event.number;
    });
    _quantityUnreadMessageSubscription =
        getIt<AppEventBus>().on<QuantityUnReadMessageEvent>().listen((event) {
      messagesCountNew.value = event.number;
    });
  }

  void _setupDownloadHandler() {
    getIt<DownloadHandler>().setupDownloadHandler();
  }

  @override
  void dispose() {
    getIt<AppInitializer>().dispose();
    _userStreamSubscription?.cancel();
    _hasNotificationSubscription?.cancel();
    _quantityFriendRequestSubscription?.cancel();
    _quantityUnreadMessageSubscription?.cancel();
    _goToMeProfileSubscription?.cancel();
    _hasNotificationSubscription?.cancel();
    getIt<DownloadHandler>().dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_onOpenApp) {
      return _buildHomePage();
    }

    _onOpenApp = false;
    return ui.SplashPage(nextPage: _buildHomePage());
  }

  AutoTabsRouter _buildHomePage() {
    return AutoTabsRouter(
      routes: const [
        SearchRoute(),
        // CallLogsRoute(), // Hidden for current period
        ChannelsRoute(),
        FriendsRoute(),
        ProfileRoute(),
      ],
      lazyLoad: true,
      homeIndex: widget.initialPage,
      builder: (context, child) {
        final tabsRouter = AutoTabsRouter.of(context);
        getIt<AppTabsRouter>().setTabsRouter(tabsRouter);

        return AppPopScope(
          child: ui.AppScaffold(
            resizeToAvoidBottomInset: false,
            systemUIBottomColor: Colors.transparent,
            systemUIColor: Colors.transparent,
            hasSafeArea: false,
            body: child,
            bottomNavigationBar: ui.ZiichatBottomNavigationBar(
              interface: _BottomNavigationInterface(
                avatarNotifier: avatarNotifier,
                context: context,
              ),
              bottomNavigationBarCallsCountNew: ValueNotifier(0),
              bottomNavigationBarLastMessagesCountNew: messagesCountNew,
              bottomNavigationBarFriendsCountNew: friendsCountNew,
              hasNotificationUser: hasNotification,
            ),
          ),
        );
      },
    );
  }
}

class _BottomNavigationInterface
    implements ui.ZiichatBottomNavigationBarInterface {
  final ValueNotifier<String?> avatarNotifier;
  final BuildContext context;

  _BottomNavigationInterface({
    required this.avatarNotifier,
    required this.context,
  });

  @override
  void onSearchIconPressed() {
    FocusScope.of(context).requestFocus(FocusNode());
    getIt<AppTabsRouter>().setActiveIndex(0);
    HomePage.CURRENT_INDEX = 0;
  }

  @override
  void onVideoCallIconPressed() {
    // Todos hidden for current period

    // FocusScope.of(context).requestFocus(FocusNode());
    // getIt<AppTabsRouter>().setActiveIndex(1);
    // HomePage.CURRENT_INDEX = 1;
  }

  @override
  void onMessageIconPressed() {
    FocusScope.of(context).requestFocus(FocusNode());
    getIt<AppTabsRouter>().setActiveIndex(1);
    HomePage.CURRENT_INDEX = 1;
  }

  @override
  void onFriendsIconPressed() {
    FocusScope.of(context).requestFocus(FocusNode());
    getIt<AppTabsRouter>().setActiveIndex(2);
    HomePage.CURRENT_INDEX = 2;
  }

  @override
  void onUserProfileIconPressed() {
    FocusScope.of(context).requestFocus(FocusNode());
    getIt<AppTabsRouter>().setActiveIndex(3);
    HomePage.CURRENT_INDEX = 3;
  }

  @override
  int selectedIndex() {
    return getIt<AppTabsRouter>().activeIndex;
  }

  @override
  ValueNotifier<String?> getAvatarNotifier() {
    return avatarNotifier;
  }
}
