import 'dart:typed_data';

import 'package:flutter_local_notifications/flutter_local_notifications.dart';

import '../../data/notification_data.dart';
import '../handler/notification/notification_avatar_handler.dart';
import '../handler/notification/notification_channel_handler.dart';

class AndroidMessageNotificationBuilder {
  static Future<AndroidNotificationDetails> createNotificationDetails(
    NotificationData data,
  ) async {
    final String channelId = data.channelId!;
    final String channelName =
        data.hasValidChannelName() ? data.channelName! : 'Messages';

    final Uint8List? largeIcon = (data.getValidAvatar() != null)
        ? await NotificationAvatarHandler()
            .getRoundedAvatar(data.getValidAvatar()!)
        : null;

    final styleInformation = await _buildStyleInformation(channelId, data);

    return AndroidNotificationDetails(
      channelId,
      channelName,
      importance: Importance.high,
      priority: Priority.high,
      playSound: true,
      sound: RawResourceAndroidNotificationSound('notification'),
      groupKey: channelId,
      setAsGroupSummary: true,
      autoCancel: true,
      styleInformation: styleInformation,
      enableLights: true,
      enableVibration: true,
      largeIcon: largeIcon != null ? ByteArrayAndroidBitmap(largeIcon) : null,
      vibrationPattern: Int64List.fromList([0, 500, 1000, 500]),
    );
  }

  static Future<MessagingStyleInformation> _buildStyleInformation(
    String channelId,
    NotificationData data,
  ) async {
    final channelManager = NotificationChannelHandler();

    print('Retrieving existing messages for channelId: $channelId...');

    final existingStyle =
        await channelManager.getNotificationMessageByChannelId(channelId);

    Person person = await _buildPerson(data);

    final List<Message> messages = [];

    if (existingStyle != null) {
      print('Existing messages found, adding to list.');
      existingStyle.messages?.forEach((item) {
        messages.add(Message(item.text, item.timestamp, item.person));
      });
    } else {
      print('No existing messages found.');
    }
    print('Adding new message to the list.');

    messages.add(
      Message(
        await data.getReplacementContent(),
        DateTime.now(),
        person,
      ),
    );

    return MessagingStyleInformation(
      await _buildPerson(data),
      conversationTitle: data.channelName,
      groupConversation: true,
      messages: messages,
    );
  }

  static Future<Person> _buildPerson(NotificationData data) async {
    final String? validAvatar = data.getValidAvatar();
    if (validAvatar != null) {
      final Uint8List? roundedAvatar =
          await NotificationAvatarHandler().getRoundedAvatar(validAvatar);
      return Person(
        name: data.isDm() ? data.channelName : data.username,
        key: data.userId,
        icon:
            roundedAvatar != null ? ByteArrayAndroidIcon(roundedAvatar) : null,
      );
    }

    return Person(
      name: data.isDm() ? data.channelName : data.username,
      key: data.userId,
      icon: FlutterBitmapAssetAndroidIcon('assets/icons/ic_user_null.png'),
    );
  }
}
