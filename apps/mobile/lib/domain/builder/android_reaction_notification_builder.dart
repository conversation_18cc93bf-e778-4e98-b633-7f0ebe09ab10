import 'dart:typed_data';

import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared/shared.dart';

import '../../data/notification_data.dart';
import '../handler/notification/notification_avatar_handler.dart';
import '../handler/notification/notification_translation_handler.dart';

class AndroidReactionNotificationBuilder {
  static Future<AndroidNotificationDetails> createNotificationDetails(
    NotificationData data,
  ) async {
    final String channelId = GlobalConfig.REACTIONS_CHANNEL_ID;
    final String channelName =
        NotificationTranslationHandler().appLocalizations.reactions;

    final Uint8List? largeIcon = (data.getValidAvatar() != null)
        ? await NotificationAvatarHandler()
            .getRoundedAvatar(data.getValidAvatar()!)
        : null;

    final styleInformation = await _buildStyleInformation(data);

    return AndroidNotificationDetails(
      channelId,
      channelName,
      importance: Importance.high,
      priority: Priority.high,
      playSound: true,
      sound: RawResourceAndroidNotificationSound('notification'),
      styleInformation: styleInformation,
      enableLights: true,
      enableVibration: true,
      largeIcon: largeIcon != null
          ? ByteArrayAndroidBitmap(largeIcon)
          : data.isDm()
              ? DrawableResourceAndroidBitmap('ic_user_null')
              : DrawableResourceAndroidBitmap('ic_channel_null'),
      vibrationPattern: Int64List.fromList([0, 500, 1000, 500]),
      autoCancel: true,
    );
  }

  static Future<MessagingStyleInformation> _buildStyleInformation(
    NotificationData data,
  ) async {
    final Person reactingPerson = await _buildPerson(data);

    return MessagingStyleInformation(
      reactingPerson,
      conversationTitle: data.channelName ?? 'Reaction Notification',
      groupConversation: false,
      messages: [
        Message(
          await data.getReplacementContent(),
          DateTime.now(),
          reactingPerson,
        ),
      ],
    );
  }

  static Future<Person> _buildPerson(NotificationData data) async {
    final String? validAvatar = data.getValidAvatar();

    if (validAvatar != null) {
      final Uint8List? roundedAvatar =
          await NotificationAvatarHandler().getRoundedAvatar(validAvatar);
      return Person(
        name: data.username,
        key: data.userId,
        icon:
            roundedAvatar != null ? ByteArrayAndroidIcon(roundedAvatar) : null,
      );
    }

    return Person(
      name: data.username,
      key: data.userId,
      icon: DrawableResourceAndroidIcon('ic_user_null'),
    );
  }
}
