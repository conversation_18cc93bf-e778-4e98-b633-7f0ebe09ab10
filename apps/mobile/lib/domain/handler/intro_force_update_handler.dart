import 'dart:io';

import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';

import '../../navigation/routes/app_router.dart';
import '../../navigation/routes/app_router.gr.dart';

class IntroForceUpdateHandler {
  final _prep = GetIt.I.get<AppPreferences>();

  /// Hàm kiểm tra và hiển thị giới thiệu cập nhật bắt buộc
  Future<bool> isShowIntro({bool? isAuthenticated, bool? isShow = true}) async {
    final remoteConfig = FirebaseRemoteConfigService();
    for (int i = 0; i < 10; i++) {
      if (await remoteConfig.fetch()) {
        break;
      }
      await Future.delayed(Duration(seconds: 1));
    }
    final versionAppstoreIntro =
        remoteConfig.getString("store_migrate_version");
    int versionCHPlayIntro = remoteConfig.getInt("chplay_migrate_build_number");
    final versionTestflightIntro =
        remoteConfig.getString("testflight_migrate_version");
    final appVersion = AppInfoUtils.version;

    if (!((Platform.isIOS &&
            (appVersion.compareTo(versionTestflightIntro) == 0 ||
                appVersion.compareTo(versionAppstoreIntro) == 0)) ||
        (Platform.isAndroid &&
            AppInfoUtils.buildNumber.compareTo(versionCHPlayIntro.toString()) ==
                0))) {
      return false;
    }

    if (await _prep.isFirstTimeOpenAppAfterMigration() != null) {
      return false;
    }

    _prep.setFirstTimeOpenAppAfterMigration(true);
    AppEventBus.publish(CallImprovePasskeyEvent(callPasskey: false));

    if (isShow != false) {
      if (GetIt.instance.get<AppPreferences>().isFirstTimeInitApp &&
          _hasWelcomeFirstInStack()) {
        AppEventBus.publish(
          AppUpdateEvent(updateType: AppUpdateType.introduce),
        );
      }
      displayIntroduceForceUpdate(isAuthenticated: isAuthenticated ?? false);
    }
    return true;
  }

  /// Hàm kiểm tra xem có route WelcomeFirst trong stack không
  bool _hasWelcomeFirstInStack() {
    return GetIt.instance
        .get<AppRouter>()
        .stack
        .any((route) => route.routeData.name == WelcomeFirstRoute.name);
  }

  /// Hàm hiển thị giới thiệu cập nhật bắt buộc
  void displayIntroduceForceUpdate({bool? isAuthenticated}) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      GetIt.instance.get<AppRouter>().popUntilRoot();
      GetIt.instance.get<AppRouter>().replaceAll([
        IntroductionFirstRoute(
          isAuthenticated: isAuthenticated ?? false,
        ),
      ]);
    });
  }
}
