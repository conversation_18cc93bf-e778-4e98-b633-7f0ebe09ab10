import 'dart:async';

import 'package:app_core/core.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';

import '../../common/di/di.dart';

class MeInfoHandler {
  MeInfoHandler();

  late StreamSubscription? _meInfoSubscription;

  void setupMeInfoHandler() {
    _meInfoSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<MeInfoEvent>()
        .listen(_onReceivedFromMeInfoEvent);
  }

  void dispose() {
    _meInfoSubscription?.cancel();
  }

  void _onReceivedFromMeInfoEvent(event) async {
    if (event is MeInfoUpdatedEvent) {
      await _meInfoUpdated();
    }
  }

  Future<void> _meInfoUpdated() async {
    await getIt<RenderMeUseCase>().execute(
      RenderMeInput(),
    );
  }
}
