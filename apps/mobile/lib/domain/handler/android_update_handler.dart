import 'dart:io'; // For exit(0)

import 'package:flutter/foundation.dart'; // For kDebugMode
import 'package:in_app_update/in_app_update.dart';
import 'package:shared/shared.dart';

/// Class to handle all Android in-app update logic
class AndroidUpdateHandler {
  /// Main method to check for updates and automatically handle them.
  ///
  /// Behavior:
  /// 1. If running in debug mode, do nothing (skip update checks).
  /// 2. If no update is available, return immediately.
  /// 3. If an update is available:
  ///    - If [availableVersionCode] is divisible by 5, perform an immediate update.
  ///      - If user denies the update, exit the app.
  ///      - Also set [showBlankView.value = true] so the app can render a blank view.
  ///    - Otherwise, perform a flexible update and complete it.
  ///      - Do NOT exit if user denies the update.
  ///
  /// [isImmediateUpdate] is a ValueNotifier that can be listened to in your UI.
  /// For example, if you set showBlankView.value = true, your widget could render an empty screen.
  Future<void> checkForUpdate() async {
    try {
      // 1. If we are in debug mode, skip checking for updates altogether.
      if (kDebugMode) {
        AppEventBus.publish(
          CheckedForUpdateEvent(type: AppUpdateType.noUpdate),
        );
        return;
      }

      // 2. Retrieve update info from the Play Store
      final info = await InAppUpdate.checkForUpdate();

      // If no update is available, just return.
      if (info.updateAvailability != UpdateAvailability.updateAvailable) {
        AppEventBus.publish(
          CheckedForUpdateEvent(type: AppUpdateType.noUpdate),
        );
        return;
      }

      // 3. There is an update available. Check the version code.
      final versionCode = info.availableVersionCode ?? 0;

      // If versionCode is divisible by 5 => immediate update
      if (versionCode % 5 == 0) {
        // Notify UI to render a blank view (if desired).
        AppEventBus.publish(
          CheckedForUpdateEvent(type: AppUpdateType.immediate),
        );

        final result = await InAppUpdate.performImmediateUpdate();

        // If user denies the update => exit the app
        if (result == AppUpdateResult.userDeniedUpdate) {
          exit(0);
        }
      } else {
        // Otherwise => flexible update
        await InAppUpdate.startFlexibleUpdate();
        AppEventBus.publish(
          CheckedForUpdateEvent(type: AppUpdateType.flexible),
        );

        // After downloading, we should complete the update.
        // If the user denies it here, do NOT exit the app.
        await InAppUpdate.completeFlexibleUpdate();
      }
    } catch (e) {
      // Handle errors (network issues, package errors, etc.)
      // You might want to show a dialog, log the error, etc.
      print('Error while checking/updating: $e');
      AppEventBus.publish(CheckedForUpdateEvent(type: AppUpdateType.noUpdate));
    }
  }

  /// (Optional) If you ever need to complete a flexible update manually,
  /// you can call this method. It does not exit the app if user denies.
  Future<void> completeFlexibleUpdate() async {
    try {
      await InAppUpdate.completeFlexibleUpdate();
    } catch (e) {
      print('Error completing flexible update: $e');
    }
  }
}
