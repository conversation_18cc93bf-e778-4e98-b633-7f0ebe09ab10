import 'package:app_core/core.dart' as core;
import 'package:app_core/core.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:share_to/share_to.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../common/di/di.dart';
import '../../navigation/routes/app_router.dart';
import '../../navigation/routes/app_router.gr.dart';
import '../../ui/page/home/<USER>';
import '../../ui/page/share_to/share_to_page.dart';

class DeepLinkHandler {
  static final TAG = 'DeepLinkHandler';
  final AppRouter appRouter;
  static bool _isBottomSheetShowing = false;

  DeepLinkHandler(this.appRouter);

  void handleDeepLink(Uri uri) {
    if (!_validateIsLogin()) {
      getIt<PendingIntentHandler>().uri = uri;
      return;
    }

    _popToHomeIfNeeded();

    if (DeepLinkUtils.isInviteLink(uri.toString())) {
      _handleInviteLink(uri.toString());
      return;
    }

    if (DeepLinkUtils.isQrAuthLink(uri.toString())) {
      _handleQrAuthLink(uri.toString());
      return;
    }

    if (DeepLinkUtils.isSendLink(uri.toString())) {
      _handleSendLink(uri.toString());
      return;
    }

    if (DeepLinkUtils.isConnectLink(uri.toString())) {
      _handleConnectLink(uri.toString());
      return;
    }
  }

  void _popToHomeIfNeeded() {
    if (!existRouteOnBackStack(HomeRoute.name)) {
      GetIt.instance.get<AppRouter>().push(HomeRoute());
      return;
    }

    if (isCurrentlyInChannelListView()) return;

    GetIt.instance.get<AppRouter>().popUntilRouteWithName(HomeRoute.name);
  }

  void _popToAuthIfNeeded() {
    if (!existRouteOnBackStack(AuthRoute.name)) {
      GetIt.instance.get<AppRouter>().push(AuthRoute());
      return;
    }

    GetIt.instance.get<AppRouter>().popUntilRouteWithName(AuthRoute.name);
  }

  bool _validateIsLogin() {
    if (core.Config.getInstance().activeSessionKey == null) {
      _popToAuthIfNeeded();
      return false;
    }
    return true;
  }

  void _handleInviteLink(String link) {
    AppEventBus.publish(OnInvitationClickedEvent(invitationLink: link));
  }

  void _handleQrAuthLink(String link) {
    GetIt.instance.get<AppEventBus>().fire(
          HandleLoginQREvent(
            id: UniqueKey().toString(),
            qrValue: link,
          ),
        );
  }

  void _handleSendLink(String link) {
    if (!DeepLinkUtils.isSendLink(link)) {
      return;
    }

    if (!isValidSendLink(link)) {
      showInvalidSendUrlDialog();
      return;
    }

    ShareToFile shareToFile = ShareToFile(
      listMediaFile: [
        SharedMediaFile(
          path: getSendLinkData(link),
          type: SharedMediaType.url,
        ),
      ],
    );

    _popToHomeIfNeeded();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_isBottomSheetShowing) return;

      _isBottomSheetShowing = true;

      BottomSheetUtil.showDefaultBottomSheet(
        context: GetIt.instance.get<AppRouter>().navigatorKey.currentContext!,
        child: ShareToPage(
          sharedFiles: shareToFile,
          title: GetIt.instance.get<AppLocalizations>().sendTo,
          action: GetIt.instance.get<AppLocalizations>().send,
        ),
        onClose: _onCloseBottomSheet,
      );
    });
  }

  void _handleConnectLink(String link) async {
    final userId = await _handleScanToConnectLink(link);
    if (userId == null) {
      _showInvalidQRDialog();
      return;
    }

    if (userId == core.Config.getInstance().activeSessionKey) {
      _navigateToMeProfile();
      return;
    }

    _navigateToUserProfile(userId);
  }

  Future<String?> _handleScanToConnectLink(String link) async {
    final output = await GetIt.I
        .get<DecodeUserConnectLinkUseCase>()
        .execute(DecodeUserConnectLinkInput(link));
    return output.userId;
  }

  Future<void> _navigateToUserProfile(String userId) async {
    await appRouter.push(UserProfileRoute(userId: userId));
  }

  void _navigateToMeProfile() {
    AppEventBus.publish(OnGoToHomeEvent(tab: HomeTab.profile));
  }

  Future<void> _showInvalidQRDialog() async {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await DialogUtils.showAppFutureDialog(
        context: appRouter.navigatorKey.currentContext!,
        buildDialogContent: (BuildContext dialogContext) {
          return AppCustomDialog(
            maxWidth: 350.w,
            title: GetIt.I.get<AppLocalizations>().invalidQRCode,
            firstAction: GetIt.I.get<AppLocalizations>().ok,
            onFirstAction: () {
              appRouter.maybePop();
            },
          );
        },
      );
    });
  }

  bool isValidSendLink(String link) {
    if (!link.startsWith(EnvConfig.getSendLink)) return false;

    String code = link.substring(
      link.indexOf(EnvConfig.getSendLink) + EnvConfig.getSendLink.length,
    );

    if (code.isEmpty) return false;

    String codeData = decodeBase64ToString(code);

    if (codeData.isEmpty || codeData.length > GlobalConfig.maxLengthMessage)
      return false;

    return true;
  }

  String getSendLinkData(String link) {
    if (!link.startsWith(EnvConfig.getSendLink)) return '';

    String code = link.substring(
      link.indexOf(EnvConfig.getSendLink) + EnvConfig.getSendLink.length,
    );

    if (code.isEmpty) return code;

    String codeData = decodeBase64ToString(code);

    if (codeData.isEmpty || codeData.length > GlobalConfig.maxLengthMessage)
      return '';

    return codeData;
  }

  void showInvalidSendUrlDialog() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      DialogUtils.showErrorSharingLinkDialog(
        appRouter.navigatorKey.currentContext!,
        onOkClicked: () {
          popToHome();
        },
      );
    });
  }

  bool existRouteOnBackStack(String name) {
    final routerLength = appRouter.stack.length;

    if (routerLength > 0) {
      for (int i = 0; i < appRouter.stack.length; i++) {
        var page = appRouter.stack[i];
        if (page.routeData.name == name) {
          return true;
        }
      }
    }
    return false;
  }

  bool isCurrentlyInChannelListView() {
    final routerLength = appRouter.stack.length;

    if (routerLength > 0) {
      final routePage = appRouter.stack.last;
      return routePage.routeData.name == HomeRoute.name &&
          HomePage.CURRENT_INDEX == HomePage.CHANNEL_LIST_INDEX;
    }
    return false;
  }

  void _onCloseBottomSheet() {
    _isBottomSheetShowing = false;
  }

  void popToHome() {
    GetIt.instance.get<AppRouter>().popUntilRouteWithName(HomeRoute.name);
  }
}
