import 'dart:async';

import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';

class VisitedProfileHandler {
  VisitedProfileHandler();

  late StreamSubscription? _userInfoSubscription;

  void setupVisitedProfileHandler() {
    _userInfoSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<HasNotificationEvent>()
        .listen(_onReceivedFromUserInfoEvent);
  }

  void dispose() {
    _userInfoSubscription?.cancel();
  }

  void _onReceivedFromUserInfoEvent(event) async {
    if (event is HasNotificationEvent) {}
  }
}
