import apns_notification
import Flutter
import GoogleMaps
import UIKit
import workmanager

@main
@objc class AppDelegate: FlutterAppDelegate {
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        GMSServices.provideAPIKey("AIzaSyDPZjtmxaoeTvSEon3o8m2nFXyawC1_bYM")
        GeneratedPluginRegistrant.register(with: self)

        // Foreground service
        SwiftFlutterForegroundTaskPlugin.setPluginRegistrantCallback { registry in
          GeneratedPluginRegistrant.register(with: registry)
        }
//        if #available(iOS 10.0, *) {
//          UNUserNotificationCenter.current().delegate = self
//        }

        /// worker conflict display apns notification
//        UNUserNotificationCenter.current().delegate = self

        /// check open app from notification when kil app
        if launchOptions?[.remoteNotification] != nil {
            if let notification = launchOptions?[.remoteNotification] as? [String: Any] {
                ApnsNotificationPlugin.addDataPendingNotification(data: notification)
            }
        }
        
        WorkmanagerPlugin.setPluginRegistrantCallback { registry in
            GeneratedPluginRegistrant.register(with: registry)
        }

        WorkmanagerPlugin.registerBGProcessingTask(withIdentifier: "sendMessage")
//            WorkmanagerPlugin.registerPeriodicTask(withIdentifier: "be.tramckrijte.workmanagerExample.iOSBackgroundAppRefresh", frequency: NSNumber(value: 20 * 60))
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }

    /// worker conflict display apns notification
    override func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        super.userNotificationCenter(center, willPresent: notification, withCompletionHandler: completionHandler)
    }
}
