# mobile

## Run for IOS

`flutter run --dart-define=FLAVOR=beta`

## Run for Android

`flutter run --flavor beta --dart-define=FLAVOR=sandbox`

## Build for IOS

`flutter build ipa --dart-define=FLAVOR=beta`

## Build for Android

`flutter build appbundle --flavor beta --dart-define=FLAVOR=beta`

## Fix bug android java version

### Android required java version 17

- Step 1: install java 17

  `brew install openjdk@17`

- Step 2: modify `~.zshrc` add `export JAVA_HOME="$(/usr/libexec/java_home -v 17)"`

- Step 3: run this command to change flutter config jdk to java 17
  `flutter config --jdk-dir=$JAVA_HOME`